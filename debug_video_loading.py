#!/usr/bin/env python3
"""
调试视频加载问题
Debug Video Loading Issue
"""

import sys
import os
import tempfile
import cv2
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_video(file_path: str, duration_seconds: int = 3, fps: int = 30):
    """创建测试视频文件"""
    try:
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(file_path, fourcc, fps, (640, 480))
        
        total_frames = duration_seconds * fps
        
        for i in range(total_frames):
            # 创建彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 根据帧号改变颜色
            color_intensity = int((i / total_frames) * 255)
            frame[:, :] = [color_intensity, 100, 255 - color_intensity]
            
            # 添加帧号文本
            cv2.putText(frame, f"Frame {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 测试视频创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频创建失败: {e}")
        return False

def debug_main_window_state():
    """调试主窗口状态"""
    try:
        print("=== 调试主窗口视频加载状态 ===")
        
        # 模拟主窗口的完整初始化
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        print(f"主窗口初始化完成")
        print(f"初始状态:")
        print(f"  - current_video_a_path: {main_window.current_video_a_path}")
        print(f"  - current_video_b_path: {main_window.current_video_b_path}")
        print(f"  - video_a_loader存在: {hasattr(main_window, 'video_a_loader')}")
        print(f"  - video_b_loader存在: {hasattr(main_window, 'video_b_loader')}")
        print(f"  - video_a_player存在: {hasattr(main_window, 'video_a_player')}")
        print(f"  - video_b_player存在: {hasattr(main_window, 'video_b_player')}")
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_b_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        if not create_test_video(test_video_b_path, 3, 30):
            return False
        
        try:
            print(f"\n=== 测试A视频加载 ===")
            print(f"加载路径: {test_video_a_path}")
            
            # 调用加载方法
            main_window.load_video_a_file(test_video_a_path)
            
            print(f"加载后状态:")
            print(f"  - current_video_a_path: {main_window.current_video_a_path}")
            print(f"  - video_a_loader.is_loaded(): {main_window.video_a_loader.is_loaded()}")
            if hasattr(main_window, 'video_a_player'):
                print(f"  - video_a_player.is_video_loaded(): {main_window.video_a_player.is_video_loaded()}")
                print(f"  - video_a_player.get_video_path(): {main_window.video_a_player.get_video_path()}")
            
            print(f"\n=== 测试B视频加载 ===")
            print(f"加载路径: {test_video_b_path}")
            
            # 调用加载方法
            main_window.load_video_b_file(test_video_b_path)
            
            print(f"加载后状态:")
            print(f"  - current_video_b_path: {main_window.current_video_b_path}")
            print(f"  - video_b_loader.is_loaded(): {main_window.video_b_loader.is_loaded()}")
            if hasattr(main_window, 'video_b_player'):
                print(f"  - video_b_player.is_video_loaded(): {main_window.video_b_player.is_video_loaded()}")
                print(f"  - video_b_player.get_video_path(): {main_window.video_b_player.get_video_path()}")
            
            print(f"\n=== 测试融合状态检查 ===")
            
            # 检查融合条件
            can_start_fusion = (bool(main_window.current_video_a_path) and
                               bool(main_window.current_video_b_path) and
                               not main_window.is_fusion_running)
            
            print(f"融合条件检查:")
            print(f"  - bool(current_video_a_path): {bool(main_window.current_video_a_path)}")
            print(f"  - bool(current_video_b_path): {bool(main_window.current_video_b_path)}")
            print(f"  - not is_fusion_running: {not main_window.is_fusion_running}")
            print(f"  - 可以开始融合: {can_start_fusion}")
            
            # 测试实际的方法调用
            print(f"\n=== 测试方法调用 ===")
            
            # 模拟start_fusion方法的检查逻辑
            print("模拟start_fusion检查:")
            if not main_window.current_video_a_path or not main_window.current_video_b_path:
                print("❌ start_fusion会显示警告: 请先加载A视频和B视频")
                print(f"  - current_video_a_path: '{main_window.current_video_a_path}'")
                print(f"  - current_video_b_path: '{main_window.current_video_b_path}'")
            else:
                print("✅ start_fusion检查通过")
            
            # 模拟generate_preview方法的检查逻辑
            print("模拟generate_preview检查:")
            if not main_window.current_video_a_path or not main_window.current_video_b_path:
                print("❌ generate_preview会显示警告: 请先加载A视频和B视频")
                print(f"  - current_video_a_path: '{main_window.current_video_a_path}'")
                print(f"  - current_video_b_path: '{main_window.current_video_b_path}'")
            else:
                print("✅ generate_preview检查通过")
            
            return can_start_fusion
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
                os.unlink(test_video_b_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_video_player_loading():
    """调试VideoPlayer加载"""
    try:
        print("\n=== 调试VideoPlayer加载 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.video_player import VideoPlayer
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建VideoPlayer
        player = VideoPlayer("调试播放器")
        
        # 创建测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        if not create_test_video(test_video_path, 2, 30):
            return False
        
        try:
            print(f"测试VideoPlayer加载: {test_video_path}")
            
            # 测试加载
            result = player.load_video(test_video_path)
            
            print(f"加载结果: {result}")
            print(f"is_video_loaded(): {player.is_video_loaded()}")
            print(f"get_video_path(): {player.get_video_path()}")
            print(f"get_total_frames(): {player.get_total_frames()}")
            
            return result
            
        finally:
            try:
                os.unlink(test_video_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ VideoPlayer调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("=== 视频加载问题深度调试 ===\n")
    
    # 首先测试VideoPlayer
    player_result = debug_video_player_loading()
    print(f"VideoPlayer测试结果: {player_result}\n")
    
    # 然后测试主窗口
    main_window_result = debug_main_window_state()
    print(f"\n主窗口测试结果: {main_window_result}")
    
    if main_window_result:
        print("\n🎉 调试完成：视频加载状态正常，应该可以进行融合")
    else:
        print("\n❌ 调试完成：发现问题，需要进一步修复")
    
    return main_window_result

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
