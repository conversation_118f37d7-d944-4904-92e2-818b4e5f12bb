#!/usr/bin/env python3
"""
测试视频播放控制功能
Test Video Player Controls
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QHBoxLayout
from PyQt5.QtCore import Qt

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.gui.video_player import VideoPlayer
from src.gui.preview_window import PreviewWindow


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("视频播放控制测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QHBoxLayout(central_widget)
        
        # 创建两个视频播放器
        self.video_player_a = VideoPlayer("测试视频A")
        self.video_player_b = VideoPlayer("测试视频B")
        
        layout.addWidget(self.video_player_a)
        layout.addWidget(self.video_player_b)
        
        # 连接信号
        self.video_player_a.position_changed.connect(self.on_video_a_position_changed)
        self.video_player_a.play_state_changed.connect(self.on_video_a_play_state_changed)
        
        self.video_player_b.position_changed.connect(self.on_video_b_position_changed)
        self.video_player_b.play_state_changed.connect(self.on_video_b_play_state_changed)
        
        # 创建预览窗口
        self.preview_window = PreviewWindow(self)
        
        print("测试窗口初始化完成")
        print("请使用以下功能进行测试：")
        print("1. 点击'加载视频'按钮加载视频文件")
        print("2. 使用播放/暂停按钮控制播放")
        print("3. 拖拽进度条跳转到指定位置")
        print("4. 使用停止按钮停止播放")
        print("5. 从菜单栏打开预览窗口测试预览视频播放")
        
        # 添加菜单栏
        self.create_menu()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 窗口菜单
        window_menu = menubar.addMenu('窗口')
        
        # 预览窗口
        preview_action = window_menu.addAction('预览窗口')
        preview_action.triggered.connect(self.show_preview_window)
    
    def show_preview_window(self):
        """显示预览窗口"""
        self.preview_window.show()
        self.preview_window.raise_()
        self.preview_window.activateWindow()
    
    def on_video_a_position_changed(self, frame_number: int):
        """视频A播放位置改变"""
        print(f"视频A播放位置: 第{frame_number}帧")
    
    def on_video_a_play_state_changed(self, is_playing: bool):
        """视频A播放状态改变"""
        state = "播放" if is_playing else "暂停"
        print(f"视频A播放状态: {state}")
    
    def on_video_b_position_changed(self, frame_number: int):
        """视频B播放位置改变"""
        print(f"视频B播放位置: 第{frame_number}帧")
    
    def on_video_b_play_state_changed(self, is_playing: bool):
        """视频B播放状态改变"""
        state = "播放" if is_playing else "暂停"
        print(f"视频B播放状态: {state}")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    print("视频播放控制测试应用程序启动成功")
    print("请测试以下功能：")
    print("- 视频加载")
    print("- 播放/暂停控制")
    print("- 进度条拖拽")
    print("- 停止功能")
    print("- 预览窗口视频播放")
    
    sys.exit(app.exec_())


if __name__ == '__main__':
    main()
