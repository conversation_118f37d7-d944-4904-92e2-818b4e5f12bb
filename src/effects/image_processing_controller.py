"""
图像处理控制器
Image Processing Controller
"""

import cv2
import numpy as np
from typing import List, Optional, Dict, Any

from ..utils.logger import Logger
from .edge_detector import EdgeDetector, EdgeDetectionMethod
from .histogram_matcher import HistogramMatcher
from .image_processor import ImageProcessor, FilterType


class ImageProcessingController:
    """图像处理控制器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.edge_detector = EdgeDetector()
        self.histogram_matcher = HistogramMatcher()
        self.image_processor = ImageProcessor()
        self.logger.info("图像处理控制器初始化完成")
    
    def process_frame_with_control(self, frame: np.ndarray, 
                                 image_processing_control,
                                 reference_frame: Optional[np.ndarray] = None) -> np.ndarray:
        """使用图像处理控制参数处理帧
        
        Args:
            frame: 输入帧
            image_processing_control: 图像处理控制参数
            reference_frame: 参考帧（用于直方图匹配）
        
        Returns:
            处理后的帧
        """
        try:
            if not image_processing_control.enable_preprocessing:
                return frame.copy()
            
            result_frame = frame.copy()
            
            # 按顺序应用预处理方法
            for method in image_processing_control.preprocessing_methods:
                if method == "edge_detection":
                    result_frame = self._apply_edge_detection(
                        result_frame, image_processing_control
                    )
                elif method == "histogram_equalization":
                    result_frame = self._apply_histogram_equalization(result_frame)
                elif method == "histogram_matching":
                    if reference_frame is not None:
                        result_frame = self._apply_histogram_matching(
                            result_frame, reference_frame
                        )
                elif method == "gamma_correction":
                    result_frame = self._apply_gamma_correction(
                        result_frame, image_processing_control.gamma_correction
                    )
                elif method == "gaussian_blur":
                    result_frame = self._apply_gaussian_blur(result_frame)
                elif method == "sharpen":
                    result_frame = self._apply_sharpen(result_frame)
                elif method == "noise_reduction":
                    result_frame = self._apply_noise_reduction(result_frame)
                else:
                    self.logger.warning(f"未知的预处理方法: {method}")
            
            self.logger.debug(f"图像预处理完成，应用了 {len(image_processing_control.preprocessing_methods)} 种方法")
            return result_frame
            
        except Exception as e:
            self.logger.error(f"图像处理控制失败: {e}")
            return frame.copy()
    
    def _apply_edge_detection(self, frame: np.ndarray, 
                            image_processing_control) -> np.ndarray:
        """应用边缘检测"""
        try:
            method_name = image_processing_control.edge_detection_method.lower()
            
            if method_name == "canny":
                edges = self.edge_detector.detect_edges_canny(
                    frame,
                    low_threshold=image_processing_control.edge_low_threshold,
                    high_threshold=image_processing_control.edge_high_threshold
                )
            elif method_name == "sobel":
                edges = self.edge_detector.detect_edges_sobel(frame)
            elif method_name == "laplacian":
                edges = self.edge_detector.detect_edges_laplacian(frame)
            elif method_name == "scharr":
                edges = self.edge_detector.detect_edges_scharr(frame)
            else:
                self.logger.warning(f"未知的边缘检测方法: {method_name}，使用Canny")
                edges = self.edge_detector.detect_edges_canny(
                    frame,
                    low_threshold=image_processing_control.edge_low_threshold,
                    high_threshold=image_processing_control.edge_high_threshold
                )
            
            # 将边缘图转换为三通道图像
            if len(edges.shape) == 2:
                edges = cv2.cvtColor(edges, cv2.COLOR_GRAY2BGR)
            
            return edges
            
        except Exception as e:
            self.logger.error(f"边缘检测失败: {e}")
            return frame.copy()
    
    def _apply_histogram_equalization(self, frame: np.ndarray) -> np.ndarray:
        """应用直方图均衡化"""
        try:
            return self.histogram_matcher.equalize_histogram(frame)
        except Exception as e:
            self.logger.error(f"直方图均衡化失败: {e}")
            return frame.copy()
    
    def _apply_histogram_matching(self, frame: np.ndarray, 
                                reference_frame: np.ndarray) -> np.ndarray:
        """应用直方图匹配"""
        try:
            return self.histogram_matcher.match_histograms(frame, reference_frame)
        except Exception as e:
            self.logger.error(f"直方图匹配失败: {e}")
            return frame.copy()
    
    def _apply_gamma_correction(self, frame: np.ndarray, gamma: float) -> np.ndarray:
        """应用Gamma校正"""
        try:
            if gamma <= 0:
                gamma = 1.0
            
            # 构建查找表
            inv_gamma = 1.0 / gamma
            table = np.array([((i / 255.0) ** inv_gamma) * 255 
                            for i in np.arange(0, 256)]).astype("uint8")
            
            # 应用Gamma校正
            corrected = cv2.LUT(frame, table)
            
            self.logger.debug(f"Gamma校正完成，gamma值: {gamma}")
            return corrected
            
        except Exception as e:
            self.logger.error(f"Gamma校正失败: {e}")
            return frame.copy()
    
    def _apply_gaussian_blur(self, frame: np.ndarray, kernel_size: int = 5) -> np.ndarray:
        """应用高斯模糊"""
        try:
            if kernel_size % 2 == 0:
                kernel_size += 1
            
            blurred = cv2.GaussianBlur(frame, (kernel_size, kernel_size), 0)
            self.logger.debug(f"高斯模糊完成，核大小: {kernel_size}")
            return blurred
            
        except Exception as e:
            self.logger.error(f"高斯模糊失败: {e}")
            return frame.copy()
    
    def _apply_sharpen(self, frame: np.ndarray) -> np.ndarray:
        """应用锐化"""
        try:
            return self.image_processor.apply_filter(frame, FilterType.SHARPEN)
        except Exception as e:
            self.logger.error(f"锐化失败: {e}")
            return frame.copy()
    
    def _apply_noise_reduction(self, frame: np.ndarray) -> np.ndarray:
        """应用降噪"""
        try:
            return self.image_processor.apply_filter(frame, FilterType.NOISE_REDUCTION)
        except Exception as e:
            self.logger.error(f"降噪失败: {e}")
            return frame.copy()
    
    def apply_fusion_method(self, background: np.ndarray, foreground: np.ndarray,
                          image_processing_control, alpha: float = 0.5) -> np.ndarray:
        """应用融合方法
        
        Args:
            background: 背景图像
            foreground: 前景图像
            image_processing_control: 图像处理控制参数
            alpha: 混合权重
        
        Returns:
            融合后的图像
        """
        try:
            fusion_method = image_processing_control.fusion_method.lower()
            blend_weight = image_processing_control.blend_weight
            feather_radius = image_processing_control.feather_radius
            
            if fusion_method == "alpha_blend":
                # Alpha混合
                result = cv2.addWeighted(background, 1 - blend_weight, 
                                       foreground, blend_weight, 0)
                
            elif fusion_method == "normal":
                # 正常混合
                result = cv2.addWeighted(background, 1 - alpha, foreground, alpha, 0)
                
            elif fusion_method == "multiply":
                # 正片叠底
                bg_norm = background.astype(np.float32) / 255.0
                fg_norm = foreground.astype(np.float32) / 255.0
                multiplied = bg_norm * fg_norm
                result = (multiplied * 255).astype(np.uint8)
                
            elif fusion_method == "screen":
                # 滤色
                bg_norm = background.astype(np.float32) / 255.0
                fg_norm = foreground.astype(np.float32) / 255.0
                screened = 1 - (1 - bg_norm) * (1 - fg_norm)
                result = (screened * 255).astype(np.uint8)
                
            elif fusion_method == "overlay":
                # 叠加
                bg_norm = background.astype(np.float32) / 255.0
                fg_norm = foreground.astype(np.float32) / 255.0
                
                mask = bg_norm < 0.5
                overlaid = np.where(mask, 2 * bg_norm * fg_norm, 
                                  1 - 2 * (1 - bg_norm) * (1 - fg_norm))
                result = (overlaid * 255).astype(np.uint8)
                
            elif fusion_method == "feather":
                # 羽化融合
                if feather_radius > 0:
                    # 创建羽化掩码
                    mask = np.ones(background.shape[:2], dtype=np.float32)
                    kernel_size = feather_radius * 2 + 1
                    mask = cv2.GaussianBlur(mask, (kernel_size, kernel_size), 
                                          feather_radius / 3.0)
                    
                    # 应用羽化掩码
                    mask = mask[:, :, np.newaxis]
                    result = (background * (1 - mask * blend_weight) + 
                            foreground * mask * blend_weight).astype(np.uint8)
                else:
                    result = cv2.addWeighted(background, 1 - blend_weight, 
                                           foreground, blend_weight, 0)
                    
            else:
                # 默认使用Alpha混合
                result = cv2.addWeighted(background, 1 - blend_weight, 
                                       foreground, blend_weight, 0)
            
            self.logger.debug(f"融合方法应用完成: {fusion_method}")
            return result
            
        except Exception as e:
            self.logger.error(f"融合方法应用失败: {e}")
            return cv2.addWeighted(background, 1 - alpha, foreground, alpha, 0)
    
    def get_available_methods(self) -> Dict[str, List[str]]:
        """获取可用的处理方法列表"""
        return {
            "preprocessing_methods": [
                "edge_detection", "histogram_equalization", "histogram_matching",
                "gamma_correction", "gaussian_blur", "sharpen", "noise_reduction"
            ],
            "edge_detection_methods": ["canny", "sobel", "laplacian", "scharr"],
            "fusion_methods": [
                "alpha_blend", "normal", "multiply", "screen", "overlay", "feather"
            ]
        }
