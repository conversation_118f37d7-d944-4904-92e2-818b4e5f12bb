"""
文字内容控制器
Text Content Controller
"""

import cv2
import numpy as np
import math
from typing import List, Tuple, Optional, Dict, Any

from ..utils.logger import Logger
from .text_overlay import TextOverlay, TextStyle, TextPosition


class TextContentController:
    """文字内容控制器类"""
    
    def __init__(self):
        self.logger = Logger.get_logger(__name__)
        self.text_overlay = TextOverlay()
        self.logger.info("文字内容控制器初始化完成")
    
    def apply_text_overlay_to_frames(self, frames: List[np.ndarray], 
                                   text_control) -> List[np.ndarray]:
        """对帧序列应用文字叠加控制
        
        Args:
            frames: 输入帧序列
            text_control: 文字内容控制参数
        
        Returns:
            应用文字叠加后的帧序列
        """
        try:
            if not text_control.enable_text_overlay or not text_control.text_content:
                return frames.copy()
            
            self.logger.info(f"开始应用文字叠加控制，帧数: {len(frames)}")
            self.logger.info(f"文字内容: '{text_control.text_content}', "
                           f"位置模式: {text_control.text_position_mode}")
            
            result_frames = []
            total_frames = len(frames)
            
            # 计算文字显示的帧范围
            appearance_interval = max(1, total_frames // text_control.appearance_frequency)
            
            for frame_index, frame in enumerate(frames):
                # 检查当前帧是否应该显示文字
                should_show_text = self._should_show_text_at_frame(
                    frame_index, total_frames, text_control
                )
                
                if should_show_text:
                    # 计算当前帧的文字位置
                    position = self._calculate_text_position(
                        text_control, frame_index, total_frames, 
                        frame.shape[1], frame.shape[0]
                    )
                    
                    # 创建文字样式
                    style = self._create_text_style_from_control(text_control, frame_index, total_frames)
                    
                    # 应用文字叠加
                    frame_with_text = self.text_overlay.add_text(frame, text_control.text_content, position, style)
                    result_frames.append(frame_with_text)
                else:
                    result_frames.append(frame.copy())
            
            self.logger.info(f"文字叠加控制应用完成，处理了 {len(result_frames)} 帧")
            return result_frames
            
        except Exception as e:
            self.logger.error(f"应用文字叠加控制失败: {e}")
            return frames.copy()
    
    def _should_show_text_at_frame(self, frame_index: int, total_frames: int, 
                                 text_control) -> bool:
        """判断当前帧是否应该显示文字"""
        try:
            # 计算显示周期
            if text_control.appearance_frequency <= 0:
                return False
            
            # 计算每次显示的间隔
            interval = max(1, total_frames // text_control.appearance_frequency)
            
            # 计算当前是第几个显示周期
            cycle_index = frame_index // interval
            
            # 在每个周期内，检查是否在连续显示帧数范围内
            frame_in_cycle = frame_index % interval
            
            return frame_in_cycle < text_control.continuous_frames
            
        except Exception as e:
            self.logger.error(f"判断文字显示失败: {e}")
            return False
    
    def _calculate_text_position(self, text_control, frame_index: int, 
                               total_frames: int, frame_width: int, 
                               frame_height: int) -> Tuple[int, int]:
        """计算文字位置"""
        try:
            from ..fusion.fusion_engine import MotionTrajectory
            
            if text_control.text_position_mode == "static":
                # 静态位置
                x = int(text_control.static_position_x * frame_width)
                y = int(text_control.static_position_y * frame_height)
                return (x, y)
            
            else:
                # 动态位置
                trajectory = text_control.text_motion_trajectory
                speed = text_control.text_motion_speed
                
                if trajectory == MotionTrajectory.HORIZONTAL_SCROLL:
                    # 水平滚动
                    progress = (frame_index * speed / total_frames) % 1.0
                    x = int(progress * frame_width)
                    y = int(text_control.static_position_y * frame_height)
                    
                elif trajectory == MotionTrajectory.VERTICAL_SCROLL:
                    # 垂直滚动
                    progress = (frame_index * speed / total_frames) % 1.0
                    x = int(text_control.static_position_x * frame_width)
                    y = int(progress * frame_height)
                    
                elif trajectory == MotionTrajectory.CIRCULAR:
                    # 圆形轨迹
                    center_x = frame_width // 2
                    center_y = frame_height // 2
                    radius = min(frame_width, frame_height) // 4
                    
                    angle = (frame_index * speed * 2 * math.pi / total_frames) % (2 * math.pi)
                    x = int(center_x + radius * math.cos(angle))
                    y = int(center_y + radius * math.sin(angle))
                    
                elif trajectory == MotionTrajectory.DIAGONAL:
                    # 对角线运动
                    progress = (frame_index * speed / total_frames) % 2.0
                    if progress <= 1.0:
                        # 从左上到右下
                        x = int(progress * frame_width)
                        y = int(progress * frame_height)
                    else:
                        # 从右下到左上
                        progress -= 1.0
                        x = int((1 - progress) * frame_width)
                        y = int((1 - progress) * frame_height)
                        
                else:
                    # 默认静态位置
                    x = int(text_control.static_position_x * frame_width)
                    y = int(text_control.static_position_y * frame_height)
                
                # 确保位置在有效范围内
                x = max(0, min(x, frame_width - 50))  # 预留50像素的文字空间
                y = max(30, min(y, frame_height - 10))  # 预留文字高度空间
                
                return (x, y)
                
        except Exception as e:
            self.logger.error(f"计算文字位置失败: {e}")
            return (50, 50)  # 返回默认位置
    
    def _create_text_style_from_control(self, text_control, frame_index: int, 
                                      total_frames: int) -> TextStyle:
        """根据控制参数创建文字样式"""
        try:
            style = TextStyle()
            
            # 设置字体
            font_map = {
                "Arial": cv2.FONT_HERSHEY_SIMPLEX,
                "Times New Roman": cv2.FONT_HERSHEY_COMPLEX,
                "Courier": cv2.FONT_HERSHEY_COMPLEX_SMALL,
                "Helvetica": cv2.FONT_HERSHEY_DUPLEX,
                "Comic Sans": cv2.FONT_HERSHEY_TRIPLEX
            }
            style.font = font_map.get(text_control.font_family, cv2.FONT_HERSHEY_SIMPLEX)
            
            # 设置字体大小（OpenCV的font_scale）
            style.font_scale = text_control.font_size / 24.0  # 基准大小24
            
            # 设置颜色和透明度
            style.color = text_control.font_color
            style.opacity = text_control.font_alpha
            
            # 设置粗体（通过thickness实现）
            style.thickness = 3 if text_control.font_bold else 2
            
            # 设置描边
            if text_control.enable_outline:
                style.border_color = text_control.outline_color
                style.border_thickness = text_control.outline_width
            
            # 设置阴影
            if text_control.enable_shadow:
                style.shadow_offset = text_control.shadow_offset
                style.shadow_color = text_control.shadow_color
            
            # 设置淡入淡出效果
            if text_control.enable_fade_effect:
                fade_duration = text_control.fade_duration
                
                # 计算当前帧在显示周期中的位置
                interval = max(1, total_frames // text_control.appearance_frequency)
                frame_in_cycle = frame_index % interval
                
                if frame_in_cycle < fade_duration:
                    # 淡入阶段
                    fade_progress = frame_in_cycle / fade_duration
                    style.opacity *= fade_progress
                elif frame_in_cycle >= text_control.continuous_frames - fade_duration:
                    # 淡出阶段
                    fade_progress = (text_control.continuous_frames - frame_in_cycle) / fade_duration
                    style.opacity *= max(0, fade_progress)
            
            return style
            
        except Exception as e:
            self.logger.error(f"创建文字样式失败: {e}")
            return TextStyle()
    
    def apply_text_overlay_to_single_frame(self, frame: np.ndarray, 
                                         text_control, frame_index: int = 0, 
                                         total_frames: int = 1) -> np.ndarray:
        """对单个帧应用文字叠加控制
        
        Args:
            frame: 输入帧
            text_control: 文字内容控制参数
            frame_index: 帧索引
            total_frames: 总帧数
        
        Returns:
            应用文字叠加后的帧
        """
        try:
            if not text_control.enable_text_overlay or not text_control.text_content:
                return frame.copy()
            
            # 计算文字位置
            position = self._calculate_text_position(
                text_control, frame_index, total_frames, 
                frame.shape[1], frame.shape[0]
            )
            
            # 创建文字样式
            style = self._create_text_style_from_control(text_control, frame_index, total_frames)
            
            # 应用文字叠加
            return self.text_overlay.add_text(frame, text_control.text_content, position, style)
            
        except Exception as e:
            self.logger.error(f"对单个帧应用文字叠加失败: {e}")
            return frame.copy()
    
    def get_text_preview(self, frame: np.ndarray, text_control) -> np.ndarray:
        """获取文字叠加预览
        
        Args:
            frame: 输入帧
            text_control: 文字内容控制参数
        
        Returns:
            文字叠加预览帧
        """
        try:
            if not text_control.enable_text_overlay or not text_control.text_content:
                return frame.copy()
            
            # 使用静态位置进行预览
            position = (
                int(text_control.static_position_x * frame.shape[1]),
                int(text_control.static_position_y * frame.shape[0])
            )
            
            # 创建预览样式
            style = self._create_text_style_from_control(text_control, 0, 1)
            
            # 应用文字叠加
            return self.text_overlay.add_text(frame, text_control.text_content, position, style)
            
        except Exception as e:
            self.logger.error(f"获取文字叠加预览失败: {e}")
            return frame.copy()
    
    def get_available_fonts(self) -> List[str]:
        """获取可用字体列表"""
        return ["Arial", "Times New Roman", "Courier", "Helvetica", "Comic Sans"]
    
    def get_available_trajectories(self) -> List[str]:
        """获取可用运动轨迹列表"""
        from ..fusion.fusion_engine import MotionTrajectory
        return [trajectory.value for trajectory in MotionTrajectory]
