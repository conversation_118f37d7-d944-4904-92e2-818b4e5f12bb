"""
视频处理工具类
Video Processing Utilities
"""

import cv2
import numpy as np
from typing import Optional, Tuple, Any
from PyQt5.QtGui import QPixmap, QImage
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QLabel

from .logger import Logger


class VideoFrameConverter:
    """视频帧转换工具类"""
    
    @staticmethod
    def opencv_to_qpixmap(frame: np.ndarray) -> Optional[QPixmap]:
        """将OpenCV帧转换为QPixmap"""
        try:
            if frame is None or frame.size == 0:
                return None
                
            height, width, channel = frame.shape
            bytes_per_line = 3 * width
            q_image = QImage(frame.data, width, height, bytes_per_line, QImage.Format_RGB888).rgbSwapped()
            
            return QPixmap.fromImage(q_image)
            
        except Exception as e:
            Logger.get_logger(__name__).error(f"帧转换失败: {e}")
            return None
    
    @staticmethod
    def display_frame_in_label(frame: np.ndarray, label: QLabel, 
                              keep_aspect_ratio: bool = True) -> bool:
        """在QLabel中显示帧"""
        try:
            pixmap = VideoFrameConverter.opencv_to_qpixmap(frame)
            if pixmap is None:
                return False
            
            if keep_aspect_ratio:
                # 缩放图像以适应标签大小
                scaled_pixmap = pixmap.scaled(
                    label.size(), 
                    Qt.KeepAspectRatio, 
                    Qt.SmoothTransformation
                )
                label.setPixmap(scaled_pixmap)
            else:
                label.setPixmap(pixmap)
            
            return True
            
        except Exception as e:
            Logger.get_logger(__name__).error(f"显示帧失败: {e}")
            return False


class VideoLoadValidator:
    """视频加载验证工具类"""
    
    @staticmethod
    def validate_video_path(video_path: str) -> bool:
        """验证视频路径"""
        try:
            from pathlib import Path
            from ..utils.file_utils import FileUtils
            
            # 检查文件是否存在
            if not Path(video_path).exists():
                return False
            
            # 检查是否为视频文件
            if not FileUtils.is_video_file(video_path):
                return False
                
            return True
            
        except Exception:
            return False
    
    @staticmethod
    def test_video_open(video_path: str) -> bool:
        """测试视频是否可以打开"""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return False
            
            # 尝试读取第一帧
            ret, frame = cap.read()
            cap.release()
            
            return ret and frame is not None
            
        except Exception:
            return False


class PlaybackStateManager:
    """播放状态管理器"""

    def __init__(self, logger_name: str = __name__):
        self.logger = Logger.get_logger(logger_name)
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.video_path = None
        self.fps = 30.0
        self.duration = 0.0
        self.state_change_callbacks = []  # 状态变化回调

    def add_state_change_callback(self, callback):
        """添加状态变化回调"""
        if callback not in self.state_change_callbacks:
            self.state_change_callbacks.append(callback)

    def remove_state_change_callback(self, callback):
        """移除状态变化回调"""
        if callback in self.state_change_callbacks:
            self.state_change_callbacks.remove(callback)

    def _notify_state_change(self, change_type: str, **kwargs):
        """通知状态变化"""
        for callback in self.state_change_callbacks:
            try:
                callback(change_type, **kwargs)
            except Exception as e:
                self.logger.error(f"状态变化回调失败: {e}")

    def reset(self):
        """重置状态"""
        old_playing = self.is_playing
        self.is_playing = False
        self.current_frame = 0
        self.total_frames = 0
        self.video_path = None
        self.fps = 30.0
        self.duration = 0.0

        if old_playing:
            self._notify_state_change("playback_stopped")

    def set_video_info(self, video_path: str, total_frames: int, fps: float):
        """设置视频信息"""
        self.video_path = video_path
        self.total_frames = total_frames
        self.fps = fps
        self.current_frame = 0
        self.duration = total_frames / fps if fps > 0 else 0.0

        self._notify_state_change("video_loaded",
                                 video_path=video_path,
                                 total_frames=total_frames,
                                 fps=fps)

    def set_playing(self, playing: bool):
        """设置播放状态"""
        old_state = self.is_playing
        self.is_playing = playing

        if old_state != playing:
            state_text = "播放" if playing else "暂停"
            self.logger.info(f"播放状态变更: {state_text}")

            change_type = "playback_started" if playing else "playback_paused"
            self._notify_state_change(change_type, is_playing=playing)

    def set_position(self, frame_number: int):
        """设置播放位置"""
        old_frame = self.current_frame

        if 0 <= frame_number < self.total_frames:
            self.current_frame = frame_number

            if old_frame != frame_number:
                self._notify_state_change("position_changed",
                                        frame_number=frame_number,
                                        time_seconds=self.get_current_time())
        else:
            self.logger.warning(f"帧号超出范围: {frame_number}")

    def next_frame(self) -> bool:
        """移动到下一帧"""
        if self.current_frame < self.total_frames - 1:
            self.set_position(self.current_frame + 1)
            return True
        else:
            # 播放完成
            old_playing = self.is_playing
            self.is_playing = False

            if old_playing:
                self._notify_state_change("playback_finished")
            return False

    def get_frame_delay_ms(self) -> int:
        """获取帧延迟（毫秒）"""
        if self.fps > 0:
            return int(1000 / self.fps)
        else:
            return 33  # 默认30fps

    def get_current_time(self) -> float:
        """获取当前播放时间（秒）"""
        if self.fps > 0:
            return self.current_frame / self.fps
        return 0.0

    def get_progress_percentage(self) -> float:
        """获取播放进度百分比"""
        if self.total_frames > 0:
            return (self.current_frame / self.total_frames) * 100
        return 0.0

    def is_valid(self) -> bool:
        """检查状态是否有效"""
        return (self.video_path is not None and
                self.total_frames > 0 and
                self.fps > 0)

    def get_state_info(self) -> dict:
        """获取状态信息"""
        return {
            'video_path': self.video_path,
            'is_playing': self.is_playing,
            'current_frame': self.current_frame,
            'total_frames': self.total_frames,
            'fps': self.fps,
            'duration': self.duration,
            'current_time': self.get_current_time(),
            'progress_percentage': self.get_progress_percentage(),
            'is_valid': self.is_valid()
        }


class VideoErrorHandler:
    """视频处理错误处理器"""

    def __init__(self, logger_name: str = __name__):
        self.logger = Logger.get_logger(logger_name)
        self.error_count = 0
        self.last_errors = []  # 保存最近的错误
        self.max_error_history = 10

    def _log_error(self, error_type: str, context: str, error: Exception):
        """记录错误"""
        self.error_count += 1
        error_msg = f"{error_type} [{context}]: {error}"
        self.logger.error(error_msg)

        # 保存到错误历史
        self.last_errors.append({
            'type': error_type,
            'context': context,
            'error': str(error),
            'timestamp': self._get_timestamp()
        })

        # 限制历史记录数量
        if len(self.last_errors) > self.max_error_history:
            self.last_errors.pop(0)

    def _get_timestamp(self) -> str:
        """获取时间戳"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def handle_video_load_error(self, video_path: str, error: Exception) -> bool:
        """处理视频加载错误"""
        self._log_error("视频加载失败", video_path, error)
        return False

    def handle_frame_error(self, frame_number: int, error: Exception) -> Optional[np.ndarray]:
        """处理帧处理错误"""
        self._log_error("帧处理失败", f"帧号: {frame_number}", error)
        return None

    def handle_playback_error(self, operation: str, error: Exception) -> bool:
        """处理播放错误"""
        self._log_error("播放操作失败", operation, error)
        return False

    def handle_ui_error(self, component: str, error: Exception) -> bool:
        """处理UI错误"""
        self._log_error("UI组件错误", component, error)
        return False

    def get_error_summary(self) -> dict:
        """获取错误摘要"""
        return {
            'total_errors': self.error_count,
            'recent_errors': self.last_errors.copy()
        }

    def clear_error_history(self):
        """清除错误历史"""
        self.error_count = 0
        self.last_errors.clear()
        self.logger.info("错误历史已清除")


class VideoUIHelper:
    """视频UI辅助工具"""
    
    @staticmethod
    def update_play_button_text(button, is_playing: bool):
        """更新播放按钮文本"""
        try:
            button.setText("暂停" if is_playing else "播放")
        except Exception as e:
            Logger.get_logger(__name__).error(f"更新播放按钮失败: {e}")
    
    @staticmethod
    def update_progress_slider(slider, current_frame: int, total_frames: int, 
                              user_dragging: bool = False):
        """更新进度条"""
        try:
            if not user_dragging:
                slider.setMaximum(max(0, total_frames - 1))
                slider.setValue(current_frame)
        except Exception as e:
            Logger.get_logger(__name__).error(f"更新进度条失败: {e}")
    
    @staticmethod
    def enable_video_controls(play_btn, stop_btn, slider, enabled: bool):
        """启用/禁用视频控制"""
        try:
            play_btn.setEnabled(enabled)
            stop_btn.setEnabled(enabled)
            slider.setEnabled(enabled)
        except Exception as e:
            Logger.get_logger(__name__).error(f"设置控件状态失败: {e}")
    
    @staticmethod
    def format_video_info(video_path: str, width: int, height: int, 
                         fps: float, duration: float) -> str:
        """格式化视频信息"""
        try:
            import os
            filename = os.path.basename(video_path)
            return (f"文件: {filename}\n"
                   f"分辨率: {width}x{height}\n"
                   f"帧率: {fps:.2f} FPS\n"
                   f"时长: {duration:.2f}秒")
        except Exception:
            return "视频信息获取失败"


class VideoOperationResult:
    """视频操作结果类"""
    
    def __init__(self, success: bool, message: str = "", data: Any = None):
        self.success = success
        self.message = message
        self.data = data
    
    def __bool__(self):
        return self.success
    
    def __str__(self):
        return f"VideoOperationResult(success={self.success}, message='{self.message}')"


class VideoProcessingHelper:
    """视频处理辅助工具"""
    
    @staticmethod
    def safe_video_operation(operation_name: str, operation_func, 
                           error_handler: VideoErrorHandler, *args, **kwargs) -> VideoOperationResult:
        """安全执行视频操作"""
        try:
            result = operation_func(*args, **kwargs)
            return VideoOperationResult(True, f"{operation_name}成功", result)
        except Exception as e:
            error_handler.handle_playback_error(operation_name, e)
            return VideoOperationResult(False, f"{operation_name}失败: {e}")
    
    @staticmethod
    def validate_frame_number(frame_number: int, total_frames: int) -> int:
        """验证并修正帧号"""
        return max(0, min(frame_number, total_frames - 1))
    
    @staticmethod
    def calculate_frame_time(frame_number: int, fps: float) -> float:
        """计算帧对应的时间（秒）"""
        if fps > 0:
            return frame_number / fps
        return 0.0
    
    @staticmethod
    def time_to_frame(time_seconds: float, fps: float) -> int:
        """时间转换为帧号"""
        if fps > 0:
            return int(time_seconds * fps)
        return 0
