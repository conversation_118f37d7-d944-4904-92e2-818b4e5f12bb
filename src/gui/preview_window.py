"""
预览窗口类
Preview Window Class
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QGroupBox,
                             QLabel, QPushButton, QTextEdit, QSlider, QTabWidget)
from PyQt5.QtCore import Qt, pyqtSignal, QTimer, pyqtSlot
from PyQt5.QtGui import QPixmap, QImage
import cv2
import numpy as np
from typing import Optional
import os

from ..utils.video_utils import (VideoFrameConverter, VideoLoadValidator,
                                PlaybackStateManager, VideoErrorHandler,
                                VideoUIHelper, VideoProcessingHelper)


class PreviewWindow(QDialog):
    """预览窗口"""

    # 信号定义
    preview_requested = pyqtSignal()
    preview_cleared = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("融合预览")
        self.setModal(False)  # 非模态窗口
        self.resize(800, 700)

        # 预览视频播放相关
        self.preview_frames = []
        self.play_timer = QTimer()
        self.play_timer.timeout.connect(self.next_frame)

        # 使用统一的状态管理器和错误处理器
        self.state_manager = PlaybackStateManager(__name__)
        self.error_handler = VideoErrorHandler(__name__)

        self.init_ui()
        
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 预览组
        preview_group = QGroupBox("融合预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.preview_label = QLabel("点击'生成预览'查看融合效果")
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumHeight(300)
        self.preview_label.setStyleSheet("border: 1px solid #ccc; background-color: #f5f5f5;")
        preview_layout.addWidget(self.preview_label)
        
        # 预览控制按钮
        preview_btn_layout = QHBoxLayout()
        self.preview_btn = QPushButton("生成预览")
        self.preview_btn.clicked.connect(self.request_preview)
        preview_btn_layout.addWidget(self.preview_btn)

        self.clear_preview_btn = QPushButton("清除预览")
        self.clear_preview_btn.clicked.connect(self.clear_preview)
        preview_btn_layout.addWidget(self.clear_preview_btn)

        self.save_preview_btn = QPushButton("保存预览")
        self.save_preview_btn.clicked.connect(self.save_preview)
        self.save_preview_btn.setEnabled(False)
        preview_btn_layout.addWidget(self.save_preview_btn)

        preview_btn_layout.addStretch()
        preview_layout.addLayout(preview_btn_layout)

        # 视频播放控制（当加载视频时显示）
        video_control_layout = QHBoxLayout()

        # 进度条
        self.progress_slider = QSlider(Qt.Horizontal)
        self.progress_slider.setMinimum(0)
        self.progress_slider.setMaximum(100)
        self.progress_slider.setValue(0)
        self.progress_slider.setEnabled(False)
        self.progress_slider.sliderPressed.connect(self.on_slider_pressed)
        self.progress_slider.sliderReleased.connect(self.on_slider_released)
        self.progress_slider.valueChanged.connect(self.on_position_changed)
        preview_layout.addWidget(self.progress_slider)

        # 播放控制按钮
        self.play_btn = QPushButton("播放")
        self.play_btn.clicked.connect(self.toggle_play)
        self.play_btn.setEnabled(False)
        video_control_layout.addWidget(self.play_btn)

        self.stop_btn = QPushButton("停止")
        self.stop_btn.clicked.connect(self.stop_video)
        self.stop_btn.setEnabled(False)
        video_control_layout.addWidget(self.stop_btn)

        self.load_video_btn = QPushButton("加载预览视频")
        self.load_video_btn.clicked.connect(self.load_preview_video)
        video_control_layout.addWidget(self.load_video_btn)

        video_control_layout.addStretch()
        preview_layout.addLayout(video_control_layout)
        
        layout.addWidget(preview_group)
        
        # 预览信息
        info_group = QGroupBox("预览信息")
        info_layout = QVBoxLayout(info_group)
        
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(100)
        self.info_text.setReadOnly(True)
        self.info_text.setStyleSheet("font-family: monospace; font-size: 9pt;")
        info_layout.addWidget(self.info_text)
        
        layout.addWidget(info_group)
        
        # 关闭按钮
        close_btn_layout = QHBoxLayout()
        close_btn_layout.addStretch()
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(self.close)
        close_btn_layout.addWidget(close_btn)
        
        layout.addLayout(close_btn_layout)

        # 用于控制进度条拖拽时的播放状态
        self.slider_pressed = False

    def request_preview(self):
        """请求生成预览"""
        self.preview_requested.emit()

    def clear_preview(self):
        """清除预览"""
        self.stop_video()  # 停止视频播放
        self.preview_label.clear()
        self.preview_label.setText("点击'生成预览'查看融合效果")
        self.save_preview_btn.setEnabled(False)
        self.info_text.clear()
        self.preview_cleared.emit()
        
    def save_preview(self):
        """保存预览图像"""
        if hasattr(self, 'current_pixmap') and self.current_pixmap:
            from PyQt5.QtWidgets import QFileDialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, "保存预览图像", "", "图像文件 (*.png *.jpg *.jpeg *.bmp)"
            )
            if file_path:
                self.current_pixmap.save(file_path)
                self.add_info(f"预览图像已保存: {file_path}")
        
    def update_preview(self, frame, frame_index=None):
        """更新预览图像"""
        try:
            if frame is not None:
                # 使用统一的帧显示方法
                if VideoFrameConverter.display_frame_in_label(frame, self.preview_label):
                    # 保存原始pixmap用于保存功能
                    self.current_pixmap = VideoFrameConverter.opencv_to_qpixmap(frame)
                    self.save_preview_btn.setEnabled(True)

                    # 更新信息
                    height, width = frame.shape[:2]
                    info = f"预览更新成功\n"
                    info += f"图像尺寸: {width}x{height}\n"
                    if frame_index is not None:
                        info += f"帧索引: {frame_index}\n"
                    self.info_text.setText(info)
                else:
                    self.error_handler.handle_ui_error("预览显示", Exception("帧显示失败"))

        except Exception as e:
            self.error_handler.handle_ui_error("预览更新", e)
            
    def add_info(self, message):
        """添加信息"""
        self.info_text.append(message)
        
    def set_preview_enabled(self, enabled):
        """设置预览按钮状态"""
        self.preview_btn.setEnabled(enabled)
        
    def load_preview_video(self):
        """加载预览视频"""
        from PyQt5.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择预览视频文件", "",
            "视频文件 (*.mp4 *.avi *.mov *.mkv *.wmv);;所有文件 (*)"
        )

        if file_path:
            self.load_video_file(file_path)

    def load_video_file(self, video_path: str):
        """加载视频文件"""
        try:
            # 验证视频路径
            if not VideoLoadValidator.validate_video_path(video_path):
                return self.error_handler.handle_video_load_error(video_path,
                    Exception("视频路径无效或文件不存在"))

            self.stop_video()  # 停止当前播放

            # 使用OpenCV加载视频
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return self.error_handler.handle_video_load_error(video_path,
                    Exception("无法打开视频文件"))

            # 获取视频信息
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            fps = cap.get(cv2.CAP_PROP_FPS)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

            # 读取所有帧
            self.preview_frames = []
            for i in range(frame_count):
                ret, frame = cap.read()
                if ret:
                    self.preview_frames.append(frame)
                else:
                    break

            cap.release()

            if self.preview_frames:
                # 更新状态管理器
                self.state_manager.set_video_info(video_path, len(self.preview_frames), fps)

                # 更新UI
                VideoUIHelper.update_progress_slider(
                    self.progress_slider, 0, len(self.preview_frames))
                VideoUIHelper.enable_video_controls(
                    self.play_btn, self.stop_btn, self.progress_slider, True)

                # 显示第一帧
                self.show_frame(0)

                # 更新信息
                info = VideoUIHelper.format_video_info(
                    video_path, width, height, fps, len(self.preview_frames) / fps)
                self.add_info(info)

                return True
            else:
                return self.error_handler.handle_video_load_error(video_path,
                    Exception("无法读取视频帧"))

        except Exception as e:
            return self.error_handler.handle_video_load_error(video_path, e)

    def show_frame(self, frame_index: int):
        """显示指定帧"""
        try:
            if 0 <= frame_index < len(self.preview_frames):
                frame = self.preview_frames[frame_index]

                # 使用统一的帧显示方法
                if VideoFrameConverter.display_frame_in_label(frame, self.preview_label):
                    self.current_pixmap = VideoFrameConverter.opencv_to_qpixmap(frame)
                    self.state_manager.set_position(frame_index)
                    self.save_preview_btn.setEnabled(True)
                else:
                    self.error_handler.handle_frame_error(frame_index,
                        Exception("帧显示失败"))
            else:
                self.error_handler.handle_frame_error(frame_index,
                    Exception("帧索引超出范围"))

        except Exception as e:
            self.error_handler.handle_frame_error(frame_index, e)

    def toggle_play(self):
        """切换播放状态"""
        if not self.preview_frames:
            return

        if self.is_playing:
            self.pause_video()
        else:
            self.play_video()

    def play_video(self):
        """播放视频"""
        if not self.preview_frames or self.state_manager.is_playing:
            return

        def _play_operation():
            self.state_manager.set_playing(True)
            VideoUIHelper.update_play_button_text(self.play_btn, True)

            # 设置定时器间隔（毫秒）
            interval = self.state_manager.get_frame_delay_ms()
            self.play_timer.start(interval)

            self.add_info("开始播放视频")
            return True

        VideoProcessingHelper.safe_video_operation(
            "播放视频", _play_operation, self.error_handler)

    def pause_video(self):
        """暂停视频"""
        if not self.state_manager.is_playing:
            return

        def _pause_operation():
            self.state_manager.set_playing(False)
            VideoUIHelper.update_play_button_text(self.play_btn, False)
            self.play_timer.stop()

            self.add_info("暂停播放视频")
            return True

        VideoProcessingHelper.safe_video_operation(
            "暂停视频", _pause_operation, self.error_handler)

    def stop_video(self):
        """停止视频"""
        def _stop_operation():
            self.state_manager.set_playing(False)
            VideoUIHelper.update_play_button_text(self.play_btn, False)
            self.play_timer.stop()

            # 重置到第一帧
            if self.preview_frames:
                self.state_manager.set_position(0)
                self.show_frame(0)
                VideoUIHelper.update_progress_slider(
                    self.progress_slider, 0, len(self.preview_frames))
            return True

        VideoProcessingHelper.safe_video_operation(
            "停止视频", _stop_operation, self.error_handler)

    @pyqtSlot()
    def next_frame(self):
        """播放下一帧"""
        if not self.preview_frames or not self.state_manager.is_playing:
            return

        if not self.state_manager.next_frame():
            # 播放完成，重置到开始
            self.stop_video()
            self.add_info("视频播放完成")
        else:
            self.show_frame(self.state_manager.current_frame)
            if not self.slider_pressed:
                VideoUIHelper.update_progress_slider(
                    self.progress_slider, self.state_manager.current_frame,
                    len(self.preview_frames))

    def on_slider_pressed(self):
        """进度条按下"""
        self.slider_pressed = True

    def on_slider_released(self):
        """进度条释放"""
        self.slider_pressed = False
        # 跳转到指定位置
        frame_index = self.progress_slider.value()
        self.seek_to_frame(frame_index)

    def on_position_changed(self, position: int):
        """播放位置改变（用户拖拽进度条）"""
        if self.slider_pressed and self.preview_frames:
            # 实时显示拖拽位置的帧
            self.show_frame(position)

    def seek_to_frame(self, frame_index: int):
        """跳转到指定帧"""
        if not self.preview_frames:
            return

        frame_index = max(0, min(frame_index, len(self.preview_frames) - 1))
        self.current_frame_index = frame_index
        self.show_frame(frame_index)
        self.progress_slider.setValue(frame_index)

    def closeEvent(self, event):
        """窗口关闭事件"""
        self.stop_video()  # 停止视频播放
        self.hide()  # 隐藏而不是关闭，以便重复使用
        event.ignore()
