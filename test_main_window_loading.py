#!/usr/bin/env python3
"""
测试主窗口视频加载逻辑
Test Main Window Video Loading Logic
"""

import sys
import os
import tempfile
import cv2
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_video(file_path: str, duration_seconds: int = 3, fps: int = 30):
    """创建测试视频文件"""
    try:
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(file_path, fourcc, fps, (640, 480))
        
        total_frames = duration_seconds * fps
        
        for i in range(total_frames):
            # 创建彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 根据帧号改变颜色
            color_intensity = int((i / total_frames) * 255)
            frame[:, :] = [color_intensity, 100, 255 - color_intensity]
            
            # 添加帧号文本
            cv2.putText(frame, f"Frame {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 测试视频创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频创建失败: {e}")
        return False

def test_video_loader_direct():
    """直接测试VideoLoader"""
    try:
        print("直接测试VideoLoader...")
        
        from src.video.video_loader import VideoLoader
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        if not create_test_video(test_video_path):
            return False
        
        try:
            # 创建VideoLoader实例
            loader = VideoLoader()
            
            # 测试加载视频
            video_info = loader.load_video(test_video_path)
            
            if video_info and video_info.is_valid:
                print(f"✅ VideoLoader加载成功:")
                print(f"  - 文件路径: {test_video_path}")
                print(f"  - 分辨率: {video_info.width}x{video_info.height}")
                print(f"  - 帧数: {video_info.frame_count}")
                print(f"  - 帧率: {video_info.fps}")
                print(f"  - 时长: {video_info.duration:.2f}秒")
                
                # 测试获取帧
                frame = loader.get_frame(0)
                if frame is not None:
                    print(f"  - 第一帧形状: {frame.shape}")
                    return True
                else:
                    print("❌ 无法获取第一帧")
                    return False
            else:
                print("❌ VideoLoader加载失败")
                return False
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ VideoLoader测试失败: {e}")
        return False

def test_fusion_engine_loading():
    """测试融合引擎加载"""
    try:
        print("测试融合引擎加载...")
        
        from src.fusion.fusion_engine import FusionEngine
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_b_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        if not create_test_video(test_video_b_path, 3, 30):
            return False
        
        try:
            # 创建融合引擎实例
            engine = FusionEngine()
            
            # 测试加载A视频
            result_a = engine.load_video_a(test_video_a_path)
            print(f"融合引擎加载A视频结果: {result_a}")
            
            # 测试加载B视频
            result_b = engine.load_video_b(test_video_b_path)
            print(f"融合引擎加载B视频结果: {result_b}")
            
            if result_a and result_b:
                print("✅ 融合引擎加载测试通过")
                return True
            else:
                print("❌ 融合引擎加载失败")
                return False
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
                os.unlink(test_video_b_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 融合引擎测试失败: {e}")
        return False

def test_main_window_logic():
    """测试主窗口逻辑（不创建实际窗口）"""
    try:
        print("测试主窗口视频加载逻辑...")
        
        # 模拟主窗口的关键组件
        class MockMainWindow:
            def __init__(self):
                from src.video.video_loader import VideoLoader
                from src.fusion.fusion_engine import FusionEngine
                
                self.video_a_loader = VideoLoader()
                self.video_b_loader = VideoLoader()
                self.fusion_engine = FusionEngine()
                
                self.current_video_a_path = None
                self.current_video_b_path = None
                self.is_fusion_running = False
                
                self.messages = []
            
            def log_message(self, message, level="INFO"):
                self.messages.append(f"[{level}] {message}")
                print(f"[{level}] {message}")
            
            def load_video_a_file(self, file_path: str):
                """模拟主窗口的A视频加载逻辑"""
                try:
                    self.log_message(f"正在加载A视频: {file_path}")

                    # 首先加载到原有的融合引擎（这是核心功能）
                    video_info = self.video_a_loader.load_video(file_path)
                    if video_info:
                        self.current_video_a_path = file_path
                        self.fusion_engine.load_video_a(file_path)
                        self.log_message(f"A视频融合引擎加载成功: {file_path}")
                        self.log_message(f"A视频加载成功: {file_path}")
                        return True
                    else:
                        self.log_message(f"A视频加载失败: {file_path}", "ERROR")
                        return False

                except Exception as e:
                    self.log_message(f"A视频加载异常: {e}", "ERROR")
                    return False
            
            def load_video_b_file(self, file_path: str):
                """模拟主窗口的B视频加载逻辑"""
                try:
                    self.log_message(f"正在加载B视频: {file_path}")

                    # 首先加载到原有的融合引擎（这是核心功能）
                    video_info = self.video_b_loader.load_video(file_path)
                    if video_info:
                        self.current_video_b_path = file_path
                        self.fusion_engine.load_video_b(file_path)
                        self.log_message(f"B视频融合引擎加载成功: {file_path}")
                        self.log_message(f"B视频加载成功: {file_path}")
                        return True
                    else:
                        self.log_message(f"B视频加载失败: {file_path}", "ERROR")
                        return False

                except Exception as e:
                    self.log_message(f"B视频加载异常: {e}", "ERROR")
                    return False
            
            def can_start_fusion(self):
                """检查是否可以开始融合"""
                return (bool(self.current_video_a_path) and
                       bool(self.current_video_b_path) and
                       not self.is_fusion_running)
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_b_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        if not create_test_video(test_video_b_path, 3, 30):
            return False
        
        try:
            # 创建模拟主窗口
            main_window = MockMainWindow()
            
            # 测试初始状态
            print(f"初始状态 - 可以开始融合: {main_window.can_start_fusion()}")
            assert not main_window.can_start_fusion(), "初始状态应该不能开始融合"
            
            # 测试加载A视频
            result_a = main_window.load_video_a_file(test_video_a_path)
            print(f"A视频加载结果: {result_a}")
            print(f"A视频路径设置: {main_window.current_video_a_path}")
            
            # 测试加载B视频
            result_b = main_window.load_video_b_file(test_video_b_path)
            print(f"B视频加载结果: {result_b}")
            print(f"B视频路径设置: {main_window.current_video_b_path}")
            
            # 测试最终状态
            print(f"最终状态 - 可以开始融合: {main_window.can_start_fusion()}")
            
            if result_a and result_b and main_window.can_start_fusion():
                print("✅ 主窗口视频加载逻辑测试通过")
                print("现在应该可以正常进行融合了！")
                return True
            else:
                print("❌ 主窗口视频加载逻辑测试失败")
                print("问题分析:")
                print(f"  - A视频加载: {result_a}")
                print(f"  - B视频加载: {result_b}")
                print(f"  - A视频路径: {main_window.current_video_a_path}")
                print(f"  - B视频路径: {main_window.current_video_b_path}")
                print(f"  - 可以融合: {main_window.can_start_fusion()}")
                return False
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
                os.unlink(test_video_b_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 主窗口逻辑测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 主窗口视频加载修复测试 ===\n")
    
    tests = [
        ("VideoLoader直接测试", test_video_loader_direct),
        ("融合引擎加载测试", test_fusion_engine_loading),
        ("主窗口逻辑测试", test_main_window_logic)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过\n")
            else:
                print(f"❌ {test_name} 失败\n")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}\n")
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 主窗口视频加载修复测试全部通过！")
        print("问题已解决，现在应该可以正常加载视频并进行融合了。")
        return True
    else:
        print("⚠️  部分测试失败，可能仍有问题需要解决。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
