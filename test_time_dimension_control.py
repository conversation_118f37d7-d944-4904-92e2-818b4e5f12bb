#!/usr/bin/env python3
"""
测试时间维度控制算法
Test Time Dimension Control Algorithm
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.insertion_fusion import InsertionFusion
from src.fusion.fusion_engine import TimeDimensionControl, TimeDistributionMode
from src.video.video_loader import VideoLoader
from src.utils.logger import <PERSON><PERSON>

def test_time_dimension_control_generation():
    """测试时间维度控制位置生成"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试时间维度控制位置生成 ===")
    
    try:
        insertion_fusion = InsertionFusion()
        total_frames = 1000  # 假设B视频有1000帧
        
        # 测试均匀分布
        time_control = TimeDimensionControl()
        time_control.insertion_count = 5
        time_control.distribution_mode = TimeDistributionMode.UNIFORM
        
        positions = insertion_fusion.generate_time_controlled_positions(time_control, total_frames)
        assert len(positions) == 5
        logger.info(f"✅ 均匀分布测试通过: {[pos.frame_number for pos in positions]}")
        
        # 测试随机分布
        time_control.distribution_mode = TimeDistributionMode.RANDOM
        positions = insertion_fusion.generate_time_controlled_positions(time_control, total_frames)
        assert len(positions) == 5
        logger.info(f"✅ 随机分布测试通过: {[pos.frame_number for pos in positions]}")
        
        # 测试前段偏向
        time_control.distribution_mode = TimeDistributionMode.FRONT_BIASED
        time_control.time_bias_strength = 0.8
        positions = insertion_fusion.generate_time_controlled_positions(time_control, total_frames)
        assert len(positions) == 5
        front_third = total_frames // 3
        front_count = sum(1 for pos in positions if pos.frame_number < front_third)
        logger.info(f"✅ 前段偏向测试通过: {[pos.frame_number for pos in positions]}, 前1/3段有{front_count}个位置")
        
        # 测试中段偏向
        time_control.distribution_mode = TimeDistributionMode.MIDDLE_BIASED
        positions = insertion_fusion.generate_time_controlled_positions(time_control, total_frames)
        assert len(positions) == 5
        middle_start = total_frames // 3
        middle_end = 2 * total_frames // 3
        middle_count = sum(1 for pos in positions if middle_start <= pos.frame_number < middle_end)
        logger.info(f"✅ 中段偏向测试通过: {[pos.frame_number for pos in positions]}, 中1/3段有{middle_count}个位置")
        
        # 测试后段偏向
        time_control.distribution_mode = TimeDistributionMode.REAR_BIASED
        positions = insertion_fusion.generate_time_controlled_positions(time_control, total_frames)
        assert len(positions) == 5
        rear_start = 2 * total_frames // 3
        rear_count = sum(1 for pos in positions if pos.frame_number >= rear_start)
        logger.info(f"✅ 后段偏向测试通过: {[pos.frame_number for pos in positions]}, 后1/3段有{rear_count}个位置")
        
        # 测试自定义时间点
        time_control.distribution_mode = TimeDistributionMode.CUSTOM
        time_control.custom_time_points = [0.1, 0.3, 0.5, 0.7, 0.9]  # 10%, 30%, 50%, 70%, 90%
        positions = insertion_fusion.generate_time_controlled_positions(time_control, total_frames)
        assert len(positions) == 5
        expected_frames = [100, 300, 500, 700, 900]
        actual_frames = [pos.frame_number for pos in positions]
        assert actual_frames == expected_frames
        logger.info(f"✅ 自定义时间点测试通过: {actual_frames}")
        
        return True
        
    except Exception as e:
        logger.error(f"时间维度控制位置生成测试失败: {e}")
        return False

def test_time_dimension_control_with_videos():
    """测试时间维度控制与实际视频"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试时间维度控制与实际视频 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建插入融合器
        insertion_fusion = InsertionFusion()
        insertion_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 测试时间控制插入
        time_control = TimeDimensionControl()
        time_control.insertion_count = 3
        time_control.distribution_mode = TimeDistributionMode.UNIFORM
        
        video_b_info = video_b_loader.get_current_info()
        logger.info(f"B视频信息: {video_b_info.frame_count}帧")
        
        # 生成插入位置
        positions = insertion_fusion.generate_time_controlled_positions(time_control, video_b_info.frame_count)
        logger.info(f"生成的插入位置: {[pos.frame_number for pos in positions]}")
        
        # 执行时间控制插入
        result_frames = insertion_fusion.time_controlled_insertion(time_control, "direct", "fit")
        logger.info(f"时间控制插入完成，结果帧数: {len(result_frames)}")
        
        # 验证结果
        assert len(result_frames) > video_b_info.frame_count  # 插入后帧数应该增加
        logger.info("✅ 时间控制插入验证通过")
        
        # 测试预览生成
        preview_frames = insertion_fusion.get_time_controlled_preview(time_control, 3)
        logger.info(f"生成预览帧数: {len(preview_frames)}")
        assert len(preview_frames) == 3
        logger.info("✅ 时间控制预览生成通过")
        
        return True
        
    except Exception as e:
        logger.error(f"时间维度控制与实际视频测试失败: {e}")
        return False

def test_edge_cases():
    """测试边界情况"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试边界情况 ===")
    
    try:
        insertion_fusion = InsertionFusion()
        
        # 测试零帧数
        time_control = TimeDimensionControl()
        time_control.insertion_count = 5
        positions = insertion_fusion.generate_time_controlled_positions(time_control, 0)
        assert len(positions) == 0
        logger.info("✅ 零帧数测试通过")
        
        # 测试零插入次数
        time_control.insertion_count = 0
        positions = insertion_fusion.generate_time_controlled_positions(time_control, 1000)
        assert len(positions) == 0
        logger.info("✅ 零插入次数测试通过")
        
        # 测试插入次数大于总帧数
        time_control.insertion_count = 1000
        positions = insertion_fusion.generate_time_controlled_positions(time_control, 10)
        assert len(positions) <= 10  # 不应该超过总帧数
        logger.info("✅ 插入次数大于总帧数测试通过")
        
        # 测试无效的自定义时间点
        time_control.distribution_mode = TimeDistributionMode.CUSTOM
        time_control.custom_time_points = [-0.5, 1.5, 0.5]  # 包含无效值
        positions = insertion_fusion.generate_time_controlled_positions(time_control, 100)
        assert len(positions) == 3
        # 验证所有位置都在有效范围内
        for pos in positions:
            assert 0 <= pos.frame_number < 100
        logger.info("✅ 无效自定义时间点测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"边界情况测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始测试时间维度控制算法")
    
    tests = [
        test_time_dimension_control_generation,
        test_time_dimension_control_with_videos,
        test_edge_cases
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！时间维度控制算法工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
