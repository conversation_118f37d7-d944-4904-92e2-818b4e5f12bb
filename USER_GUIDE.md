# 视频融合编辑器用户指南
# Video Fusion Editor User Guide

## 🎯 五维控制系统详细指南

本指南将详细介绍如何使用视频融合编辑器的五维控制系统，帮助您创建专业的视频融合效果。

## 📋 目录

1. [快速入门](#快速入门)
2. [五维控制详解](#五维控制详解)
3. [实用案例教程](#实用案例教程)
4. [高级技巧](#高级技巧)
5. [常见问题](#常见问题)

## 🚀 快速入门

### 第一步：启动应用
```bash
./run.sh
```

### 第二步：加载视频
1. 点击工具栏的"加载A视频"和"加载B视频"按钮
2. 或者直接拖拽视频文件到应用窗口

### 第三步：选择融合类型
在"1. 时间维度"标签页中选择：
- **插入融合**: 将A视频片段插入到B视频中
- **叠加融合**: 将A视频叠加在B视频上方
- **混合融合**: 将A视频与B视频混合

### 第四步：配置五维参数
按照以下顺序配置各个维度的参数...

## 🎛️ 五维控制详解

### 1️⃣ 时间维度控制

#### 插入模式
- **直接插入**: 在指定位置插入A视频帧，不替换原有内容
- **替换插入**: 用A视频帧替换B视频的对应帧
- **分段插入**: 将A视频分段插入到B视频的多个位置

#### 时间分布模式
- **随机分布**: 在指定范围内随机选择插入位置
- **均匀分布**: 在指定范围内均匀分布插入位置
- **前段偏向**: 更多插入位置集中在视频前段
- **中段偏向**: 更多插入位置集中在视频中段
- **后段偏向**: 更多插入位置集中在视频后段
- **自定义位置**: 手动指定具体的插入时间点

#### 参数设置
- **插入次数**: 设置A视频插入B视频的次数（1-100次）
- **帧范围**: 设置融合的起始帧和结束帧

### 2️⃣ 空间尺寸控制

#### 缩放模式
- **等比例缩放 (Fit)**: 保持长宽比，适应目标尺寸
- **拉伸适配 (Stretch)**: 直接拉伸到目标尺寸
- **裁剪适配 (Crop)**: 保持长宽比，裁剪多余部分
- **填充适配 (Fill)**: 保持长宽比，填充空白区域
- **保持原尺寸 (Original)**: 不改变原始尺寸

#### 参数设置
- **缩放比例**: 0.1x - 5.0x，精确控制缩放大小
- **保持长宽比**: 是否保持原始长宽比
- **对齐方式**: 居中、左上、右上、左下、右下

### 3️⃣ 空间位置控制

#### 静态位置模式
- **位置坐标**: 使用百分比设置X、Y位置（0%-100%）
- **预设位置**: 左上、右上、左下、右下、居中等快速选择

#### 动态位置模式
- **水平滚动**: 从左到右或从右到左的水平移动
- **垂直滚动**: 从上到下或从下到上的垂直移动
- **圆形轨迹**: 以画面中心为圆心的圆形运动
- **对角线运动**: 沿对角线方向的直线运动
- **自定义路径**: 通过关键点定义复杂运动轨迹

#### 参数设置
- **运动速度**: 0.1x - 10.0x，控制运动快慢
- **运动范围**: 限制运动的边界区域

### 4️⃣ 图像内容处理控制

#### 预处理方法
- **边缘检测**: 
  - Canny算法：适合一般边缘检测
  - Sobel算法：适合水平/垂直边缘
  - Laplacian算法：适合细节边缘
  - Scharr算法：适合高精度边缘
- **直方图均衡化**: 增强图像对比度
- **Gamma校正**: 调整图像亮度（0.1-3.0）
- **高斯模糊**: 柔化图像效果
- **锐化**: 增强图像清晰度
- **降噪**: 减少图像噪点

#### 混合方法
- **Alpha混合**: 标准透明度混合
- **正片叠底**: 暗部增强效果
- **滤色**: 亮部增强效果
- **叠加**: 对比度增强效果
- **羽化融合**: 边缘柔化混合

#### 参数设置
- **边缘检测阈值**: 低阈值（0-255）、高阈值（0-255）
- **混合权重**: 0.0-1.0，控制混合强度
- **羽化半径**: 0-50像素，控制边缘柔化程度

### 5️⃣ 文字内容控制

#### 文字样式
- **字体选择**: Arial、Times New Roman、Courier、Helvetica、Comic Sans
- **字体大小**: 8-72像素
- **字体颜色**: RGB颜色选择器
- **字体效果**: 粗体、描边、阴影

#### 位置控制
- **静态位置**: 固定位置显示文字
- **动态运动**: 文字跟随运动轨迹
  - 支持所有空间位置控制的运动模式
  - 独立的运动速度设置

#### 时间控制
- **出现频次**: 文字在视频中出现的次数
- **连续帧数**: 每次出现持续的帧数
- **淡入淡出**: 平滑的出现和消失效果
- **淡入淡出时长**: 效果持续的帧数

## 🎬 实用案例教程

### 案例1：制作画中画效果

**目标**: 在主视频右下角显示小窗口视频

**步骤**:
1. **时间维度**: 选择"叠加融合"
2. **空间尺寸**: 
   - 缩放模式：等比例缩放
   - 缩放比例：0.25
   - 保持长宽比：是
3. **空间位置**:
   - 位置模式：静态位置
   - X位置：75%
   - Y位置：75%
4. **图像处理**:
   - 启用预处理：是
   - 预处理方法：边缘检测
   - 边缘检测方法：Canny
5. **文字内容**:
   - 启用文字叠加：是
   - 文字内容："直播画面"
   - 位置：右上角静态位置

### 案例2：创建动态水印

**目标**: 制作移动的半透明水印效果

**步骤**:
1. **时间维度**: 选择"混合融合"
2. **空间尺寸**:
   - 缩放模式：等比例缩放
   - 缩放比例：0.15
3. **空间位置**:
   - 位置模式：动态运动
   - 运动轨迹：圆形轨迹
   - 运动速度：1.0
4. **图像处理**:
   - 混合权重：0.3（30%透明度）
   - 混合方法：羽化融合
   - 羽化半径：10
5. **文字内容**:
   - 文字内容："© 2025 版权所有"
   - 运动轨迹：跟随圆形轨迹

### 案例3：制作片头插入

**目标**: 在视频开头插入标题片段

**步骤**:
1. **时间维度**:
   - 融合类型：插入融合
   - 插入模式：替换插入
   - 时间分布：前段偏向
   - 插入次数：1
2. **空间尺寸**:
   - 缩放模式：拉伸适配（全屏）
3. **图像处理**:
   - 启用预处理：是
   - 预处理方法：直方图均衡化、Gamma校正
   - Gamma值：1.2
4. **文字内容**:
   - 文字内容："电影标题"
   - 字体大小：48
   - 位置：居中
   - 淡入淡出：是
   - 淡入淡出时长：30帧

## 🔧 高级技巧

### 技巧1：组合多种预处理方法
可以同时启用多种预处理方法，系统会按顺序应用：
1. 边缘检测 → 2. Gamma校正 → 3. 高斯模糊

### 技巧2：自定义运动轨迹
在空间位置控制中，选择"自定义路径"可以通过关键点定义复杂轨迹。

### 技巧3：参数预设保存
配置好参数后，可以保存为预设，方便下次使用。

### 技巧4：实时预览优化
- 预览帧数设置为3-5帧可以快速查看效果
- 启用自动预览可以实时看到参数变化效果

## ❓ 常见问题

### Q1: 为什么预览生成很慢？
**A**: 可能原因和解决方案：
- 视频分辨率过高 → 降低预览帧数
- 启用了多种预处理 → 暂时关闭部分预处理
- 内存不足 → 关闭其他应用程序

### Q2: 文字显示不正确怎么办？
**A**: 检查以下设置：
- 文字编码格式是否正确
- 字体大小是否合适
- 文字位置是否在画面范围内

### Q3: 融合效果不理想怎么调整？
**A**: 建议调整顺序：
1. 先调整空间尺寸和位置
2. 再调整图像处理参数
3. 最后添加文字叠加

### Q4: 如何提高处理速度？
**A**: 优化建议：
- 使用较低的缩放比例
- 减少预处理方法数量
- 设置合适的帧范围
- 关闭不必要的文字叠加

## 📞 技术支持

如果遇到问题，请查看：
1. 应用程序日志文件
2. 系统性能监控面板
3. 内存使用情况

更多技术细节请参考 README.md 文档。
