#!/usr/bin/env python3
"""
测试空间位置控制算法
Test Spatial Position Control Algorithm
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.overlay_fusion import OverlayFusion
from src.fusion.blend_fusion import BlendFusion
from src.fusion.fusion_engine import SpatialPositionControl, MotionTrajectory
from src.video.video_loader import VideoLoader
from src.utils.logger import Logger
import numpy as np

def test_spatial_position_control_creation():
    """测试空间位置控制参数创建"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试空间位置控制参数创建 ===")
    
    try:
        # 测试默认参数
        spatial_control = SpatialPositionControl()
        assert spatial_control.is_static == True
        assert spatial_control.static_position_x == 0.5
        assert spatial_control.static_position_y == 0.5
        assert spatial_control.motion_trajectory == MotionTrajectory.STATIC
        logger.info("✅ 默认参数创建成功")
        
        # 测试动态位置参数
        spatial_control = SpatialPositionControl(
            is_static=False,
            motion_trajectory=MotionTrajectory.CIRCULAR,
            motion_speed=2.0,
            motion_range_limit=(0.1, 0.1, 0.9, 0.9)
        )
        assert spatial_control.is_static == False
        assert spatial_control.motion_trajectory == MotionTrajectory.CIRCULAR
        assert spatial_control.motion_speed == 2.0
        logger.info("✅ 动态位置参数创建成功")
        
        # 测试自定义路径
        spatial_control = SpatialPositionControl(
            is_static=False,
            motion_trajectory=MotionTrajectory.CUSTOM_PATH,
            custom_path_points=[(0.1, 0.1), (0.5, 0.5), (0.9, 0.9)]
        )
        assert len(spatial_control.custom_path_points) == 3
        logger.info("✅ 自定义路径参数创建成功")
        
        # 测试序列化
        data = spatial_control.to_dict()
        new_spatial_control = SpatialPositionControl.from_dict(data)
        assert new_spatial_control.motion_trajectory == MotionTrajectory.CUSTOM_PATH
        assert len(new_spatial_control.custom_path_points) == 3
        logger.info("✅ 参数序列化测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"空间位置控制参数创建测试失败: {e}")
        return False

def test_spatial_position_calculation():
    """测试空间位置计算"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试空间位置计算 ===")
    
    try:
        overlay_fusion = OverlayFusion()
        
        # 测试静态位置
        static_control = SpatialPositionControl(
            is_static=True,
            static_position_x=0.3,
            static_position_y=0.7
        )
        
        pos = overlay_fusion._calculate_spatial_position(
            static_control, 0, 100, 1000, 800, 200, 150
        )
        expected_x = int(0.3 * 1000)  # 300
        expected_y = int(0.7 * 800)   # 560
        assert pos == (expected_x, expected_y)
        logger.info(f"✅ 静态位置计算正确: {pos}")
        
        # 测试水平滚动
        scroll_control = SpatialPositionControl(
            is_static=False,
            motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL,
            motion_speed=1.0,
            static_position_y=0.5
        )
        
        # 测试不同帧的位置
        positions = []
        for frame_idx in [0, 25, 50, 75, 99]:
            pos = overlay_fusion._calculate_spatial_position(
                scroll_control, frame_idx, 100, 1000, 800, 200, 150
            )
            positions.append(pos)
        
        # 验证水平位置在变化，垂直位置保持不变
        y_positions = [pos[1] for pos in positions]
        assert all(y == y_positions[0] for y in y_positions)  # Y位置应该相同
        
        x_positions = [pos[0] for pos in positions]
        assert x_positions[0] <= x_positions[-1]  # X位置应该递增
        logger.info(f"✅ 水平滚动位置计算正确: {positions}")
        
        # 测试圆形轨迹
        circular_control = SpatialPositionControl(
            is_static=False,
            motion_trajectory=MotionTrajectory.CIRCULAR,
            motion_speed=1.0
        )
        
        circular_positions = []
        for frame_idx in [0, 25, 50, 75]:
            pos = overlay_fusion._calculate_spatial_position(
                circular_control, frame_idx, 100, 1000, 800, 200, 150
            )
            circular_positions.append(pos)
        
        # 验证圆形轨迹的位置都不同
        assert len(set(circular_positions)) == len(circular_positions)
        logger.info(f"✅ 圆形轨迹位置计算正确: {circular_positions}")
        
        return True
        
    except Exception as e:
        logger.error(f"空间位置计算测试失败: {e}")
        return False

def test_overlay_fusion_spatial_position_control():
    """测试叠加融合的空间位置控制"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试叠加融合空间位置控制 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建叠加融合器
        overlay_fusion = OverlayFusion()
        overlay_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 测试不同的空间位置控制参数
        test_cases = [
            {
                "name": "静态位置",
                "spatial_control": SpatialPositionControl(
                    is_static=True,
                    static_position_x=0.2,
                    static_position_y=0.3
                )
            },
            {
                "name": "水平滚动",
                "spatial_control": SpatialPositionControl(
                    is_static=False,
                    motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL,
                    motion_speed=1.0,
                    static_position_y=0.5
                )
            },
            {
                "name": "圆形轨迹",
                "spatial_control": SpatialPositionControl(
                    is_static=False,
                    motion_trajectory=MotionTrajectory.CIRCULAR,
                    motion_speed=0.5
                )
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"测试: {test_case['name']}")
            
            # 执行空间位置控制叠加融合
            result_frames = overlay_fusion.spatial_position_controlled_overlay_fusion(
                test_case['spatial_control'], 
                spatial_size_control=None,
                alpha=0.6, 
                frame_range=(0, 5)
            )
            
            # 验证结果
            if len(result_frames) != 5:
                logger.error(f"预期5帧，实际得到{len(result_frames)}帧")
                return False
            logger.info(f"✅ {test_case['name']} 测试通过，结果帧数: {len(result_frames)}")
        
        return True
        
    except Exception as e:
        logger.error(f"叠加融合空间位置控制测试失败: {e}")
        return False

def test_blend_fusion_spatial_position_control():
    """测试混合融合的空间位置控制"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试混合融合空间位置控制 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过混合融合测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建混合融合器
        blend_fusion = BlendFusion()
        blend_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 测试空间位置控制混合融合
        spatial_control = SpatialPositionControl(
            is_static=False,
            motion_trajectory=MotionTrajectory.VERTICAL_SCROLL,
            motion_speed=1.0,
            static_position_x=0.3
        )
        
        result_frames = blend_fusion.spatial_position_controlled_blend_fusion(
            spatial_control,
            spatial_size_control=None,
            alpha=0.5,
            frame_range=(0, 5)
        )
        
        # 验证结果
        if len(result_frames) != 5:
            logger.error(f"预期5帧，实际得到{len(result_frames)}帧")
            return False
        logger.info("✅ 混合融合空间位置控制测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"混合融合空间位置控制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始测试空间位置控制算法")
    
    tests = [
        test_spatial_position_control_creation,
        test_spatial_position_calculation,
        test_overlay_fusion_spatial_position_control,
        test_blend_fusion_spatial_position_control
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！空间位置控制算法工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
