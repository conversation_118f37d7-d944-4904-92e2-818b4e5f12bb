#!/usr/bin/env python3
"""
测试新的融合参数数据结构
Test New Fusion Parameters Data Structure
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.fusion_engine import (
    FusionParams, TimeDimensionControl, SpatialSizeControl, 
    SpatialPositionControl, ImageProcessingControl, TextContentControl,
    TimeDistributionMode, ScaleMode, MotionTrajectory
)
from src.utils.logger import Logger

def test_fusion_params_creation():
    """测试融合参数创建"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试融合参数创建 ===")
    
    try:
        # 创建默认参数
        params = FusionParams()
        logger.info("✅ 默认融合参数创建成功")
        
        # 检查五个控制维度是否正确初始化
        assert hasattr(params, 'time_dimension')
        assert hasattr(params, 'spatial_size')
        assert hasattr(params, 'spatial_position')
        assert hasattr(params, 'image_processing')
        assert hasattr(params, 'text_content')
        logger.info("✅ 五个控制维度参数初始化正确")
        
        # 测试参数修改
        params.time_dimension.insertion_count = 5
        params.time_dimension.distribution_mode = TimeDistributionMode.FRONT_BIASED
        
        params.spatial_size.scale_ratio = 0.8
        params.spatial_size.scale_mode = ScaleMode.CROP
        
        params.spatial_position.is_static = False
        params.spatial_position.motion_trajectory = MotionTrajectory.CIRCULAR
        
        params.image_processing.enable_preprocessing = True
        params.image_processing.preprocessing_methods = ["edge_detection", "histogram_equalization"]
        
        params.text_content.enable_text_overlay = True
        params.text_content.text_content = "测试文字"
        params.text_content.font_size = 32
        
        logger.info("✅ 参数修改成功")
        
        return True
        
    except Exception as e:
        logger.error(f"融合参数创建测试失败: {e}")
        return False

def test_fusion_params_serialization():
    """测试融合参数序列化"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试融合参数序列化 ===")
    
    try:
        # 创建参数并设置值
        params = FusionParams()
        params.time_dimension.insertion_count = 4
        params.time_dimension.distribution_mode = TimeDistributionMode.RANDOM
        params.time_dimension.custom_time_points = [0.2, 0.5, 0.8]
        
        params.spatial_size.scale_ratio = 0.75
        params.spatial_size.maintain_aspect_ratio = False
        
        params.spatial_position.static_position_x = 0.3
        params.spatial_position.static_position_y = 0.7
        
        params.image_processing.edge_detection_method = "sobel"
        params.image_processing.gamma_correction = 1.2
        
        params.text_content.text_content = "Hello World"
        params.text_content.font_family = "Times New Roman"
        params.text_content.font_color = (255, 0, 0)
        
        # 转换为字典
        params_dict = params.to_dict()
        logger.info("✅ 参数转换为字典成功")
        
        # 验证字典结构
        assert 'time_dimension' in params_dict
        assert 'spatial_size' in params_dict
        assert 'spatial_position' in params_dict
        assert 'image_processing' in params_dict
        assert 'text_content' in params_dict
        logger.info("✅ 字典结构验证正确")
        
        # 从字典重新创建参数
        new_params = FusionParams()
        new_params.from_dict(params_dict)
        logger.info("✅ 从字典重新创建参数成功")
        
        # 验证参数值
        assert new_params.time_dimension.insertion_count == 4
        assert new_params.time_dimension.distribution_mode == TimeDistributionMode.RANDOM
        assert new_params.spatial_size.scale_ratio == 0.75
        assert new_params.spatial_position.static_position_x == 0.3
        assert new_params.image_processing.edge_detection_method == "sobel"
        assert new_params.text_content.text_content == "Hello World"
        assert new_params.text_content.font_color == (255, 0, 0)
        logger.info("✅ 参数值验证正确")
        
        return True
        
    except Exception as e:
        logger.error(f"融合参数序列化测试失败: {e}")
        return False

def test_individual_control_classes():
    """测试各个控制类"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试各个控制类 ===")
    
    try:
        # 测试时间维度控制
        time_ctrl = TimeDimensionControl()
        time_ctrl.insertion_count = 6
        time_ctrl.distribution_mode = TimeDistributionMode.MIDDLE_BIASED
        time_dict = time_ctrl.to_dict()
        new_time_ctrl = TimeDimensionControl.from_dict(time_dict)
        assert new_time_ctrl.insertion_count == 6
        assert new_time_ctrl.distribution_mode == TimeDistributionMode.MIDDLE_BIASED
        logger.info("✅ 时间维度控制类测试通过")
        
        # 测试空间尺寸控制
        size_ctrl = SpatialSizeControl()
        size_ctrl.scale_ratio = 0.6
        size_ctrl.scale_mode = ScaleMode.STRETCH
        size_dict = size_ctrl.to_dict()
        new_size_ctrl = SpatialSizeControl.from_dict(size_dict)
        assert new_size_ctrl.scale_ratio == 0.6
        assert new_size_ctrl.scale_mode == ScaleMode.STRETCH
        logger.info("✅ 空间尺寸控制类测试通过")
        
        # 测试空间位置控制
        pos_ctrl = SpatialPositionControl()
        pos_ctrl.motion_trajectory = MotionTrajectory.ELLIPTICAL
        pos_ctrl.motion_speed = 2.5
        pos_dict = pos_ctrl.to_dict()
        new_pos_ctrl = SpatialPositionControl.from_dict(pos_dict)
        assert new_pos_ctrl.motion_trajectory == MotionTrajectory.ELLIPTICAL
        assert new_pos_ctrl.motion_speed == 2.5
        logger.info("✅ 空间位置控制类测试通过")
        
        # 测试图像处理控制
        img_ctrl = ImageProcessingControl()
        img_ctrl.enable_preprocessing = True
        img_ctrl.preprocessing_methods = ["edge_detection", "gamma_correction"]
        img_dict = img_ctrl.to_dict()
        new_img_ctrl = ImageProcessingControl.from_dict(img_dict)
        assert new_img_ctrl.enable_preprocessing == True
        assert "edge_detection" in new_img_ctrl.preprocessing_methods
        logger.info("✅ 图像处理控制类测试通过")
        
        # 测试文字内容控制
        text_ctrl = TextContentControl()
        text_ctrl.text_content = "测试文字内容"
        text_ctrl.font_size = 48
        text_ctrl.enable_outline = True
        text_dict = text_ctrl.to_dict()
        new_text_ctrl = TextContentControl.from_dict(text_dict)
        assert new_text_ctrl.text_content == "测试文字内容"
        assert new_text_ctrl.font_size == 48
        assert new_text_ctrl.enable_outline == True
        logger.info("✅ 文字内容控制类测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"各个控制类测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始测试新的融合参数数据结构")
    
    tests = [
        test_fusion_params_creation,
        test_fusion_params_serialization,
        test_individual_control_classes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！新的融合参数数据结构工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
