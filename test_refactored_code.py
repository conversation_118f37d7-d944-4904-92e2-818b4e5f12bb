#!/usr/bin/env python3
"""
测试重构后的代码功能
Test Refactored Code Functionality
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_video_utils_imports():
    """测试视频工具类导入"""
    try:
        print("测试视频工具类导入...")
        
        from src.utils.video_utils import (
            VideoFrameConverter, VideoLoadValidator, 
            PlaybackStateManager, VideoErrorHandler, 
            VideoUIHelper, VideoProcessingHelper
        )
        
        print("✅ 所有视频工具类导入成功")
        return True
        
    except Exception as e:
        print(f"❌ 视频工具类导入失败: {e}")
        return False

def test_playback_state_manager():
    """测试播放状态管理器"""
    try:
        print("测试播放状态管理器...")
        
        from src.utils.video_utils import PlaybackStateManager
        
        # 创建状态管理器
        manager = PlaybackStateManager("test")
        
        # 测试初始状态
        assert not manager.is_playing
        assert manager.current_frame == 0
        assert manager.total_frames == 0
        assert not manager.is_valid()
        
        # 测试设置视频信息
        manager.set_video_info("/test/video.mp4", 100, 30.0)
        assert manager.video_path == "/test/video.mp4"
        assert manager.total_frames == 100
        assert manager.fps == 30.0
        assert manager.is_valid()
        
        # 测试播放状态
        manager.set_playing(True)
        assert manager.is_playing
        
        # 测试位置设置
        manager.set_position(25)
        assert manager.current_frame == 25
        
        # 测试下一帧
        result = manager.next_frame()
        assert result
        assert manager.current_frame == 26
        
        # 测试获取状态信息
        state_info = manager.get_state_info()
        assert isinstance(state_info, dict)
        assert 'video_path' in state_info
        assert 'is_playing' in state_info
        
        print("✅ 播放状态管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 播放状态管理器测试失败: {e}")
        return False

def test_video_error_handler():
    """测试视频错误处理器"""
    try:
        print("测试视频错误处理器...")
        
        from src.utils.video_utils import VideoErrorHandler
        
        # 创建错误处理器
        handler = VideoErrorHandler("test")
        
        # 测试错误处理
        result = handler.handle_video_load_error("/test/video.mp4", Exception("测试错误"))
        assert not result
        
        result = handler.handle_frame_error(10, Exception("帧错误"))
        assert result is None
        
        result = handler.handle_playback_error("播放", Exception("播放错误"))
        assert not result
        
        # 测试错误摘要
        summary = handler.get_error_summary()
        assert isinstance(summary, dict)
        assert 'total_errors' in summary
        assert summary['total_errors'] > 0
        
        # 测试清除错误历史
        handler.clear_error_history()
        summary = handler.get_error_summary()
        assert summary['total_errors'] == 0
        
        print("✅ 视频错误处理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频错误处理器测试失败: {e}")
        return False

def test_video_frame_converter():
    """测试视频帧转换器"""
    try:
        print("测试视频帧转换器...")
        
        from src.utils.video_utils import VideoFrameConverter
        
        # 创建测试帧
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:, :] = [100, 150, 200]  # 填充颜色
        
        # 测试帧转换（不需要实际的QPixmap）
        # 这里只测试方法存在性
        assert hasattr(VideoFrameConverter, 'opencv_to_qpixmap')
        assert hasattr(VideoFrameConverter, 'display_frame_in_label')
        
        print("✅ 视频帧转换器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频帧转换器测试失败: {e}")
        return False

def test_video_load_validator():
    """测试视频加载验证器"""
    try:
        print("测试视频加载验证器...")
        
        from src.utils.video_utils import VideoLoadValidator
        
        # 测试无效路径
        result = VideoLoadValidator.validate_video_path("/nonexistent/video.mp4")
        assert not result
        
        # 测试方法存在性
        assert hasattr(VideoLoadValidator, 'test_video_open')
        
        print("✅ 视频加载验证器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频加载验证器测试失败: {e}")
        return False

def test_video_ui_helper():
    """测试视频UI辅助工具"""
    try:
        print("测试视频UI辅助工具...")
        
        from src.utils.video_utils import VideoUIHelper
        
        # 测试方法存在性
        assert hasattr(VideoUIHelper, 'update_play_button_text')
        assert hasattr(VideoUIHelper, 'update_progress_slider')
        assert hasattr(VideoUIHelper, 'enable_video_controls')
        assert hasattr(VideoUIHelper, 'format_video_info')
        
        # 测试视频信息格式化
        info = VideoUIHelper.format_video_info(
            "/test/video.mp4", 640, 480, 30.0, 10.0)
        assert isinstance(info, str)
        assert "video.mp4" in info
        assert "640x480" in info
        
        print("✅ 视频UI辅助工具测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频UI辅助工具测试失败: {e}")
        return False

def test_video_processing_helper():
    """测试视频处理辅助工具"""
    try:
        print("测试视频处理辅助工具...")
        
        from src.utils.video_utils import VideoProcessingHelper, VideoErrorHandler
        
        # 创建错误处理器
        error_handler = VideoErrorHandler("test")
        
        # 测试成功操作
        def success_operation():
            return "成功"
        
        result = VideoProcessingHelper.safe_video_operation(
            "测试操作", success_operation, error_handler)
        assert result.success
        assert result.data == "成功"
        
        # 测试失败操作
        def fail_operation():
            raise Exception("测试失败")
        
        result = VideoProcessingHelper.safe_video_operation(
            "失败操作", fail_operation, error_handler)
        assert not result.success
        
        # 测试帧号验证
        valid_frame = VideoProcessingHelper.validate_frame_number(-5, 100)
        assert valid_frame == 0
        
        valid_frame = VideoProcessingHelper.validate_frame_number(150, 100)
        assert valid_frame == 99
        
        # 测试时间计算
        time_seconds = VideoProcessingHelper.calculate_frame_time(30, 30.0)
        assert time_seconds == 1.0
        
        frame_number = VideoProcessingHelper.time_to_frame(2.0, 30.0)
        assert frame_number == 60
        
        print("✅ 视频处理辅助工具测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 视频处理辅助工具测试失败: {e}")
        return False

def test_state_change_callbacks():
    """测试状态变化回调"""
    try:
        print("测试状态变化回调...")
        
        from src.utils.video_utils import PlaybackStateManager
        
        # 创建状态管理器
        manager = PlaybackStateManager("test")
        
        # 创建回调函数
        callback_calls = []
        
        def test_callback(change_type, **kwargs):
            callback_calls.append((change_type, kwargs))
        
        # 添加回调
        manager.add_state_change_callback(test_callback)
        
        # 触发状态变化
        manager.set_video_info("/test/video.mp4", 100, 30.0)
        manager.set_playing(True)
        manager.set_position(10)
        
        # 验证回调被调用
        assert len(callback_calls) >= 3
        
        # 移除回调
        manager.remove_state_change_callback(test_callback)
        
        print("✅ 状态变化回调测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 状态变化回调测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 重构后代码功能测试 ===\n")
    
    tests = [
        ("视频工具类导入测试", test_video_utils_imports),
        ("播放状态管理器测试", test_playback_state_manager),
        ("视频错误处理器测试", test_video_error_handler),
        ("视频帧转换器测试", test_video_frame_converter),
        ("视频加载验证器测试", test_video_load_validator),
        ("视频UI辅助工具测试", test_video_ui_helper),
        ("视频处理辅助工具测试", test_video_processing_helper),
        ("状态变化回调测试", test_state_change_callbacks)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过\n")
            else:
                print(f"❌ {test_name} 失败\n")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}\n")
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有重构后的功能测试通过！代码重复定义优化成功。")
        return True
    else:
        print("⚠️  部分测试失败，请检查重构后的代码。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
