#!/usr/bin/env python3
"""
最终系统验证测试
Final System Verification Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.fusion_engine import FusionEngine
from src.video.video_loader import VideoLoader
from src.utils.logger import Logger
import numpy as np

def test_application_startup():
    """测试应用程序启动"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试应用程序启动 ===")
    
    try:
        # 测试导入主要模块
        from src.gui.main_window import MainWindow
        from src.gui.control_panel import ControlPanel
        from src.fusion.fusion_engine import FusionEngine
        
        logger.info("✅ 所有主要模块导入成功")
        
        # 测试融合引擎初始化
        engine = FusionEngine()
        logger.info("✅ 融合引擎初始化成功")
        
        return True
        
    except Exception as e:
        logger.error(f"应用程序启动测试失败: {e}")
        return False

def test_five_dimension_control_system():
    """测试五维控制系统"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试五维控制系统 ===")
    
    try:
        # 测试所有控制维度的导入
        from src.fusion.fusion_engine import (
            TimeDimensionControl, SpatialSizeControl, SpatialPositionControl,
            ImageProcessingControl, TextContentControl
        )
        
        # 测试控制器模块
        from src.effects.image_processing_controller import ImageProcessingController
        from src.effects.text_content_controller import TextContentController
        
        logger.info("✅ 五维控制系统模块导入成功")
        
        # 测试控制器初始化
        img_controller = ImageProcessingController()
        text_controller = TextContentController()
        
        logger.info("✅ 控制器初始化成功")
        
        # 测试参数创建
        time_control = TimeDimensionControl()
        spatial_size_control = SpatialSizeControl()
        spatial_position_control = SpatialPositionControl()
        image_processing_control = ImageProcessingControl()
        text_content_control = TextContentControl()
        
        logger.info("✅ 五维控制参数创建成功")
        
        return True
        
    except Exception as e:
        logger.error(f"五维控制系统测试失败: {e}")
        return False

def test_fusion_algorithms():
    """测试融合算法"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试融合算法 ===")
    
    try:
        from src.fusion.insertion_fusion import InsertionFusion
        from src.fusion.overlay_fusion import OverlayFusion
        from src.fusion.blend_fusion import BlendFusion
        
        # 测试融合类初始化
        insertion_fusion = InsertionFusion()
        overlay_fusion = OverlayFusion()
        blend_fusion = BlendFusion()
        
        logger.info("✅ 所有融合算法初始化成功")
        
        # 验证五维控制集成
        assert hasattr(insertion_fusion, 'comprehensive_controlled_insertion')
        assert hasattr(overlay_fusion, 'spatial_position_controlled_overlay_fusion')
        assert hasattr(blend_fusion, 'spatial_position_controlled_blend_fusion')
        
        logger.info("✅ 五维控制集成验证成功")
        
        return True
        
    except Exception as e:
        logger.error(f"融合算法测试失败: {e}")
        return False

def test_gui_integration():
    """测试GUI集成"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试GUI集成 ===")
    
    try:
        from src.gui.control_panel import ControlPanel
        
        # 测试控制面板方法
        control_panel = ControlPanel()
        
        # 验证五维控制标签页方法存在
        assert hasattr(control_panel, 'create_time_dimension_tab')
        assert hasattr(control_panel, 'create_spatial_size_tab')
        assert hasattr(control_panel, 'create_spatial_position_tab')
        assert hasattr(control_panel, 'create_image_processing_tab')
        assert hasattr(control_panel, 'create_text_content_tab')
        
        logger.info("✅ GUI五维控制集成验证成功")
        
        # 测试参数获取
        params = control_panel.get_current_params()
        assert 'time_dimension' in params
        assert 'spatial_size' in params
        assert 'spatial_position' in params
        assert 'image_processing' in params
        assert 'text_content' in params
        
        logger.info("✅ GUI参数获取验证成功")
        
        return True
        
    except Exception as e:
        logger.error(f"GUI集成测试失败: {e}")
        return False

def test_video_processing():
    """测试视频处理"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试视频处理 ===")
    
    try:
        # 检查测试视频文件
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过视频处理测试")
            return True
        
        # 测试视频加载
        video_loader = VideoLoader()
        success_a = video_loader.load_video(video_a_path)
        success_b = video_loader.load_video(video_b_path)
        
        assert success_a and success_b
        logger.info("✅ 视频加载测试成功")
        
        # 测试融合引擎视频加载
        engine = FusionEngine()
        engine_success_a = engine.load_video_a(video_a_path)
        engine_success_b = engine.load_video_b(video_b_path)
        
        assert engine_success_a and engine_success_b
        logger.info("✅ 融合引擎视频加载测试成功")
        
        return True
        
    except Exception as e:
        logger.error(f"视频处理测试失败: {e}")
        return False

def test_performance_and_memory():
    """测试性能和内存管理"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试性能和内存管理 ===")
    
    try:
        from src.utils.performance_monitor import PerformanceMonitor
        from src.utils.memory_manager import MemoryManager
        from src.utils.thread_pool import ThreadPoolManager
        
        # 测试性能监控
        perf_monitor = PerformanceMonitor()
        logger.info("✅ 性能监控器初始化成功")
        
        # 测试内存管理
        memory_manager = MemoryManager()
        logger.info("✅ 内存管理器初始化成功")
        
        # 测试线程池
        thread_pool = ThreadPoolManager()
        logger.info("✅ 线程池管理器初始化成功")
        
        return True
        
    except Exception as e:
        logger.error(f"性能和内存管理测试失败: {e}")
        return False

def test_documentation_completeness():
    """测试文档完整性"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试文档完整性 ===")
    
    try:
        # 检查主要文档文件
        docs = [
            "README.md",
            "USER_GUIDE.md", 
            "prd.md",
            "requirements.txt",
            "activate_env.sh",
            "run.sh"
        ]
        
        missing_docs = []
        for doc in docs:
            if not os.path.exists(doc):
                missing_docs.append(doc)
        
        if missing_docs:
            logger.warning(f"缺少文档文件: {missing_docs}")
        else:
            logger.info("✅ 所有主要文档文件存在")
        
        # 检查源代码文档
        src_dirs = [
            "src/fusion",
            "src/gui", 
            "src/video",
            "src/effects",
            "src/utils"
        ]
        
        for src_dir in src_dirs:
            if os.path.exists(src_dir):
                logger.info(f"✅ 源代码目录存在: {src_dir}")
            else:
                logger.warning(f"源代码目录缺失: {src_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"文档完整性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("🚀 开始最终系统验证测试")
    
    tests = [
        ("应用程序启动", test_application_startup),
        ("五维控制系统", test_five_dimension_control_system),
        ("融合算法", test_fusion_algorithms),
        ("GUI集成", test_gui_integration),
        ("视频处理", test_video_processing),
        ("性能和内存管理", test_performance_and_memory),
        ("文档完整性", test_documentation_completeness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- 执行测试: {test_name} ---")
        if test_func():
            passed += 1
            logger.info(f"✅ {test_name} 测试通过")
        else:
            logger.error(f"❌ {test_name} 测试失败")
    
    logger.info(f"\n🏁 最终系统验证测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 恭喜！所有系统验证测试通过！")
        logger.info("📋 五维视频融合控制系统已完全就绪")
        logger.info("🚀 系统可以投入正式使用")
        return True
    else:
        logger.error("⚠️  部分系统验证测试失败，请检查相关模块")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
