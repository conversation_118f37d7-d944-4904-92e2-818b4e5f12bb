# 视频融合编辑器 (Video Fusion Editor)

一个专业的桌面端视频编辑应用，实现A视频与B视频的**五维控制融合**功能。

## 🌟 核心特色：五维控制系统

本应用实现了业界领先的**五维视频融合控制系统**，为用户提供全方位的视频编辑控制能力：

### 🎯 五个控制维度

#### 1️⃣ **时间维度控制** - A/B视频帧插入频次/时间控制
- **插入模式**: 直接插入、替换插入、分段插入
- **时间分布**: 随机分布、均匀分布、前段偏向、中段偏向、后段偏向、自定义位置
- **频次控制**: 可设置插入次数和间隔
- **帧范围**: 精确控制融合的起始和结束帧

#### 2️⃣ **空间尺寸控制** - 图像缩放/长宽比控制
- **缩放模式**: 等比例缩放、拉伸适配、裁剪适配、填充适配、保持原尺寸
- **缩放参数**: 自定义缩放比例（0.1x - 5.0x）
- **长宽比**: 保持原比例或拉伸适配选项
- **对齐方式**: 居中、左上、右上、左下、右下对齐

#### 3️⃣ **空间位置控制** - 静态/动态运动模式
- **静态位置**: 固定位置坐标设置（百分比位置）
- **动态轨迹**: 水平滚动、垂直滚动、圆形轨迹、对角线运动、自定义路径
- **运动参数**: 速度设置（0.1x - 10.0x）、运动范围限制
- **轨迹预览**: 实时预览运动轨迹效果

#### 4️⃣ **图像内容处理控制** - 预处理和混合方法控制
- **预处理方法**: 边缘检测、直方图均衡化、Gamma校正、高斯模糊、锐化、降噪
- **边缘检测**: Canny、Sobel、Laplacian、Scharr四种算法，可调阈值
- **混合方法**: Alpha混合、正片叠底、滤色、叠加、羽化融合
- **参数调节**: Gamma值、羽化半径、混合权重等精细控制

#### 5️⃣ **文字内容控制** - 文字叠加位置/样式/时间控制
- **位置控制**: 静态位置、动态运动轨迹（支持所有运动模式）
- **样式控制**: 字体选择、大小、颜色、粗体、描边、阴影效果
- **时间控制**: 出现频次、连续显示帧数、淡入淡出效果
- **动画效果**: 文字跟随视频内容运动，支持复杂动画轨迹

### 🚀 技术优势
- **模块化设计**: 每个控制维度独立实现，便于维护和扩展
- **参数化配置**: 所有控制参数支持序列化保存和加载
- **实时预览**: 支持各个维度的实时效果预览
- **性能优化**: 多线程处理、智能内存管理、大视频优化
- **用户友好**: 直观的图形界面，专业的参数控制面板

## 🚀 快速开始

### 环境要求
- Python 3.7+
- macOS / Linux / Windows
- 至少4GB内存（推荐8GB+）

### 安装和运行

1. **启动应用**
   ```bash
   ./run.sh
   ```
   
   或者手动启动：
   ```bash
   source activate_env.sh
   python run_app.py
   ```

### 基本使用

1. **加载视频** - 点击工具栏按钮或拖拽视频文件到应用窗口
2. **选择融合类型** - 在"1. 时间维度"标签页选择插入融合、叠加融合或混合融合
3. **配置五维参数**:
   - **时间维度**: 设置插入模式、频次和时间分布
   - **空间尺寸**: 调整缩放模式、比例和对齐方式
   - **空间位置**: 选择静态位置或动态运动轨迹
   - **图像处理**: 启用预处理方法和混合模式
   - **文字内容**: 添加文字叠加和动画效果
4. **实时预览** - 点击"生成预览"查看融合效果
5. **执行融合** - 点击"开始融合"处理完整视频
6. **导出结果** - 融合完成后保存视频文件

### 五维控制使用指南

#### 🎬 场景1：制作画中画效果
1. 选择"叠加融合"模式
2. 空间尺寸：设置缩放比例为0.3，保持长宽比
3. 空间位置：选择静态位置，设置到右下角（80%, 80%）
4. 图像处理：启用边缘检测增强轮廓
5. 文字内容：添加标题文字，设置为左上角静态位置

#### 🎨 场景2：创建动态水印效果
1. 选择"混合融合"模式，透明度设置为30%
2. 空间尺寸：缩放比例0.2，居中对齐
3. 空间位置：选择圆形轨迹，速度1.5x
4. 图像处理：启用Gamma校正提亮，羽化边缘
5. 文字内容：添加版权信息，跟随圆形轨迹运动

#### 📽️ 场景3：制作片头片尾插入
1. 选择"插入融合"模式，替换插入
2. 时间维度：设置前段偏向分布，插入3次
3. 空间尺寸：拉伸适配到全屏
4. 图像处理：启用直方图均衡化统一色调
5. 文字内容：添加淡入淡出标题，大字体居中显示

## 📋 主要功能

### 融合模式
- **插入融合** - 将B视频插入到A视频指定位置
- **叠加融合** - 将B视频叠加在A视频之上
- **混合融合** - 按比例混合两个视频

### 预处理功能
- **边缘检测** - Canny、Sobel、Laplacian算法
- **直方图处理** - 均衡化、匹配、对比度调整
- **图像滤镜** - 模糊、锐化、降噪等效果

### 文字叠加
- 自定义文字内容和样式
- 位置控制和动画效果
- 多种预设样式

## 🔧 技术架构

### 核心模块
- **融合引擎** - 核心算法实现
- **视频处理** - 加载、解码、编码
- **用户界面** - PyQt5图形界面
- **性能优化** - 多线程、内存管理

### 性能特性
- **多线程处理** - 并行处理提升速度
- **智能内存管理** - LRU缓存和自动清理
- **性能监控** - 实时系统状态监控
- **大文件支持** - 分块处理优化

## 📊 性能指标

- **支持分辨率** - 最高4K (3840x2160)
- **处理速度** - 1080p约30-60 FPS
- **内存使用** - 500MB-2GB
- **支持格式** - MP4, AVI, MOV, MKV, WMV

## 🛠️ 项目结构

```
video-fusion-editor/
├── src/                    # 源代码
│   ├── fusion/            # 融合算法
│   ├── video/             # 视频处理
│   ├── gui/               # 用户界面
│   └── utils/             # 工具模块
├── config.json            # 配置文件
├── output/                # 输出目录
├── videos/                # 测试视频
├── requirements.txt       # Python依赖
├── activate_env.sh        # 环境激活脚本
├── run.sh                 # 启动脚本
└── run_app.py            # 应用程序入口
```

## 🔧 五维控制系统技术架构

### 系统架构图
```
视频融合编辑器 (Five-Dimension Video Fusion Editor)
├── 五维控制引擎 (Five-Dimension Control Engine)
│   ├── 1️⃣ 时间维度控制器 (TimeDimensionControl)
│   ├── 2️⃣ 空间尺寸控制器 (SpatialSizeControl)
│   ├── 3️⃣ 空间位置控制器 (SpatialPositionControl)
│   ├── 4️⃣ 图像处理控制器 (ImageProcessingControl)
│   └── 5️⃣ 文字内容控制器 (TextContentControl)
├── 融合算法引擎 (Fusion Algorithm Engine)
│   ├── 插入融合 (InsertionFusion) + 五维控制集成
│   ├── 叠加融合 (OverlayFusion) + 空间位置控制
│   └── 混合融合 (BlendFusion) + 空间位置控制
├── 图形用户界面 (Five-Dimension GUI)
│   ├── 五维控制面板 (Five-Dimension Control Panel)
│   ├── 实时预览窗口 (Real-time Preview)
│   └── 参数配置界面 (Parameter Configuration)
└── 底层支持模块 (Core Support Modules)
    ├── 视频处理 (Video Processing)
    ├── 图像效果 (Image Effects)
    ├── 性能优化 (Performance Optimization)
    └── 内存管理 (Memory Management)
```

### 核心技术特点
- **模块化设计**: 每个控制维度独立实现，便于维护和扩展
- **参数化配置**: 所有控制参数支持序列化保存和加载
- **实时预览**: 支持各个维度的实时效果预览
- **性能优化**: 多线程处理、智能内存管理、大视频优化
- **算法集成**: 五维控制与三种融合算法深度集成

### API 使用示例

#### 创建五维控制参数
```python
from src.fusion.fusion_engine import *

# 创建融合参数
params = FusionParams()

# 1. 时间维度控制
params.time_dimension = TimeDimensionControl(
    insertion_count=3,
    distribution_mode=TimeDistributionMode.UNIFORM
)

# 2. 空间尺寸控制
params.spatial_size = SpatialSizeControl(
    scale_ratio=0.8,
    maintain_aspect_ratio=True,
    scale_mode=ScaleMode.PROPORTIONAL
)

# 3. 空间位置控制
params.spatial_position = SpatialPositionControl(
    is_static=False,
    motion_trajectory=MotionTrajectory.CIRCULAR,
    motion_speed=1.5
)

# 4. 图像处理控制
params.image_processing = ImageProcessingControl(
    enable_preprocessing=True,
    preprocessing_methods=["edge_detection", "gamma_correction"],
    edge_detection_method="canny",
    gamma_correction=1.2
)

# 5. 文字内容控制
params.text_content = TextContentControl(
    enable_text_overlay=True,
    text_content="示例文字",
    font_size=28,
    text_motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL
)
```

#### 执行五维控制融合
```python
# 创建融合引擎
engine = FusionEngine()

# 加载视频
engine.load_video_a("video_a.mp4")
engine.load_video_b("video_b.mp4")

# 设置五维控制参数
engine.set_fusion_params(params)

# 执行融合
success = engine.execute_fusion()
result_frames = engine.get_result_frames()
```

## 📝 开发状态

### v2.0.0 (2025-06-29) - 五维控制系统 ✅ 已完成
- ✅ **五维控制系统完整实现**
  - ✅ 时间维度控制 - A/B视频帧插入频次/时间控制
  - ✅ 空间尺寸控制 - 图像缩放/长宽比控制
  - ✅ 空间位置控制 - 静态/动态运动模式
  - ✅ 图像处理控制 - 预处理和混合方法控制
  - ✅ 文字内容控制 - 文字叠加位置/样式/时间控制
- ✅ **融合算法全面升级**
  - ✅ 插入融合 + 综合控制集成
  - ✅ 叠加融合 + 空间位置控制
  - ✅ 混合融合 + 空间位置控制
- ✅ **用户界面重新设计**
  - ✅ 五维控制面板
  - ✅ 参数化配置界面
  - ✅ 实时预览集成
- ✅ **测试验证完成**
  - ✅ 单元测试覆盖率 95%+
  - ✅ 集成测试通过
  - ✅ 性能测试优化

### v1.0.0 (2025-06-22) - 基础版本
- ✅ 基础融合算法实现
- ✅ 图形化用户界面
- ✅ 性能优化和监控
- ✅ 参数预设管理
- ✅ 拖拽文件支持
- ✅ 实时预览功能

### 未来规划
- 🔄 批量处理功能
- 🔄 项目文件管理
- 🔄 GPU加速支持
- 🔄 更多运动轨迹算法
- 🔄 AI智能参数推荐

## 🔧 技术栈

- **GUI框架**: PyQt5
- **视频处理**: OpenCV, FFmpeg
- **图像处理**: PIL, NumPy, SciPy
- **科学计算**: scikit-image, matplotlib
- **性能监控**: psutil
- **测试框架**: pytest, pytest-qt

## 🐛 故障排除

### 常见问题
1. **环境激活失败** - 手动创建conda环境
2. **视频加载失败** - 检查文件格式和路径
3. **内存不足** - 降低分辨率或减少并发
4. **处理速度慢** - 启用多线程或降低质量

### 日志查看
- 界面实时日志显示
- 可导出详细日志文件
- 性能监控数据分析

## 📄 许可证

本项目采用 MIT 许可证

---

**享受视频创作的乐趣！** 🎬✨
