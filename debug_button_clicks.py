#!/usr/bin/env python3
"""
调试按钮点击问题
Debug Button Click Issues
"""

import sys
import os
import tempfile
import cv2
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_video(file_path: str, duration_seconds: int = 3, fps: int = 30):
    """创建测试视频文件"""
    try:
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(file_path, fourcc, fps, (640, 480))
        
        total_frames = duration_seconds * fps
        
        for i in range(total_frames):
            # 创建彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 根据帧号改变颜色
            color_intensity = int((i / total_frames) * 255)
            frame[:, :] = [color_intensity, 100, 255 - color_intensity]
            
            # 添加帧号文本
            cv2.putText(frame, f"Frame {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 测试视频创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频创建失败: {e}")
        return False

def test_button_connections():
    """测试按钮连接"""
    try:
        print("=== 测试按钮连接 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查控制面板是否存在
        if not hasattr(main_window, 'control_panel'):
            print("❌ 主窗口没有control_panel属性")
            return False
        
        control_panel = main_window.control_panel
        
        # 检查按钮是否存在
        if not hasattr(control_panel, 'preview_button'):
            print("❌ 控制面板没有preview_button")
            return False
        
        # 检查信号连接
        print("检查信号连接:")
        
        # 检查preview_requested信号
        preview_signal = control_panel.preview_requested
        print(f"  - preview_requested信号存在: ✅")
        
        # 检查fusion_requested信号
        fusion_signal = control_panel.fusion_requested
        print(f"  - fusion_requested信号存在: ✅")
        
        # 检查主窗口方法
        print("检查主窗口方法:")
        print(f"  - generate_preview方法: {'✅' if hasattr(main_window, 'generate_preview') else '❌'}")
        print(f"  - start_fusion方法: {'✅' if hasattr(main_window, 'start_fusion') else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 按钮连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_emission():
    """测试信号发射"""
    try:
        print("\n=== 测试信号发射 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_b_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        if not create_test_video(test_video_b_path, 3, 30):
            return False
        
        try:
            print("1. 加载测试视频...")
            
            # 加载视频
            main_window.load_video_a_file(test_video_a_path)
            main_window.load_video_b_file(test_video_b_path)
            
            print(f"   A视频路径: {main_window.current_video_a_path}")
            print(f"   B视频路径: {main_window.current_video_b_path}")
            
            print("2. 测试控制面板信号发射...")
            
            # 创建信号接收器
            preview_called = []
            fusion_called = []
            
            def on_preview_signal():
                preview_called.append(True)
                print("   📡 preview_requested信号被发射")
            
            def on_fusion_signal():
                fusion_called.append(True)
                print("   📡 fusion_requested信号被发射")
            
            # 连接信号
            main_window.control_panel.preview_requested.connect(on_preview_signal)
            main_window.control_panel.fusion_requested.connect(on_fusion_signal)
            
            print("3. 模拟按钮点击...")
            
            # 模拟预览按钮点击
            print("   点击预览按钮...")
            main_window.control_panel.generate_preview()
            
            # 模拟融合按钮点击（如果存在）
            if hasattr(main_window.control_panel, 'request_fusion'):
                print("   点击融合按钮...")
                main_window.control_panel.request_fusion()
            
            print("4. 检查信号接收...")
            print(f"   preview_requested信号被接收: {'✅' if preview_called else '❌'}")
            print(f"   fusion_requested信号被接收: {'✅' if fusion_called else '❌'}")
            
            print("5. 直接调用主窗口方法...")
            
            # 直接调用方法
            print("   直接调用generate_preview...")
            try:
                main_window.generate_preview()
                print("   ✅ generate_preview调用成功")
            except Exception as e:
                print(f"   ❌ generate_preview调用失败: {e}")
            
            print("   直接调用start_fusion...")
            try:
                main_window.start_fusion()
                print("   ✅ start_fusion调用成功")
            except Exception as e:
                print(f"   ❌ start_fusion调用失败: {e}")
            
            return True
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
                os.unlink(test_video_b_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 信号发射测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_control_panel_buttons():
    """测试控制面板按钮"""
    try:
        print("\n=== 测试控制面板按钮 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.control_panel import ControlPanel
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建控制面板
        control_panel = ControlPanel()
        
        print("检查控制面板按钮:")
        
        # 检查预览按钮
        if hasattr(control_panel, 'preview_button'):
            print(f"  - preview_button存在: ✅")
            print(f"  - preview_button文本: '{control_panel.preview_button.text()}'")
        else:
            print(f"  - preview_button存在: ❌")
        
        # 检查融合按钮（可能在其他地方）
        fusion_buttons = []
        for attr_name in dir(control_panel):
            if 'fusion' in attr_name.lower() and 'button' in attr_name.lower():
                fusion_buttons.append(attr_name)
        
        print(f"  - 找到的融合相关按钮: {fusion_buttons}")
        
        # 检查方法
        print("检查控制面板方法:")
        print(f"  - generate_preview方法: {'✅' if hasattr(control_panel, 'generate_preview') else '❌'}")
        print(f"  - request_fusion方法: {'✅' if hasattr(control_panel, 'request_fusion') else '❌'}")
        
        # 检查信号
        print("检查控制面板信号:")
        print(f"  - preview_requested信号: {'✅' if hasattr(control_panel, 'preview_requested') else '❌'}")
        print(f"  - fusion_requested信号: {'✅' if hasattr(control_panel, 'fusion_requested') else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 控制面板按钮测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== 按钮点击问题深度调试 ===\n")
    
    tests = [
        ("按钮连接测试", test_button_connections),
        ("控制面板按钮测试", test_control_panel_buttons),
        ("信号发射测试", test_signal_emission)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过\n")
            else:
                print(f"❌ {test_name} 失败\n")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}\n")
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有按钮测试通过！")
        print("如果用户仍然遇到问题，建议：")
        print("1. 检查用户使用的视频文件格式")
        print("2. 确认用户的操作步骤")
        print("3. 检查应用程序日志输出")
    else:
        print("❌ 发现按钮或信号连接问题！")
    
    return passed == total

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
