#!/usr/bin/env python3
"""
五维控制系统综合测试
Comprehensive Five-Dimension Control System Test
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.fusion_engine import (
    FusionEngine, FusionParams, FusionType,
    TimeDimensionControl, TimeDistributionMode, SpatialSizeControl, SpatialPositionControl,
    ImageProcessingControl, TextContentControl, MotionTrajectory, ScaleMode
)
from src.fusion.insertion_fusion import InsertionPosition
from src.video.video_loader import VideoLoader
from src.utils.logger import Logger
import numpy as np

def test_fusion_params_creation_and_serialization():
    """测试融合参数创建和序列化"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试融合参数创建和序列化 ===")
    
    try:
        # 创建完整的融合参数
        params = FusionParams()
        
        # 设置时间维度控制
        params.time_dimension = TimeDimensionControl(
            insertion_count=3,
            distribution_mode=TimeDistributionMode.UNIFORM
        )
        
        # 设置空间尺寸控制
        params.spatial_size = SpatialSizeControl(
            scale_ratio=0.8,
            maintain_aspect_ratio=True,
            scale_mode=ScaleMode.PROPORTIONAL
        )
        
        # 设置空间位置控制
        params.spatial_position = SpatialPositionControl(
            is_static=False,
            motion_trajectory=MotionTrajectory.CIRCULAR,
            motion_speed=1.5
        )
        
        # 设置图像处理控制
        params.image_processing = ImageProcessingControl(
            enable_preprocessing=True,
            preprocessing_methods=["edge_detection", "gamma_correction"],
            edge_detection_method="canny",
            gamma_correction=1.2
        )
        
        # 设置文字内容控制
        params.text_content = TextContentControl(
            enable_text_overlay=True,
            text_content="测试文字",
            font_size=28,
            font_color=(255, 0, 0),
            text_motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL
        )
        
        # 测试序列化
        data = params.to_dict()
        assert "time_dimension" in data
        assert "spatial_size" in data
        assert "spatial_position" in data
        assert "image_processing" in data
        assert "text_content" in data
        logger.info("✅ 融合参数序列化测试通过")
        
        # 测试反序列化
        new_params = FusionParams()
        new_params.from_dict(data)
        assert new_params.time_dimension.insertion_count == 3
        assert new_params.spatial_size.scale_ratio == 0.8
        assert new_params.spatial_position.motion_speed == 1.5
        assert new_params.image_processing.gamma_correction == 1.2
        assert new_params.text_content.font_size == 28
        logger.info("✅ 融合参数反序列化测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"融合参数创建和序列化测试失败: {e}")
        return False

def test_fusion_engine_gui_integration():
    """测试融合引擎GUI集成"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试融合引擎GUI集成 ===")
    
    try:
        engine = FusionEngine()
        
        # 模拟GUI参数
        gui_params = {
            "fusion_type": "insertion",
            "alpha": 0.7,
            "time_dimension": {
                "insertion_mode": "replace",
                "insertion_count": 4,
                "time_distribution": "random",
                "frame_range": (20, 80)
            },
            "spatial_size": {
                "resize_mode": "crop",
                "scale_ratio": 1.2,
                "keep_aspect_ratio": False,
                "alignment": "top-left"
            },
            "spatial_position": {
                "position_mode": "dynamic",
                "static_position": {"x": 0.3, "y": 0.7},
                "dynamic_motion": {"trajectory": "vertical", "speed": 2.0}
            },
            "image_processing": {
                "enable_preprocessing": True,
                "preprocessing_methods": ["edge_detection", "histogram_equalization"],
                "edge_detection": {"method": "sobel", "low_threshold": 30, "high_threshold": 120},
                "gamma_correction": 1.5
            },
            "text_content": {
                "enable_text_overlay": True,
                "text_content": "GUI测试文字",
                "font_family": "Times New Roman",
                "font_size": 32,
                "font_color": (0, 255, 0),
                "font_bold": True,
                "text_position_mode": "dynamic",
                "text_motion_trajectory": "circular",
                "text_motion_speed": 0.8,
                "appearance_frequency": 2,
                "continuous_frames": 50
            }
        }
        
        # 测试GUI参数设置
        engine.set_fusion_params_from_gui(gui_params)
        
        # 验证参数设置
        params = engine.fusion_params
        assert params.fusion_type == FusionType.INSERTION
        assert params.alpha == 0.7
        assert params.time_dimension.insertion_count == 4
        assert params.spatial_size.resize_mode == "crop"
        assert params.spatial_position.motion_speed == 2.0
        assert params.image_processing.enable_preprocessing == True
        assert params.text_content.font_size == 32
        
        logger.info("✅ 融合引擎GUI集成测试通过")
        return True
        
    except Exception as e:
        logger.error(f"融合引擎GUI集成测试失败: {e}")
        return False

def test_comprehensive_insertion_fusion():
    """测试综合控制插入融合"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试综合控制插入融合 ===")
    
    try:
        # 检查测试视频文件
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 创建融合引擎
        engine = FusionEngine()
        
        # 加载视频
        if not engine.load_video_a(video_a_path):
            logger.error("加载A视频失败")
            return False
        if not engine.load_video_b(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 设置插入位置
        engine.add_insertion_position(30, 1)
        engine.add_insertion_position(60, 1)
        
        # 创建综合控制参数
        gui_params = {
            "fusion_type": "insertion",
            "alpha": 0.6,
            "time_dimension": {
                "insertion_mode": "direct",
                "insertion_count": 2,
                "time_distribution": "uniform",
                "frame_range": (0, 100)
            },
            "spatial_size": {
                "resize_mode": "fit",
                "scale_ratio": 1.0,
                "keep_aspect_ratio": True,
                "alignment": "center"
            },
            "spatial_position": {
                "position_mode": "static",
                "static_position": {"x": 0.2, "y": 0.3},
                "dynamic_motion": {"trajectory": "horizontal", "speed": 1.0}
            },
            "image_processing": {
                "enable_preprocessing": True,
                "preprocessing_methods": ["gamma_correction"],
                "gamma_correction": 1.3
            },
            "text_content": {
                "enable_text_overlay": True,
                "text_content": "综合测试",
                "font_size": 24,
                "font_color": (255, 255, 0),
                "text_position_mode": "static",
                "static_position": {"x": 0.1, "y": 0.9},
                "appearance_frequency": 1,
                "continuous_frames": 100
            }
        }
        
        # 设置参数并执行融合
        engine.set_fusion_params_from_gui(gui_params)
        
        # 执行融合
        success = engine.execute_fusion()
        assert success == True
        
        # 验证结果
        result_frames = engine.get_result_frames()
        assert len(result_frames) > 0
        
        logger.info(f"✅ 综合控制插入融合测试通过，结果帧数: {len(result_frames)}")
        return True
        
    except Exception as e:
        logger.error(f"综合控制插入融合测试失败: {e}")
        return False

def test_comprehensive_overlay_fusion():
    """测试综合控制叠加融合"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试综合控制叠加融合 ===")
    
    try:
        # 检查测试视频文件
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 创建融合引擎
        engine = FusionEngine()
        
        # 加载视频
        if not engine.load_video_a(video_a_path):
            logger.error("加载A视频失败")
            return False
        if not engine.load_video_b(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建叠加融合参数
        gui_params = {
            "fusion_type": "overlay",
            "alpha": 0.5,
            "spatial_position": {
                "position_mode": "dynamic",
                "dynamic_motion": {"trajectory": "circular", "speed": 1.5}
            },
            "spatial_size": {
                "resize_mode": "fit",
                "scale_ratio": 0.7,
                "keep_aspect_ratio": True
            },
            "text_content": {
                "enable_text_overlay": True,
                "text_content": "叠加测试",
                "font_size": 20,
                "font_color": (0, 255, 255),
                "text_position_mode": "dynamic",
                "text_motion_trajectory": "horizontal",
                "text_motion_speed": 2.0
            }
        }
        
        # 设置参数并执行融合
        engine.set_fusion_params_from_gui(gui_params)
        success = engine.execute_fusion()
        assert success == True
        
        # 验证结果
        result_frames = engine.get_result_frames()
        assert len(result_frames) > 0
        
        logger.info(f"✅ 综合控制叠加融合测试通过，结果帧数: {len(result_frames)}")
        return True
        
    except Exception as e:
        logger.error(f"综合控制叠加融合测试失败: {e}")
        return False

def test_comprehensive_blend_fusion():
    """测试综合控制混合融合"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试综合控制混合融合 ===")
    
    try:
        # 检查测试视频文件
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 创建融合引擎
        engine = FusionEngine()
        
        # 加载视频
        if not engine.load_video_a(video_a_path):
            logger.error("加载A视频失败")
            return False
        if not engine.load_video_b(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建混合融合参数
        gui_params = {
            "fusion_type": "blend",
            "alpha": 0.4,
            "spatial_position": {
                "position_mode": "dynamic",
                "dynamic_motion": {"trajectory": "diagonal", "speed": 1.0}
            },
            "image_processing": {
                "enable_preprocessing": True,
                "preprocessing_methods": ["edge_detection", "gaussian_blur"],
                "edge_detection": {"method": "canny", "low_threshold": 50, "high_threshold": 150}
            },
            "text_content": {
                "enable_text_overlay": True,
                "text_content": "混合测试",
                "font_size": 26,
                "font_color": (255, 0, 255),
                "enable_outline": True,
                "enable_shadow": True
            }
        }
        
        # 设置参数并执行融合
        engine.set_fusion_params_from_gui(gui_params)
        success = engine.execute_fusion()
        assert success == True
        
        # 验证结果
        result_frames = engine.get_result_frames()
        assert len(result_frames) > 0
        
        logger.info(f"✅ 综合控制混合融合测试通过，结果帧数: {len(result_frames)}")
        return True
        
    except Exception as e:
        logger.error(f"综合控制混合融合测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始五维控制系统综合测试")
    
    tests = [
        test_fusion_params_creation_and_serialization,
        test_fusion_engine_gui_integration,
        test_comprehensive_insertion_fusion,
        test_comprehensive_overlay_fusion,
        test_comprehensive_blend_fusion
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"综合测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有综合测试通过！五维控制系统工作正常")
        return True
    else:
        logger.error("❌ 部分综合测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
