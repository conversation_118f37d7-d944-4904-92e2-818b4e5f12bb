#!/usr/bin/env python3
"""
简单的视频播放控制功能测试
Simple Video Player Controls Test
"""

import sys
import os

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_video_player_imports():
    """测试视频播放器模块导入"""
    try:
        print("测试导入PyQt5模块...")
        from PyQt5.QtWidgets import QApplication, QWidget
        from PyQt5.QtCore import Qt, QTimer
        from PyQt5.QtGui import QPixmap
        print("✅ PyQt5模块导入成功")
        
        print("测试导入OpenCV模块...")
        import cv2
        print(f"✅ OpenCV版本: {cv2.__version__}")
        
        print("测试导入NumPy模块...")
        import numpy as np
        print(f"✅ NumPy版本: {np.__version__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_video_player_class():
    """测试视频播放器类定义"""
    try:
        print("测试视频播放器类导入...")
        
        # 直接导入视频播放器文件
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "video_player", 
            os.path.join(os.path.dirname(__file__), 'src', 'gui', 'video_player.py')
        )
        video_player_module = importlib.util.module_from_spec(spec)
        
        # 模拟依赖
        sys.modules['src.utils.logger'] = type('MockLogger', (), {
            'Logger': type('Logger', (), {
                'get_logger': lambda name: type('MockLogger', (), {
                    'info': lambda msg: print(f"INFO: {msg}"),
                    'error': lambda msg: print(f"ERROR: {msg}"),
                    'warning': lambda msg: print(f"WARNING: {msg}")
                })()
            })
        })()
        
        sys.modules['src.video.video_loader'] = type('MockVideoLoader', (), {
            'VideoLoader': type('VideoLoader', (), {
                '__init__': lambda self: None,
                'load_video': lambda self, path: None,
                'get_frame': lambda self, frame_num: None,
                'is_loaded': lambda self: False,
                'get_current_info': lambda self: None
            })
        })()
        
        spec.loader.exec_module(video_player_module)
        
        # 检查类是否存在
        if hasattr(video_player_module, 'VideoPlayer'):
            print("✅ VideoPlayer类定义正确")
        else:
            print("❌ VideoPlayer类未找到")
            return False
            
        if hasattr(video_player_module, 'VideoPlaybackThread'):
            print("✅ VideoPlaybackThread类定义正确")
        else:
            print("❌ VideoPlaybackThread类未找到")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 视频播放器类测试失败: {e}")
        return False

def test_video_functionality():
    """测试视频功能"""
    try:
        print("测试基本视频操作...")
        
        # 测试OpenCV视频操作
        import cv2
        import numpy as np
        
        # 创建一个测试视频帧
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:, :] = [100, 150, 200]  # 填充颜色
        
        print("✅ 测试帧创建成功")
        
        # 测试图像转换
        from PyQt5.QtGui import QImage, QPixmap
        height, width, channel = test_frame.shape
        bytes_per_line = 3 * width
        q_image = QImage(test_frame.data, width, height, bytes_per_line, QImage.Format_RGB888)
        pixmap = QPixmap.fromImage(q_image)
        
        print("✅ 图像转换成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频功能测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    try:
        print("测试UI组件创建...")
        
        from PyQt5.QtWidgets import QApplication, QWidget, QVBoxLayout, QHBoxLayout
        from PyQt5.QtWidgets import QLabel, QPushButton, QSlider, QGroupBox
        from PyQt5.QtCore import Qt
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建测试组件
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 创建组框
        group_box = QGroupBox("测试视频播放器")
        group_layout = QVBoxLayout(group_box)
        
        # 创建标签
        label = QLabel("测试视频显示区域")
        label.setAlignment(Qt.AlignCenter)
        label.setMinimumHeight(200)
        group_layout.addWidget(label)
        
        # 创建进度条
        slider = QSlider(Qt.Horizontal)
        slider.setMinimum(0)
        slider.setMaximum(100)
        group_layout.addWidget(slider)
        
        # 创建按钮
        button_layout = QHBoxLayout()
        play_btn = QPushButton("播放")
        stop_btn = QPushButton("停止")
        load_btn = QPushButton("加载视频")
        
        button_layout.addWidget(play_btn)
        button_layout.addWidget(stop_btn)
        button_layout.addWidget(load_btn)
        group_layout.addLayout(button_layout)
        
        layout.addWidget(group_box)
        
        print("✅ UI组件创建成功")
        
        # 测试组件属性设置
        play_btn.setText("暂停")
        slider.setValue(50)
        label.setText("测试文本更新")
        
        print("✅ UI组件属性设置成功")
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 视频播放控制功能简单测试 ===\n")
    
    tests = [
        ("模块导入测试", test_video_player_imports),
        ("视频播放器类测试", test_video_player_class),
        ("视频功能测试", test_video_functionality),
        ("UI组件测试", test_ui_components)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过\n")
            else:
                print(f"❌ {test_name} 失败\n")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}\n")
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！视频播放控制功能基础组件正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关组件。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
