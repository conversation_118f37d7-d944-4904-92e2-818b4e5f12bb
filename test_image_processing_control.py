#!/usr/bin/env python3
"""
测试图像处理控制功能
Test Image Processing Control
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.insertion_fusion import InsertionFusion, InsertionPosition
from src.fusion.fusion_engine import ImageProcessingControl
from src.effects.image_processing_controller import ImageProcessingController
from src.video.video_loader import VideoLoader
from src.utils.logger import Logger
import numpy as np
import cv2

def test_image_processing_control_creation():
    """测试图像处理控制参数创建"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试图像处理控制参数创建 ===")
    
    try:
        # 测试默认参数
        img_control = ImageProcessingControl()
        assert img_control.enable_preprocessing == False
        assert img_control.preprocessing_methods == []
        assert img_control.edge_detection_method == "canny"
        assert img_control.fusion_method == "alpha_blend"
        logger.info("✅ 默认参数创建成功")
        
        # 测试自定义参数
        img_control = ImageProcessingControl(
            enable_preprocessing=True,
            preprocessing_methods=["edge_detection", "histogram_equalization"],
            edge_detection_method="sobel",
            edge_low_threshold=30,
            edge_high_threshold=100,
            fusion_method="multiply",
            blend_weight=0.7
        )
        assert img_control.enable_preprocessing == True
        assert "edge_detection" in img_control.preprocessing_methods
        assert img_control.edge_detection_method == "sobel"
        assert img_control.fusion_method == "multiply"
        logger.info("✅ 自定义参数创建成功")
        
        # 测试序列化
        data = img_control.to_dict()
        new_img_control = ImageProcessingControl.from_dict(data)
        assert new_img_control.enable_preprocessing == True
        assert new_img_control.edge_detection_method == "sobel"
        assert new_img_control.fusion_method == "multiply"
        logger.info("✅ 参数序列化测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"图像处理控制参数创建测试失败: {e}")
        return False

def test_image_processing_controller():
    """测试图像处理控制器"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试图像处理控制器 ===")
    
    try:
        controller = ImageProcessingController()
        
        # 创建测试图像
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # 测试不启用预处理
        img_control = ImageProcessingControl(enable_preprocessing=False)
        result = controller.process_frame_with_control(test_frame, img_control)
        assert np.array_equal(result, test_frame)
        logger.info("✅ 不启用预处理测试通过")
        
        # 测试边缘检测
        img_control = ImageProcessingControl(
            enable_preprocessing=True,
            preprocessing_methods=["edge_detection"],
            edge_detection_method="canny"
        )
        result = controller.process_frame_with_control(test_frame, img_control)
        assert result.shape == test_frame.shape
        logger.info("✅ 边缘检测测试通过")
        
        # 测试Gamma校正
        img_control = ImageProcessingControl(
            enable_preprocessing=True,
            preprocessing_methods=["gamma_correction"],
            gamma_correction=1.5
        )
        result = controller.process_frame_with_control(test_frame, img_control)
        assert result.shape == test_frame.shape
        logger.info("✅ Gamma校正测试通过")
        
        # 测试多种预处理方法组合
        img_control = ImageProcessingControl(
            enable_preprocessing=True,
            preprocessing_methods=["gamma_correction", "gaussian_blur", "sharpen"],
            gamma_correction=1.2
        )
        result = controller.process_frame_with_control(test_frame, img_control)
        assert result.shape == test_frame.shape
        logger.info("✅ 多种预处理方法组合测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"图像处理控制器测试失败: {e}")
        return False

def test_fusion_methods():
    """测试融合方法"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试融合方法 ===")
    
    try:
        controller = ImageProcessingController()
        
        # 创建测试图像
        background = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        foreground = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # 测试不同的融合方法
        fusion_methods = ["alpha_blend", "normal", "multiply", "screen", "overlay", "feather"]
        
        for method in fusion_methods:
            img_control = ImageProcessingControl(
                fusion_method=method,
                blend_weight=0.5,
                feather_radius=5
            )
            
            result = controller.apply_fusion_method(background, foreground, img_control)
            assert result.shape == background.shape
            logger.info(f"✅ 融合方法 {method} 测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"融合方法测试失败: {e}")
        return False

def test_insertion_fusion_with_image_processing():
    """测试插入融合的图像处理控制"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试插入融合图像处理控制 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建插入融合器
        insertion_fusion = InsertionFusion()
        insertion_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 测试不同的图像处理控制参数
        test_cases = [
            {
                "name": "边缘检测",
                "img_control": ImageProcessingControl(
                    enable_preprocessing=True,
                    preprocessing_methods=["edge_detection"],
                    edge_detection_method="canny",
                    edge_low_threshold=50,
                    edge_high_threshold=150
                )
            },
            {
                "name": "直方图均衡化",
                "img_control": ImageProcessingControl(
                    enable_preprocessing=True,
                    preprocessing_methods=["histogram_equalization"]
                )
            },
            {
                "name": "Gamma校正",
                "img_control": ImageProcessingControl(
                    enable_preprocessing=True,
                    preprocessing_methods=["gamma_correction"],
                    gamma_correction=1.5
                )
            },
            {
                "name": "组合处理",
                "img_control": ImageProcessingControl(
                    enable_preprocessing=True,
                    preprocessing_methods=["gamma_correction", "gaussian_blur"],
                    gamma_correction=1.2
                )
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"测试: {test_case['name']}")
            
            # 创建插入位置
            positions = [InsertionPosition(50, 1), InsertionPosition(100, 1)]
            
            # 执行图像处理控制插入
            result_frames = insertion_fusion.image_processing_controlled_insertion(
                positions, test_case['img_control'], None, "direct"
            )
            
            # 验证结果
            assert len(result_frames) > 0
            logger.info(f"✅ {test_case['name']} 测试通过，结果帧数: {len(result_frames)}")
        
        return True
        
    except Exception as e:
        logger.error(f"插入融合图像处理控制测试失败: {e}")
        return False

def test_available_methods():
    """测试可用方法列表"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试可用方法列表 ===")
    
    try:
        controller = ImageProcessingController()
        methods = controller.get_available_methods()
        
        # 验证返回的方法列表
        assert "preprocessing_methods" in methods
        assert "edge_detection_methods" in methods
        assert "fusion_methods" in methods
        
        assert "edge_detection" in methods["preprocessing_methods"]
        assert "canny" in methods["edge_detection_methods"]
        assert "alpha_blend" in methods["fusion_methods"]
        
        logger.info(f"✅ 可用方法列表测试通过")
        logger.info(f"   预处理方法: {methods['preprocessing_methods']}")
        logger.info(f"   边缘检测方法: {methods['edge_detection_methods']}")
        logger.info(f"   融合方法: {methods['fusion_methods']}")
        
        return True
        
    except Exception as e:
        logger.error(f"可用方法列表测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始测试图像处理控制功能")
    
    tests = [
        test_image_processing_control_creation,
        test_image_processing_controller,
        test_fusion_methods,
        test_available_methods,
        test_insertion_fusion_with_image_processing
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！图像处理控制功能工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
