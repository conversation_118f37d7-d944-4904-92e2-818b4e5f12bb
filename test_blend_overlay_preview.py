#!/usr/bin/env python3
"""
测试混合融合和叠加融合预览功能
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.video.video_loader import VideoLoader
from src.fusion.fusion_engine import FusionEngine, FusionParams, FusionType

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_blend_preview():
    """测试混合融合预览"""
    logger.info("=== 测试混合融合预览 ===")
    
    # 测试视频路径
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    # 检查视频文件是否存在
    if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
        logger.error("测试视频文件不存在")
        return False
    
    try:
        # 创建融合引擎
        fusion_engine = FusionEngine()
        
        # 加载视频
        if not fusion_engine.load_video_a(video_a_path):
            logger.error("A视频加载失败")
            return False
        
        if not fusion_engine.load_video_b(video_b_path):
            logger.error("B视频加载失败")
            return False
        
        # 设置混合融合参数
        fusion_params = FusionParams()
        fusion_params.fusion_type = FusionType.BLEND
        fusion_params.alpha = 0.5
        fusion_params.resize_mode = "fit"
        
        fusion_engine.set_fusion_params(fusion_params)
        logger.info("混合融合参数设置完成")
        
        # 生成预览
        logger.info("生成混合融合预览...")
        preview_frames = fusion_engine.get_fusion_preview(max_frames=3)
        
        if preview_frames:
            logger.info(f"✅ 混合融合预览生成成功，预览帧数: {len(preview_frames)}")
            
            # 保存预览图像
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            import cv2
            for i, (frame_index, frame) in enumerate(preview_frames):
                preview_path = output_dir / f"blend_preview_{i}_frame_{frame_index}.jpg"
                cv2.imwrite(str(preview_path), frame)
                logger.info(f"混合预览图像已保存: {preview_path}")
            
            return True
        else:
            logger.error("❌ 混合融合预览生成失败")
            return False
            
    except Exception as e:
        logger.error(f"混合融合预览测试失败: {e}")
        return False

def test_overlay_preview():
    """测试叠加融合预览"""
    logger.info("=== 测试叠加融合预览 ===")
    
    # 测试视频路径
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    # 检查视频文件是否存在
    if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
        logger.error("测试视频文件不存在")
        return False
    
    try:
        # 创建融合引擎
        fusion_engine = FusionEngine()
        
        # 加载视频
        if not fusion_engine.load_video_a(video_a_path):
            logger.error("A视频加载失败")
            return False
        
        if not fusion_engine.load_video_b(video_b_path):
            logger.error("B视频加载失败")
            return False
        
        # 设置叠加融合参数
        fusion_params = FusionParams()
        fusion_params.fusion_type = FusionType.OVERLAY
        fusion_params.alpha = 0.7
        fusion_params.resize_mode = "fit"
        
        fusion_engine.set_fusion_params(fusion_params)
        logger.info("叠加融合参数设置完成")
        
        # 生成预览
        logger.info("生成叠加融合预览...")
        preview_frames = fusion_engine.get_fusion_preview(max_frames=3)
        
        if preview_frames:
            logger.info(f"✅ 叠加融合预览生成成功，预览帧数: {len(preview_frames)}")
            
            # 保存预览图像
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            import cv2
            for i, (frame_index, frame) in enumerate(preview_frames):
                preview_path = output_dir / f"overlay_preview_{i}_frame_{frame_index}.jpg"
                cv2.imwrite(str(preview_path), frame)
                logger.info(f"叠加预览图像已保存: {preview_path}")
            
            return True
        else:
            logger.error("❌ 叠加融合预览生成失败")
            return False
            
    except Exception as e:
        logger.error(f"叠加融合预览测试失败: {e}")
        return False

def test_all_fusion_types():
    """测试所有融合类型的预览"""
    logger.info("=== 测试所有融合类型预览 ===")
    
    # 测试视频路径
    video_a_path = "videos/172681-849651720_tiny.mp4"
    video_b_path = "videos/283533_medium.mp4"
    
    # 检查视频文件是否存在
    if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
        logger.error("测试视频文件不存在")
        return False
    
    fusion_types = [
        (FusionType.INSERTION, "插入融合"),
        (FusionType.BLEND, "混合融合"),
        (FusionType.OVERLAY, "叠加融合")
    ]
    
    success_count = 0
    
    for fusion_type, type_name in fusion_types:
        try:
            logger.info(f"测试 {type_name}...")
            
            # 创建融合引擎
            fusion_engine = FusionEngine()
            
            # 加载视频
            fusion_engine.load_video_a(video_a_path)
            fusion_engine.load_video_b(video_b_path)
            
            # 设置融合参数
            fusion_params = FusionParams()
            fusion_params.fusion_type = fusion_type
            fusion_params.alpha = 0.6
            fusion_params.resize_mode = "fit"
            
            # 为插入融合设置位置
            if fusion_type == FusionType.INSERTION:
                from src.fusion.insertion_fusion import InsertionPosition
                fusion_params.positions = [
                    InsertionPosition(frame_number=100, duration=1),
                    InsertionPosition(frame_number=200, duration=1)
                ]
            
            fusion_engine.set_fusion_params(fusion_params)
            
            # 生成预览
            preview_frames = fusion_engine.get_fusion_preview(max_frames=2)
            
            if preview_frames:
                logger.info(f"✅ {type_name} 预览成功，帧数: {len(preview_frames)}")
                success_count += 1
            else:
                logger.error(f"❌ {type_name} 预览失败")
                
        except Exception as e:
            logger.error(f"❌ {type_name} 预览异常: {e}")
    
    logger.info(f"预览测试完成: {success_count}/{len(fusion_types)} 成功")
    return success_count == len(fusion_types)

def main():
    """主测试函数"""
    logger.info("🚀 开始混合融合和叠加融合预览测试")
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    logger.info(f"当前工作目录: {current_dir}")
    
    # 测试列表
    tests = [
        ("混合融合预览", test_blend_preview),
        ("叠加融合预览", test_overlay_preview),
        ("所有融合类型预览", test_all_fusion_types),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！混合融合和叠加融合预览功能正常")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
