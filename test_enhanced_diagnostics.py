#!/usr/bin/env python3
"""
测试增强的错误诊断功能
Test Enhanced Error Diagnostics
"""

import sys
import os
import tempfile
import cv2
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_video(file_path: str, duration_seconds: int = 3, fps: int = 30):
    """创建测试视频文件"""
    try:
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(file_path, fourcc, fps, (640, 480))
        
        total_frames = duration_seconds * fps
        
        for i in range(total_frames):
            # 创建彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 根据帧号改变颜色
            color_intensity = int((i / total_frames) * 255)
            frame[:, :] = [color_intensity, 100, 255 - color_intensity]
            
            # 添加帧号文本
            cv2.putText(frame, f"Frame {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 测试视频创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频创建失败: {e}")
        return False

def test_no_videos_loaded():
    """测试没有加载视频的情况"""
    try:
        print("=== 测试场景1: 没有加载任何视频 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        print("初始状态:")
        print(f"  - current_video_a_path: {main_window.current_video_a_path}")
        print(f"  - current_video_b_path: {main_window.current_video_b_path}")
        
        # 测试诊断功能
        issues, details = main_window.diagnose_video_loading_issues()
        
        print("\n诊断结果:")
        print("问题列表:")
        for i, issue in enumerate(issues, 1):
            print(f"  {i}. {issue}")
        
        print("\n详细状态:")
        for detail in details:
            print(f"  {detail}")
        
        print("\n✅ 测试场景1完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试场景1失败: {e}")
        return False

def test_partial_videos_loaded():
    """测试部分视频加载的情况"""
    try:
        print("\n=== 测试场景2: 只加载A视频 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 创建测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        try:
            # 只加载A视频
            main_window.load_video_a_file(test_video_a_path)
            
            print("加载A视频后状态:")
            print(f"  - current_video_a_path: {main_window.current_video_a_path}")
            print(f"  - current_video_b_path: {main_window.current_video_b_path}")
            
            # 测试诊断功能
            issues, details = main_window.diagnose_video_loading_issues()
            
            print("\n诊断结果:")
            print("问题列表:")
            for i, issue in enumerate(issues, 1):
                print(f"  {i}. {issue}")
            
            print("\n详细状态:")
            for detail in details:
                print(f"  {detail}")
            
            print("\n✅ 测试场景2完成")
            return True
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试场景2失败: {e}")
        return False

def test_both_videos_loaded():
    """测试两个视频都加载的情况"""
    try:
        print("\n=== 测试场景3: 加载A和B视频 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 创建测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_b_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        if not create_test_video(test_video_b_path, 3, 30):
            return False
        
        try:
            # 加载A和B视频
            main_window.load_video_a_file(test_video_a_path)
            main_window.load_video_b_file(test_video_b_path)
            
            print("加载A和B视频后状态:")
            print(f"  - current_video_a_path: {main_window.current_video_a_path}")
            print(f"  - current_video_b_path: {main_window.current_video_b_path}")
            
            # 测试诊断功能
            issues, details = main_window.diagnose_video_loading_issues()
            
            print("\n诊断结果:")
            if issues:
                print("问题列表:")
                for i, issue in enumerate(issues, 1):
                    print(f"  {i}. {issue}")
            else:
                print("  无问题发现")
            
            print("\n详细状态:")
            for detail in details:
                print(f"  {detail}")
            
            # 测试融合引擎状态
            engine_status = main_window.fusion_engine.get_status()
            print(f"\n融合引擎状态: {engine_status}")
            
            print("\n✅ 测试场景3完成")
            return True
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
                os.unlink(test_video_b_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 测试场景3失败: {e}")
        return False

def test_error_message_generation():
    """测试错误消息生成"""
    try:
        print("\n=== 测试场景4: 错误消息生成 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        from unittest.mock import patch
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 模拟QMessageBox以捕获错误消息
        captured_messages = []
        
        def mock_warning(parent, title, message):
            captured_messages.append((title, message))
            print(f"\n捕获的错误消息:")
            print(f"标题: {title}")
            print(f"内容:\n{message}")
        
        # 使用mock替换QMessageBox.warning
        with patch('src.gui.main_window.QMessageBox.warning', side_effect=mock_warning):
            # 尝试开始融合（应该失败并显示详细错误）
            main_window.start_fusion()
            
            # 尝试生成预览（应该失败并显示详细错误）
            main_window.generate_preview()
        
        print(f"\n捕获了 {len(captured_messages)} 个错误消息")
        
        print("\n✅ 测试场景4完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试场景4失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 增强错误诊断功能测试 ===\n")
    
    tests = [
        ("没有加载视频", test_no_videos_loaded),
        ("部分视频加载", test_partial_videos_loaded),
        ("完整视频加载", test_both_videos_loaded),
        ("错误消息生成", test_error_message_generation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有增强错误诊断功能测试通过！")
        print("现在用户将看到详细的错误诊断信息。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
