#!/usr/bin/env python3
"""
调试用户工作流程
Debug User Workflow
"""

import sys
import os
import tempfile
import cv2
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_video(file_path: str, duration_seconds: int = 3, fps: int = 30):
    """创建测试视频文件"""
    try:
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(file_path, fourcc, fps, (640, 480))
        
        total_frames = duration_seconds * fps
        
        for i in range(total_frames):
            # 创建彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 根据帧号改变颜色
            color_intensity = int((i / total_frames) * 255)
            frame[:, :] = [color_intensity, 100, 255 - color_intensity]
            
            # 添加帧号文本
            cv2.putText(frame, f"Frame {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 测试视频创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频创建失败: {e}")
        return False

def simulate_user_workflow():
    """模拟用户工作流程"""
    try:
        print("=== 模拟用户工作流程 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        print("1. 创建主窗口...")
        main_window = MainWindow()
        
        print(f"主窗口创建完成")
        print(f"初始状态检查:")
        print(f"  - current_video_a_path: {main_window.current_video_a_path}")
        print(f"  - current_video_b_path: {main_window.current_video_b_path}")
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_a_path = tmp_file.name
        
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_b_path = tmp_file.name
        
        if not create_test_video(test_video_a_path, 2, 30):
            return False
        
        if not create_test_video(test_video_b_path, 3, 30):
            return False
        
        try:
            print(f"\n2. 用户加载A视频...")
            print(f"   文件路径: {test_video_a_path}")
            
            # 模拟用户点击加载A视频
            main_window.load_video_a_file(test_video_a_path)
            
            print(f"   加载后状态:")
            print(f"   - current_video_a_path: {main_window.current_video_a_path}")
            print(f"   - video_a_loader.is_loaded(): {main_window.video_a_loader.is_loaded()}")
            
            print(f"\n3. 用户加载B视频...")
            print(f"   文件路径: {test_video_b_path}")
            
            # 模拟用户点击加载B视频
            main_window.load_video_b_file(test_video_b_path)
            
            print(f"   加载后状态:")
            print(f"   - current_video_b_path: {main_window.current_video_b_path}")
            print(f"   - video_b_loader.is_loaded(): {main_window.video_b_loader.is_loaded()}")
            
            print(f"\n4. 检查融合条件...")
            
            # 检查融合条件
            can_start_fusion = (bool(main_window.current_video_a_path) and
                               bool(main_window.current_video_b_path) and
                               not main_window.is_fusion_running)
            
            print(f"   融合条件:")
            print(f"   - bool(current_video_a_path): {bool(main_window.current_video_a_path)}")
            print(f"   - bool(current_video_b_path): {bool(main_window.current_video_b_path)}")
            print(f"   - not is_fusion_running: {not main_window.is_fusion_running}")
            print(f"   - 可以开始融合: {can_start_fusion}")
            
            print(f"\n5. 模拟用户点击'生成预览'...")
            
            # 直接调用generate_preview方法
            try:
                # 检查方法内部的条件
                if not main_window.current_video_a_path or not main_window.current_video_b_path:
                    print("   ❌ generate_preview检查失败: 请先加载A视频和B视频")
                    print(f"      current_video_a_path: '{main_window.current_video_a_path}'")
                    print(f"      current_video_b_path: '{main_window.current_video_b_path}'")
                    return False
                else:
                    print("   ✅ generate_preview检查通过")
                    # 这里不实际执行预览生成，只检查条件
                    
            except Exception as e:
                print(f"   ❌ generate_preview调用异常: {e}")
                return False
            
            print(f"\n6. 模拟用户点击'开始融合'...")
            
            # 直接调用start_fusion方法
            try:
                # 检查方法内部的条件
                if not main_window.current_video_a_path or not main_window.current_video_b_path:
                    print("   ❌ start_fusion检查失败: 请先加载A视频和B视频")
                    print(f"      current_video_a_path: '{main_window.current_video_a_path}'")
                    print(f"      current_video_b_path: '{main_window.current_video_b_path}'")
                    return False
                else:
                    print("   ✅ start_fusion检查通过")
                    # 这里不实际执行融合，只检查条件
                    
            except Exception as e:
                print(f"   ❌ start_fusion调用异常: {e}")
                return False
            
            print(f"\n7. 测试UI状态更新...")
            
            # 调用update_ui_state方法
            try:
                main_window.update_ui_state()
                print("   ✅ UI状态更新成功")
            except Exception as e:
                print(f"   ❌ UI状态更新失败: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            print(f"\n8. 最终状态检查...")
            print(f"   - current_video_a_path: {main_window.current_video_a_path}")
            print(f"   - current_video_b_path: {main_window.current_video_b_path}")
            print(f"   - is_fusion_running: {main_window.is_fusion_running}")
            
            # 再次检查融合条件
            final_can_fusion = (bool(main_window.current_video_a_path) and
                               bool(main_window.current_video_b_path) and
                               not main_window.is_fusion_running)
            
            print(f"   - 最终可以融合: {final_can_fusion}")
            
            if final_can_fusion:
                print("\n🎉 用户工作流程模拟成功！应该可以正常进行融合。")
                return True
            else:
                print("\n❌ 用户工作流程模拟失败！发现问题。")
                return False
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_a_path)
                os.unlink(test_video_b_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 用户工作流程模拟异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_attribute_existence():
    """测试属性存在性"""
    try:
        print("\n=== 测试属性存在性 ===")
        
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 检查关键属性
        attributes_to_check = [
            'current_video_a_path',
            'current_video_b_path',
            'is_fusion_running',
            'video_a_loader',
            'video_b_loader',
            'video_a_player',
            'video_b_player',
            'fusion_engine'
        ]
        
        print("检查关键属性:")
        all_exist = True
        for attr in attributes_to_check:
            exists = hasattr(main_window, attr)
            print(f"  - {attr}: {'✅' if exists else '❌'}")
            if not exists:
                all_exist = False
        
        # 检查方法
        methods_to_check = [
            'load_video_a_file',
            'load_video_b_file',
            'start_fusion',
            'generate_preview',
            'update_ui_state'
        ]
        
        print("检查关键方法:")
        for method in methods_to_check:
            exists = hasattr(main_window, method)
            print(f"  - {method}: {'✅' if exists else '❌'}")
            if not exists:
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ 属性检查异常: {e}")
        return False

def main():
    """主函数"""
    print("=== 用户工作流程深度调试 ===\n")
    
    # 首先检查属性存在性
    attr_result = test_attribute_existence()
    print(f"属性检查结果: {attr_result}\n")
    
    if not attr_result:
        print("❌ 关键属性或方法缺失，无法继续测试")
        return False
    
    # 然后模拟用户工作流程
    workflow_result = simulate_user_workflow()
    
    if workflow_result:
        print("\n🎉 所有测试通过！用户应该可以正常使用融合功能。")
        print("如果用户仍然遇到问题，可能是以下原因：")
        print("1. 用户使用的视频文件格式不支持")
        print("2. 用户的操作顺序与测试不同")
        print("3. GUI事件处理中存在问题")
        print("4. 实际的UI按钮连接有问题")
    else:
        print("\n❌ 发现问题！需要进一步调查。")
    
    return workflow_result

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
