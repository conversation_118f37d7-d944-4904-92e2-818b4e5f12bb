2025-06-28 09:05:00,555 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:05:00,556 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:05:00,556 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:05:00,556 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:05:00,557 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:05:00,557 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:05:00,557 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:05:00,949 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:05:00,949 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:05:00,949 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:05:00,949 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:05:00,965 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:05:00,966 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:05:00,966 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:05:27,192 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:05:27,245 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:05:27,245 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:05:27,259 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:05:27,259 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:05:27,259 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:08:13,947 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:08:13,947 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:08:13,947 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:08:13,947 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:08:13,948 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:08:13,948 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:08:13,948 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:08:14,325 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:08:14,325 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:08:14,326 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:08:14,326 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:08:14,348 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:08:14,348 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:08:14,348 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:08:22,968 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:08:22,984 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:08:22,985 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:08:22,999 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:08:23,000 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:08:23,000 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:08:23,000 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:08:36,665 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:08:36,691 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:08:36,691 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:08:36,714 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:08:36,714 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:08:36,714 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:08:36,714 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:08:56,872 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:08:56,872 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:08:56,873 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:08:56,873 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:08:56,873 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:08:56,873 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:08:56,917 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:08:56,918 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:08:58,978 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:09:06,241 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-28 09:09:06,241 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:09:06,241 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:09:08,172 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:09:08,172 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:09:08,172 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:09:08,173 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:09:08,173 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:09:08,173 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:09:08,182 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:09:08,183 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:09:10,762 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:09:13,041 - src.gui.control_panel - INFO - 融合类型改变: 插入融合 (Insertion)
2025-06-28 09:09:13,041 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:09:13,041 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:10:29,558 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:10:29,558 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:10:29,583 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:10:29,583 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:10:29,591 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:10:29,591 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:10:29,592 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:10:29,592 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:10:29,592 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:10:29,592 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:10:29,593 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:10:29,605 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:10:29,605 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:10:29,605 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:10:29,629 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:10:29,630 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:10:29,630 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:10:29,644 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:10:29,645 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:10:29,665 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-28 09:10:29,665 - src.effects.edge_detector - ERROR - 不支持的边缘检测方法: canny
2025-06-28 09:10:29,666 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-28 09:10:29,672 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:10:29,673 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:10:29,673 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:10:29,673 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:10:29,673 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:10:29,674 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:10:29,674 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:10:29,684 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:10:29,684 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:10:29,684 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:10:29,706 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:10:29,707 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:10:29,707 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:11:38,543 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:11:38,544 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:11:38,568 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:11:38,568 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:11:38,577 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:11:38,577 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:11:38,577 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:11:38,578 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:11:38,578 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:11:38,578 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:11:38,578 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:11:38,587 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:11:38,587 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:11:38,588 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:11:38,608 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:11:38,609 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:11:38,609 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:11:38,625 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:11:38,625 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:11:38,644 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-28 09:11:38,660 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-28 09:11:38,662 - src.effects.histogram_matcher - ERROR - 直方图匹配失败: match_histograms() got an unexpected keyword argument 'multichannel'
2025-06-28 09:11:38,662 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-28 09:11:38,667 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:11:38,667 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:11:38,667 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:11:38,667 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:11:38,668 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:11:38,668 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:11:38,668 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:11:38,678 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:11:38,679 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:11:38,679 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:11:38,697 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:11:38,698 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:11:38,698 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:13:34,839 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:13:34,839 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:13:34,840 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:13:34,840 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:13:34,840 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:13:34,840 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:13:34,840 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:13:35,174 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:13:35,174 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:13:35,174 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:13:35,174 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:13:35,190 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:13:35,191 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:13:35,191 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:13:42,356 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:13:42,373 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:13:42,373 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:13:42,387 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:13:42,387 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:13:42,387 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:13:42,388 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:13:47,412 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:13:47,436 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:13:47,436 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:13:47,458 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:13:47,458 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:13:47,458 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:13:47,458 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:13:49,271 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:13:49,271 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:13:49,271 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:13:49,271 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:13:49,271 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:13:49,271 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:13:49,294 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:13:49,295 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:13:51,043 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:14:03,570 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:14:03,571 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:14:03,572 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:14:03,572 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:14:03,583 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:14:03,584 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:14:31,888 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:14:31,889 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:01,152 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:15:01,152 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:01,970 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:15:01,971 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:01,971 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:01,972 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:01,972 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:01,972 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:01,982 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:01,983 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:15:02,121 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:15:02,121 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:02,122 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:02,123 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:02,123 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:02,123 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:02,133 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:02,134 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:15:03,659 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:15:04,226 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:15:05,837 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:15:05,837 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:05,838 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:05,839 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:05,839 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:05,839 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:05,848 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:05,849 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:15:07,311 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:15:23,120 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:15:23,121 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:23,122 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:23,122 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:23,122 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:23,123 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:23,132 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:23,132 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:15:26,398 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:15:29,404 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:15:29,404 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:15:29,405 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:29,405 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:29,406 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:29,406 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:29,416 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:29,417 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:15:32,063 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:15:35,119 - src.gui.main_window - INFO - 开始融合处理...
2025-06-28 09:15:35,120 - src.fusion.fusion_engine - ERROR - 融合参数验证失败: 未设置插入位置
2025-06-28 09:15:35,194 - src.gui.main_window - ERROR - 融合处理失败
2025-06-28 09:15:49,568 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:49,569 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:49,570 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:49,570 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:49,579 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:49,579 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:15:55,218 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:15:55,220 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:15:55,220 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:15:55,220 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:15:55,228 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:15:55,229 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:16:01,022 - src.gui.main_window - INFO - 应用程序退出
2025-06-28 09:17:55,214 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:17:55,215 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:17:55,215 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:17:55,215 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:17:55,215 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:17:55,216 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:17:55,216 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:17:55,671 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:17:55,672 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:17:55,672 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:17:55,672 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:17:55,690 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:17:55,690 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:17:55,691 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:18:44,678 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:18:44,731 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:18:44,731 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:18:44,745 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:18:44,745 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:18:44,745 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:18:44,745 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:18:49,358 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:18:49,385 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:18:49,385 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:18:49,406 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:18:49,406 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:18:49,406 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:18:49,406 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:18:50,803 - src.gui.main_window - WARNING - 设置默认插入位置失败: __init__() got an unexpected keyword argument 'x'
2025-06-28 09:18:50,804 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:18:50,804 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:18:50,804 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:18:50,804 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:18:50,804 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:18:50,804 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:18:50,850 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:18:50,850 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:18:53,361 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:19:05,338 - src.gui.preset_manager - INFO - 加载了 0 个预设
2025-06-28 09:19:37,038 - src.gui.main_window - WARNING - 设置默认插入位置失败: __init__() got an unexpected keyword argument 'x'
2025-06-28 09:19:37,038 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:19:37,038 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:19:38,975 - src.gui.main_window - WARNING - 设置默认插入位置失败: __init__() got an unexpected keyword argument 'x'
2025-06-28 09:19:38,975 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:19:38,976 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:19:38,976 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:19:38,976 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:19:38,976 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:19:38,976 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:19:38,986 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 09:19:38,987 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:19:40,743 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:11,314 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:20:11,314 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:20:11,315 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:20:11,315 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:20:11,315 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:20:11,315 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:20:11,316 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:20:11,732 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:20:11,732 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:20:11,732 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:20:11,732 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:20:11,752 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:20:11,752 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:20:11,752 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:20:22,652 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:20:22,667 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:20:22,667 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:22,681 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:20:22,681 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:22,681 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:20:22,681 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:20:27,188 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:20:27,213 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:20:27,214 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:27,235 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:20:27,235 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:27,235 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:20:27,235 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:20:29,287 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:29,287 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:29,287 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:29,287 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:29,287 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:29,288 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:29,288 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:29,713 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:29,725 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:29,729 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:33,270 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:33,270 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:33,270 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:33,271 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:33,271 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:33,271 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:33,271 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:33,659 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:33,668 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:33,673 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:34,187 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:34,187 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:34,187 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:34,187 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:34,187 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:34,187 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:34,188 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:34,566 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:34,576 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:34,582 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:34,753 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:34,754 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:34,754 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:34,754 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:34,754 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:34,754 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:34,754 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:35,107 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:35,115 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:35,120 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:35,370 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:35,370 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:35,370 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:35,370 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:35,370 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:35,370 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:35,371 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:35,751 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:35,762 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:35,766 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:35,870 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:35,871 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:35,871 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:35,871 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:35,871 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:35,871 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:35,871 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:36,238 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:36,249 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:36,254 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:36,263 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:36,263 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:36,263 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:36,263 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:36,263 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:36,264 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:36,264 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:36,690 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:36,700 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:36,704 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:36,710 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:36,710 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:36,710 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:36,710 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:36,710 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:36,711 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:36,711 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:37,105 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:37,114 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:37,121 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:37,129 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:37,129 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:37,129 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:37,129 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:37,129 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:37,129 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:37,130 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:37,535 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:37,540 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:37,545 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:37,553 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:37,554 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:37,554 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:37,554 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:37,554 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:37,554 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:37,554 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:37,949 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:37,954 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:37,957 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:37,963 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:37,963 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:37,964 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:37,964 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:37,964 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:37,964 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:37,964 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:38,330 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:38,340 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:38,344 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:20:38,353 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:20:38,353 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:20:38,353 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:20:38,353 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:20:38,353 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:20:38,354 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:20:38,354 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:20:38,719 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:20:38,726 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:20:38,731 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:21:49,982 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:21:49,982 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:21:49,983 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:21:49,983 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:21:49,984 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:21:49,984 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:21:49,985 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:21:50,004 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:21:50,005 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:21:50,005 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:21:50,030 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:21:50,031 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:21:50,031 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:21:50,032 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.8, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 600, 'duration': 1}, {'frame': 900, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:21:50,032 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:21:50,032 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:21:50,033 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:21:50,518 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:21:50,608 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:21:50,608 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:21:50,608 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:21:50,609 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:21:50,609 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:21:50,609 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:21:50,609 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:21:50,621 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:21:50,621 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:21:50,621 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:21:50,640 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:21:50,641 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:21:50,641 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:21:50,641 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.8, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 600, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:23:56,552 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:23:56,553 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:23:56,553 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:23:56,553 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:23:56,553 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:23:56,554 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:23:56,554 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:23:56,606 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:23:56,607 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:23:56,607 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:23:56,633 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:23:56,633 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:23:56,633 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:23:56,633 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.8, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 600, 'duration': 1}, {'frame': 900, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:23:56,633 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:23:56,634 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:23:56,634 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:23:57,067 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:23:57,141 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:23:57,141 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:23:57,141 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:23:57,141 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:23:57,142 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:23:57,142 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:23:57,142 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:23:57,151 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:23:57,151 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:23:57,152 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:23:57,171 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:23:57,171 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:23:57,171 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:23:57,172 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.8, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 600, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:24:12,882 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:24:12,883 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:24:12,883 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:24:12,883 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:24:12,883 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:24:12,884 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:24:12,884 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:24:13,263 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:24:13,264 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:24:13,264 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:24:13,264 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:24:13,280 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:24:13,281 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:24:13,281 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:30:27,300 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:30:27,300 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:30:27,301 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:30:27,301 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:30:27,301 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:30:27,301 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:30:27,301 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:30:27,641 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:30:27,642 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:30:27,642 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:30:27,642 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:30:27,650 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:30:27,650 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:30:27,651 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:31:30,037 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:31:30,037 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:31:30,037 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:31:30,038 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:31:30,038 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:31:30,038 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:31:30,038 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:31:30,375 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:31:30,376 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:31:30,376 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:31:30,376 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:31:30,384 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:31:30,385 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:31:30,385 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:31:45,214 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:31:45,214 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:31:45,214 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:31:45,214 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:31:45,215 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:31:45,215 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:31:45,215 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:31:45,552 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 09:31:45,553 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 09:31:45,553 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 09:31:45,553 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 09:31:45,561 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 09:31:45,562 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 09:31:45,562 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 09:32:50,379 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:32:50,399 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:32:50,399 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:32:50,417 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:32:50,418 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:32:50,418 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:32:50,418 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 09:32:55,042 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:32:55,065 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:32:55,066 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:32:55,087 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:32:55,087 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:32:55,088 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:32:55,088 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 09:32:57,291 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:32:57,291 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:32:57,291 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:32:57,291 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 09:32:57,291 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:32:57,292 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:32:57,292 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:32:57,292 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:32:57,292 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:32:57,292 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:32:57,722 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:32:57,735 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:32:58,109 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 09:33:11,257 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:33:11,258 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:33:11,258 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:33:11,258 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:33:11,630 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 09:33:11,641 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 09:33:33,943 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-28 09:33:33,943 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:33:33,943 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:33:33,943 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:33:33,943 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:33:36,158 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:33:36,158 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:33:36,158 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:33:36,158 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 09:33:36,158 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 09:33:36,159 - src.fusion.fusion_engine - WARNING - 暂不支持 FusionType.OVERLAY 类型的预览
2025-06-28 09:33:36,159 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 09:35:08,061 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 09:35:08,062 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 09:35:08,062 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 09:35:08,062 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 09:35:08,063 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 09:35:08,063 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 09:35:08,063 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 09:35:08,081 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:35:08,082 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:35:08,082 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 09:35:08,107 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 09:35:08,107 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:35:08,107 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 09:35:08,107 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.8, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 600, 'duration': 1}, {'frame': 900, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 09:35:08,107 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 09:35:08,107 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 09:35:08,108 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 09:35:08,633 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 11:59:43,767 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 11:59:43,768 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 11:59:43,768 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 11:59:43,768 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 11:59:43,768 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 11:59:43,769 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 11:59:43,769 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 11:59:44,167 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 11:59:44,168 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 11:59:44,168 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 11:59:44,168 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 11:59:44,184 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 11:59:44,185 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 11:59:44,185 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 12:00:02,068 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 12:00:02,122 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 12:00:02,122 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 12:00:02,136 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 12:00:02,136 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 12:00:02,136 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 12:00:02,136 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 12:00:06,919 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 12:00:06,944 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 12:00:06,945 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 12:00:06,965 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 12:00:06,965 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 12:00:06,965 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 12:00:06,965 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 12:00:09,167 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 12:00:09,167 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:00:09,167 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:00:09,167 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 12:00:09,167 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:00:09,168 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:00:09,168 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 12:00:09,168 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 12:00:09,168 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 12:00:09,168 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 12:00:09,614 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 12:00:09,627 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 12:00:10,016 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 12:01:09,415 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:01:09,416 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:01:09,416 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:01:09,416 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:01:09,416 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:01:09,416 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:01:09,417 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:01:09,766 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 12:01:09,767 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 12:01:09,767 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 12:01:09,767 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 12:01:09,783 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 12:01:09,783 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 12:01:09,783 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 12:01:12,679 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:01:12,680 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:01:12,680 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:01:12,680 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:01:14,590 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 12:02:07,849 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:02:07,850 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:02:07,850 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:02:07,850 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:02:07,851 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:02:07,851 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:02:07,851 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:02:08,214 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 12:02:08,215 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 12:02:08,216 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 12:02:08,216 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 12:02:08,240 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 12:02:08,240 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 12:02:08,240 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 12:02:41,392 - src.gui.preset_manager - INFO - 加载了 0 个预设
2025-06-28 12:03:04,894 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:04,894 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:04,894 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:04,894 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:10,090 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:10,091 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:10,091 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:10,091 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:10,757 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:10,757 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:10,757 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:10,757 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:31,974 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:31,974 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:31,974 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:31,974 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:33,607 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:33,607 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:33,607 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:33,607 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:39,157 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:39,157 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:03:39,157 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:03:39,157 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 12:04:00,509 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:04:00,510 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:04:00,510 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:04:00,510 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:04:00,510 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:04:00,510 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:04:00,510 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:04:00,870 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 12:04:00,870 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 12:04:00,871 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 12:04:00,871 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 12:04:00,902 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 12:04:00,902 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 12:04:00,902 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 12:04:58,213 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 12:04:58,299 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:04:58,299 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:04:58,300 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:04:58,300 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:04:58,300 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:04:58,301 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:04:58,301 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:04:58,637 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 12:04:58,638 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:04:58,638 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:05:20,174 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:05:20,174 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:05:20,175 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:05:20,175 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:05:20,175 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:05:20,175 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:05:20,175 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:05:20,553 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 12:05:20,554 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 12:05:20,554 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 12:05:20,554 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 12:05:20,577 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 12:05:20,577 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 12:05:20,577 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 12:07:42,365 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:07:42,366 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:07:42,367 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:07:42,367 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:07:42,368 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:07:42,368 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:07:42,368 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:07:42,391 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 12:07:42,391 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 12:07:42,391 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 12:07:42,415 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 12:07:42,415 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 12:07:42,415 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 12:07:42,415 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.8, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 600, 'duration': 1}, {'frame': 900, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 12:07:42,415 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 12:07:42,416 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 12:07:42,416 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 12:07:42,882 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 12:07:43,603 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 12:07:43,682 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 12:07:43,682 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 12:07:43,682 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 12:07:43,682 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 12:07:43,682 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 12:07:43,682 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 12:07:43,682 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 12:07:43,988 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 12:07:43,988 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,988 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,989 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,989 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,989 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,989 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,989 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,990 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,990 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,990 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,990 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,990 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,990 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,991 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,991 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,991 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,991 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,991 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,991 - src.gui.main_window - INFO - 预览已清除
2025-06-28 12:07:43,992 - src.gui.main_window - INFO - 预览已清除
2025-06-28 13:01:10,029 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:01:10,029 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:01:10,030 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:01:10,030 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:01:10,030 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:01:10,030 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:01:10,030 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:01:10,402 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:01:10,403 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 13:01:10,403 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 13:01:10,403 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 13:01:10,419 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 13:01:10,420 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 13:01:10,420 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 13:01:21,617 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 13:01:21,636 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 13:01:21,636 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:01:21,651 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 13:01:21,651 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:01:21,651 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 13:01:21,652 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 13:01:26,345 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 13:01:26,369 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 13:01:26,370 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:01:26,390 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 13:01:26,390 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:01:26,391 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 13:01:26,391 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 13:01:47,754 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-28 13:01:47,754 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:47,754 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:47,755 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:47,755 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:56,233 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:56,233 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:56,233 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:56,233 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:57,416 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:57,416 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:57,417 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:57,417 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:58,200 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:58,200 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:01:58,200 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:01:58,200 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:02:00,368 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 13:02:00,368 - src.fusion.fusion_engine - WARNING - 暂不支持 FusionType.BLEND 类型的预览
2025-06-28 13:02:00,368 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 13:02:04,925 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 13:02:08,318 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 13:02:08,318 - src.fusion.fusion_engine - WARNING - 暂不支持 FusionType.BLEND 类型的预览
2025-06-28 13:02:08,318 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 13:02:09,908 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 13:02:13,583 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:02:13,583 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:02:13,583 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:02:13,584 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:02:16,152 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:02:16,152 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:02:16,152 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:02:16,153 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:02:32,256 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 13:02:32,256 - src.fusion.fusion_engine - WARNING - 暂不支持 FusionType.BLEND 类型的预览
2025-06-28 13:02:32,256 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 13:02:38,626 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 13:05:51,117 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:05:51,117 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:05:51,118 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:05:51,118 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:05:51,118 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:05:51,118 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:05:51,118 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:05:51,136 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,136 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:05:51,136 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,158 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,159 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:05:51,159 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,159 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:05:51,159 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:05:51,159 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-28 13:05:51,241 - src.fusion.blend_fusion - ERROR - 生成混合预览失败: 'BlendFusion' object has no attribute 'blend_frames'
2025-06-28 13:05:51,257 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:05:51,257 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:05:51,257 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:05:51,257 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:05:51,257 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:05:51,257 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:05:51,257 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:05:51,269 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,269 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:05:51,269 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,288 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,288 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:05:51,288 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,288 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.7, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:05:51,289 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:05:51,289 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-28 13:05:51,443 - src.fusion.overlay_fusion - ERROR - 生成叠加预览失败: 'OverlayFusion' object has no attribute 'overlay_frames'
2025-06-28 13:05:51,459 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:05:51,460 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:05:51,460 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:05:51,461 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:05:51,461 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:05:51,461 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:05:51,461 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:05:51,471 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,472 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:05:51,472 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,488 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,488 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:05:51,488 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,489 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [{'frame': 100, 'duration': 1}, {'frame': 200, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:05:51,489 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 13:05:51,489 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:05:51,489 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:05:51,945 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 2
2025-06-28 13:05:51,946 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:05:51,946 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:05:51,947 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:05:51,947 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:05:51,947 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:05:51,947 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:05:51,947 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:05:51,973 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,973 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:05:51,974 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:51,994 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,994 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:05:51,994 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:51,994 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:05:51,994 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:05:51,994 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 2
2025-06-28 13:05:52,054 - src.fusion.blend_fusion - ERROR - 生成混合预览失败: 'BlendFusion' object has no attribute 'blend_frames'
2025-06-28 13:05:52,056 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:05:52,056 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:05:52,056 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:05:52,056 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:05:52,056 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:05:52,056 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:05:52,056 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:05:52,086 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:52,087 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:05:52,087 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:05:52,105 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:52,105 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:05:52,105 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:05:52,105 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:05:52,105 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:05:52,105 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 2
2025-06-28 13:05:52,168 - src.fusion.overlay_fusion - ERROR - 生成叠加预览失败: 'OverlayFusion' object has no attribute 'overlay_frames'
2025-06-28 13:06:49,228 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:06:49,228 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:06:49,229 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:06:49,229 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:06:49,229 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:06:49,229 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:06:49,229 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:06:49,249 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:49,249 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:06:49,250 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:49,274 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:49,274 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:06:49,275 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:49,275 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:06:49,275 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:06:49,275 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-28 13:06:49,358 - src.fusion.blend_fusion - ERROR - 生成混合预览失败: 'BlendFusion' object has no attribute 'blend_frames'
2025-06-28 13:06:49,375 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:06:49,375 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:06:49,375 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:06:49,376 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:06:49,376 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:06:49,376 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:06:49,376 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:06:49,387 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:49,387 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:06:49,388 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:49,406 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:49,406 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:06:49,407 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:49,407 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.7, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:06:49,407 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:06:49,407 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-28 13:06:49,477 - src.fusion.overlay_fusion - ERROR - 生成叠加预览失败: 'OverlayFusion' object has no attribute 'overlay_frames'
2025-06-28 13:06:49,494 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:06:49,494 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:06:49,494 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:06:49,495 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:06:49,495 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:06:49,495 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:06:49,495 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:06:49,505 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:49,505 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:06:49,505 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:49,527 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:49,527 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:06:49,527 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:49,527 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [{'frame': 100, 'duration': 1}, {'frame': 200, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:06:49,528 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 13:06:49,528 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:06:49,528 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:06:49,993 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 2
2025-06-28 13:06:49,994 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:06:49,995 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:06:49,995 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:06:49,995 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:06:49,995 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:06:49,995 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:06:49,996 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:06:50,025 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:50,026 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:06:50,026 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:50,044 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:50,045 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:06:50,045 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:50,045 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:06:50,045 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:06:50,045 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 2
2025-06-28 13:06:50,110 - src.fusion.blend_fusion - ERROR - 生成混合预览失败: 'BlendFusion' object has no attribute 'blend_frames'
2025-06-28 13:06:50,112 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:06:50,112 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:06:50,112 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:06:50,113 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:06:50,113 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:06:50,113 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:06:50,113 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:06:50,148 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:50,149 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:06:50,149 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:06:50,168 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:50,169 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:06:50,169 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:06:50,169 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:06:50,169 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:06:50,169 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 2
2025-06-28 13:06:50,239 - src.fusion.overlay_fusion - ERROR - 生成叠加预览失败: 'OverlayFusion' object has no attribute 'overlay_frames'
2025-06-28 13:08:01,466 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:01,467 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:01,467 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:01,467 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:01,468 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:01,468 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:01,468 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:01,488 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:01,488 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:01,488 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:01,514 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:01,514 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:01,514 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:01,514 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:01,515 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:08:01,515 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-28 13:08:01,604 - src.fusion.blend_fusion - ERROR - 生成混合预览失败: 'BlendFusion' object has no attribute 'alpha'
2025-06-28 13:08:01,621 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:01,621 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:01,621 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:01,621 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:01,621 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:01,622 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:01,622 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:01,633 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:01,634 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:01,634 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:01,657 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:01,657 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:01,657 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:01,657 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.7, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:01,658 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:08:01,658 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-28 13:08:01,734 - src.fusion.overlay_fusion - ERROR - 生成叠加预览失败: 'OverlayFusion' object has no attribute 'alpha'
2025-06-28 13:08:01,748 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:01,748 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:01,748 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:01,749 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:01,749 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:01,749 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:01,749 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:01,758 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:01,759 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:01,759 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:01,778 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:01,779 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:01,779 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:01,779 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [{'frame': 100, 'duration': 1}, {'frame': 200, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:01,779 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 13:08:01,779 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:01,780 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:02,223 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 2
2025-06-28 13:08:02,225 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:02,225 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:02,225 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:02,225 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:02,225 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:02,225 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:02,225 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:02,257 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:02,257 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:02,257 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:02,279 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:02,279 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:02,279 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:02,280 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:02,280 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:08:02,280 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 2
2025-06-28 13:08:02,343 - src.fusion.blend_fusion - ERROR - 生成混合预览失败: 'BlendFusion' object has no attribute 'alpha'
2025-06-28 13:08:02,346 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:02,346 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:02,346 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:02,347 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:02,347 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:02,347 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:02,347 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:02,377 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:02,377 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:02,377 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:02,396 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:02,396 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:02,397 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:02,397 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:02,397 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:08:02,397 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 2
2025-06-28 13:08:02,463 - src.fusion.overlay_fusion - ERROR - 生成叠加预览失败: 'OverlayFusion' object has no attribute 'alpha'
2025-06-28 13:08:58,939 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:58,939 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:58,940 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:58,940 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:58,940 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:58,940 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:58,941 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:58,959 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:58,959 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:58,959 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:58,982 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:58,982 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:58,983 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:58,983 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:58,983 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:08:58,983 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-28 13:08:59,301 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-28 13:08:59,330 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:59,330 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:59,330 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:59,330 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:59,331 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:59,331 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:59,332 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:59,342 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:59,342 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:59,342 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:59,361 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:59,361 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:59,362 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:59,362 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.7, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:59,363 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:08:59,363 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-28 13:08:59,707 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-28 13:08:59,739 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:08:59,739 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:08:59,739 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:08:59,740 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:08:59,740 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:08:59,740 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:08:59,740 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:08:59,752 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:59,752 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:59,753 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:08:59,770 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:59,771 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:08:59,771 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:08:59,771 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [{'frame': 100, 'duration': 1}, {'frame': 200, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:08:59,771 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 13:08:59,771 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:08:59,771 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:09:00,220 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 2
2025-06-28 13:09:00,222 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:09:00,222 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:09:00,222 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:09:00,222 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:09:00,222 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:09:00,222 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:09:00,222 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:09:00,252 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:09:00,253 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:09:00,253 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:09:00,271 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:09:00,271 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:09:00,272 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:09:00,272 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:09:00,272 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:09:00,272 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 2
2025-06-28 13:09:00,414 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 2 帧
2025-06-28 13:09:00,416 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:09:00,417 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:09:00,417 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:09:00,417 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:09:00,417 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:09:00,417 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:09:00,417 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:09:00,450 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:09:00,450 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:09:00,450 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:09:00,469 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:09:00,469 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:09:00,469 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:09:00,469 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:09:00,470 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:09:00,470 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 2
2025-06-28 13:09:00,622 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 2 帧
2025-06-28 13:09:28,823 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:09:28,823 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:09:28,823 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:09:28,823 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:09:28,824 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:09:28,824 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:09:28,824 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:09:29,201 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:09:29,201 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 13:09:29,201 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 13:09:29,201 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 13:09:29,224 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 13:09:29,224 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 13:09:29,224 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 13:11:56,989 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:11:56,990 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:11:56,990 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:11:56,990 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:11:56,991 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:11:56,991 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:11:56,991 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:11:57,327 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:11:57,344 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:11:57,344 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:11:57,344 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:11:57,365 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:11:57,365 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:11:57,365 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:11:57,366 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-28 13:11:57,366 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:11:57,366 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:11:57,366 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:11:57,366 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:11:57,441 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:11:57,441 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:11:57,441 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:11:57,441 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:11:57,441 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:11:57,441 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:11:57,441 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:11:57,591 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:11:57,600 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:11:57,600 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:11:57,600 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:11:57,623 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:11:57,623 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:11:57,623 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:11:57,624 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-28 13:11:57,624 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:11:57,624 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:11:57,624 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:11:57,624 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:11:57,625 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-28 13:11:57,625 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:11:57,625 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:11:57,625 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:11:57,625 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:12:41,151 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:12:41,151 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:12:41,151 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:12:41,151 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:12:41,152 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:12:41,152 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:12:41,152 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:12:41,485 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:12:41,501 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:12:41,501 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:12:41,501 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:12:41,523 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:12:41,523 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:12:41,524 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:12:41,524 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-28 13:12:41,524 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:12:41,524 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:12:41,524 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:12:41,525 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:13:46,323 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:13:46,323 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-28 13:13:46,593 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-28 13:13:46,688 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:13:46,688 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:13:46,688 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:13:46,688 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:13:46,688 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:13:46,688 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:13:46,688 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:13:46,838 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:13:46,847 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:13:46,848 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 13:13:46,848 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 13:13:46,868 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 13:13:46,868 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 13:13:46,868 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-28 13:13:46,886 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-28 13:13:46,886 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:13:46,886 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:13:46,886 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:13:46,886 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:13:48,213 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 13:13:48,213 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 2
2025-06-28 13:13:48,338 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 2 帧
2025-06-28 13:13:48,339 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-28 13:13:48,339 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:13:48,339 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:13:48,339 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 13:13:48,340 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 13:13:49,629 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 13:13:49,629 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 2
2025-06-28 13:13:49,724 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 2 帧
2025-06-28 13:14:07,485 - src.gui.main_window - INFO - 应用程序退出
2025-06-28 13:14:30,603 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 13:14:30,603 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 13:14:30,604 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 13:14:30,604 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 13:14:30,604 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 13:14:30,604 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 13:14:30,604 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 13:14:30,954 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 13:14:30,954 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 13:14:30,954 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 13:14:30,954 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 13:14:30,970 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 13:14:30,970 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 13:14:30,971 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 15:06:15,505 - src.gui.main_window - INFO - 应用程序退出
2025-06-28 22:22:23,149 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 22:22:23,149 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 22:22:23,150 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-28 22:22:23,150 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-28 22:22:23,150 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-28 22:22:23,151 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-28 22:22:23,151 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-28 22:22:23,658 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-28 22:22:23,658 - __main__ - INFO - 视频融合编辑器启动
2025-06-28 22:22:23,659 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-28 22:22:23,659 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-28 22:22:23,682 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-28 22:22:23,682 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-28 22:22:23,683 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-28 22:22:35,295 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 22:22:35,345 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 22:22:35,345 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:22:35,357 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 22:22:35,358 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:22:35,358 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 22:22:35,358 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-28 22:22:41,353 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 22:22:41,379 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 22:22:41,379 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:22:41,401 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 22:22:41,401 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:22:41,401 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 22:22:41,401 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-28 22:22:48,149 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:22:48,149 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:22:48,149 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:22:48,149 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:22:48,195 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-28 22:22:48,196 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-28 22:22:53,873 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 22:23:00,969 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:00,969 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:00,969 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:00,970 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:00,970 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:00,970 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:01,714 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:01,714 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:01,714 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:01,715 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:01,715 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:01,715 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:03,182 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:23:03,183 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:23:03,183 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:23:03,183 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:23:03,582 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:23:03,591 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:23:04,060 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 22:23:11,904 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:11,905 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:11,905 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:11,905 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:11,905 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:11,905 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:13,231 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:13,231 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:13,231 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:13,231 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:13,231 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:13,231 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:15,032 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:23:15,032 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:23:15,032 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:23:15,032 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:23:15,388 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:23:15,399 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:23:15,420 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 22:23:34,983 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:34,983 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:34,983 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:34,983 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:34,984 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:34,984 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,131 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,131 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,131 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,131 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,132 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,132 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,298 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,298 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,298 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,298 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,299 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,299 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,482 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,482 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,482 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,482 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,482 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,482 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,631 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,631 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,631 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,632 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,632 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,632 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,781 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,782 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,782 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:35,782 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:35,782 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:35,782 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:36,014 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:36,015 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:36,015 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:36,015 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:36,015 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:36,015 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:36,231 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:36,231 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:36,231 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:36,232 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:36,232 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:36,232 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:36,414 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:36,414 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:36,414 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:36,415 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:36,415 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:36,415 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:37,665 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:23:37,665 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:23:37,665 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:23:37,665 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:23:38,012 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:23:38,024 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:23:38,044 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 22:23:42,931 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:42,932 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:42,932 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:42,932 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:42,932 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:42,932 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,081 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,081 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,081 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,082 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,082 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,082 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,248 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,248 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,248 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,248 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,249 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,249 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,414 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,415 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,415 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,415 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,415 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,415 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,564 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,565 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,565 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,565 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,565 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,565 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,714 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,715 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,715 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,715 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,715 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,715 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,848 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,848 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,848 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:43,848 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:43,848 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:43,849 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,014 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,015 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,015 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,015 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,015 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,015 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,164 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,165 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,165 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,165 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,165 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,165 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,315 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,315 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,315 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,315 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,315 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,316 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,464 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,465 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,465 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,465 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,465 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,465 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,631 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,631 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,631 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,631 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,631 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,631 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,781 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,781 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,781 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:44,782 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-28 22:23:44,782 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': '适应', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v'}
2025-06-28 22:23:44,782 - src.gui.main_window - INFO - 融合参数已更新
2025-06-28 22:23:46,215 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:23:46,215 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:23:46,215 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:23:46,216 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:23:46,581 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:23:46,590 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:23:46,610 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 22:24:27,299 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:24:27,299 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:24:27,299 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:24:27,299 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:24:27,660 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:24:27,669 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:24:27,690 - src.gui.control_panel - INFO - 生成融合预览
2025-06-28 22:24:31,428 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:24:31,428 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:24:31,429 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:24:31,429 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:24:31,775 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:24:31,783 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:24:35,515 - src.gui.main_window - INFO - 正在生成预览...
2025-06-28 22:24:35,515 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 22:24:35,515 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 22:24:35,515 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 22:24:35,858 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 22:24:35,864 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-28 22:55:26,891 - __main__ - INFO - 开始测试新的融合参数数据结构
2025-06-28 22:55:26,891 - __main__ - INFO - === 测试融合参数创建 ===
2025-06-28 22:55:26,891 - __main__ - INFO - ✅ 默认融合参数创建成功
2025-06-28 22:55:26,891 - __main__ - INFO - ✅ 五个控制维度参数初始化正确
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 参数修改成功
2025-06-28 22:55:26,892 - __main__ - INFO - === 测试融合参数序列化 ===
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 参数转换为字典成功
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 字典结构验证正确
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 从字典重新创建参数成功
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 参数值验证正确
2025-06-28 22:55:26,892 - __main__ - INFO - === 测试各个控制类 ===
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 时间维度控制类测试通过
2025-06-28 22:55:26,892 - __main__ - INFO - ✅ 空间尺寸控制类测试通过
2025-06-28 22:55:26,893 - __main__ - INFO - ✅ 空间位置控制类测试通过
2025-06-28 22:55:26,893 - __main__ - INFO - ✅ 图像处理控制类测试通过
2025-06-28 22:55:26,893 - __main__ - INFO - ✅ 文字内容控制类测试通过
2025-06-28 22:55:26,893 - __main__ - INFO - 测试完成: 3/3 通过
2025-06-28 22:55:26,893 - __main__ - INFO - 🎉 所有测试通过！新的融合参数数据结构工作正常
2025-06-28 23:34:04,340 - __main__ - INFO - 开始测试时间维度控制算法
2025-06-28 23:34:04,340 - __main__ - INFO - === 测试时间维度控制位置生成 ===
2025-06-28 23:34:04,340 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=uniform
2025-06-28 23:34:04,340 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:34:04,341 - __main__ - INFO - ✅ 均匀分布测试通过: [166, 333, 500, 666, 833]
2025-06-28 23:34:04,341 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=random
2025-06-28 23:34:04,360 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:34:04,374 - __main__ - INFO - ✅ 随机分布测试通过: [81, 85, 479, 797, 853]
2025-06-28 23:34:04,374 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=front_biased
2025-06-28 23:34:04,375 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:34:04,375 - __main__ - INFO - ✅ 前段偏向测试通过: [6, 266, 282, 328, 775], 前1/3段有4个位置
2025-06-28 23:34:04,375 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=middle_biased
2025-06-28 23:34:04,378 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:34:04,378 - __main__ - INFO - ✅ 中段偏向测试通过: [383, 461, 542, 663, 953], 中1/3段有4个位置
2025-06-28 23:34:04,378 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=rear_biased
2025-06-28 23:34:04,396 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:34:04,396 - __main__ - INFO - ✅ 后段偏向测试通过: [488, 725, 780, 858, 952], 后1/3段有4个位置
2025-06-28 23:34:04,397 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=custom
2025-06-28 23:34:04,397 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:34:04,397 - __main__ - INFO - ✅ 自定义时间点测试通过: [100, 300, 500, 700, 900]
2025-06-28 23:34:04,397 - __main__ - INFO - === 测试时间维度控制与实际视频 ===
2025-06-28 23:34:04,414 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:34:04,414 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:34:04,434 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:34:04,434 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:34:04,435 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 23:34:04,435 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:34:04,435 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:34:04,435 - __main__ - INFO - B视频信息: 1202帧
2025-06-28 23:34:04,435 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=3, 分布模式=uniform
2025-06-28 23:34:04,435 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:34:04,436 - __main__ - INFO - 生成的插入位置: [300, 601, 901]
2025-06-28 23:34:04,436 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=3, 分布模式=uniform
2025-06-28 23:34:04,436 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:34:04,436 - src.fusion.insertion_fusion - INFO - 开始直接插入，插入位置数量: 3
2025-06-28 23:34:10,290 - src.fusion.insertion_fusion - INFO - 直接插入完成，结果帧数: 1205
2025-06-28 23:34:10,850 - __main__ - INFO - 时间控制插入完成，结果帧数: 1205
2025-06-28 23:34:10,850 - __main__ - INFO - ✅ 时间控制插入验证通过
2025-06-28 23:34:10,850 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=3, 分布模式=uniform
2025-06-28 23:34:10,850 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:34:11,138 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 23:34:11,138 - __main__ - INFO - 生成预览帧数: 3
2025-06-28 23:34:11,138 - __main__ - INFO - ✅ 时间控制预览生成通过
2025-06-28 23:34:12,014 - __main__ - INFO - === 测试边界情况 ===
2025-06-28 23:34:12,014 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=uniform
2025-06-28 23:34:12,014 - src.fusion.insertion_fusion - INFO - 生成了 0 个插入位置
2025-06-28 23:34:12,014 - __main__ - INFO - ✅ 零帧数测试通过
2025-06-28 23:34:12,014 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=0, 分布模式=uniform
2025-06-28 23:34:12,014 - src.fusion.insertion_fusion - INFO - 生成了 0 个插入位置
2025-06-28 23:34:12,014 - __main__ - INFO - ✅ 零插入次数测试通过
2025-06-28 23:34:12,015 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=1000, 分布模式=uniform
2025-06-28 23:34:12,016 - src.fusion.insertion_fusion - INFO - 生成了 1000 个插入位置
2025-06-28 23:34:12,016 - __main__ - ERROR - 边界情况测试失败: 
2025-06-28 23:34:12,017 - __main__ - ERROR - 测试 test_edge_cases 失败
2025-06-28 23:34:12,017 - __main__ - INFO - 测试完成: 2/3 通过
2025-06-28 23:34:12,017 - __main__ - ERROR - ❌ 部分测试失败
2025-06-28 23:35:16,646 - __main__ - INFO - 开始测试时间维度控制算法
2025-06-28 23:35:16,646 - __main__ - INFO - === 测试时间维度控制位置生成 ===
2025-06-28 23:35:16,647 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=uniform
2025-06-28 23:35:16,647 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:35:16,647 - __main__ - INFO - ✅ 均匀分布测试通过: [166, 333, 500, 666, 833]
2025-06-28 23:35:16,647 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=random
2025-06-28 23:35:16,647 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:35:16,647 - __main__ - INFO - ✅ 随机分布测试通过: [169, 340, 410, 559, 753]
2025-06-28 23:35:16,648 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=front_biased
2025-06-28 23:35:16,648 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:35:16,648 - __main__ - INFO - ✅ 前段偏向测试通过: [38, 59, 286, 315, 355], 前1/3段有4个位置
2025-06-28 23:35:16,648 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=middle_biased
2025-06-28 23:35:16,650 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:35:16,650 - __main__ - INFO - ✅ 中段偏向测试通过: [334, 338, 431, 464, 836], 中1/3段有4个位置
2025-06-28 23:35:16,650 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=rear_biased
2025-06-28 23:35:16,655 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:35:16,655 - __main__ - INFO - ✅ 后段偏向测试通过: [301, 673, 687, 981, 995], 后1/3段有4个位置
2025-06-28 23:35:16,655 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=custom
2025-06-28 23:35:16,655 - src.fusion.insertion_fusion - INFO - 生成了 5 个插入位置
2025-06-28 23:35:16,656 - __main__ - INFO - ✅ 自定义时间点测试通过: [100, 300, 500, 700, 900]
2025-06-28 23:35:16,656 - __main__ - INFO - === 测试时间维度控制与实际视频 ===
2025-06-28 23:35:16,669 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:35:16,670 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:35:16,688 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:35:16,688 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:35:16,688 - __main__ - INFO - B视频信息: 1202帧
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=3, 分布模式=uniform
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:35:16,688 - __main__ - INFO - 生成的插入位置: [300, 601, 901]
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=3, 分布模式=uniform
2025-06-28 23:35:16,688 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:35:16,689 - src.fusion.insertion_fusion - INFO - 开始直接插入，插入位置数量: 3
2025-06-28 23:35:22,479 - src.fusion.insertion_fusion - INFO - 直接插入完成，结果帧数: 1205
2025-06-28 23:35:23,005 - __main__ - INFO - 时间控制插入完成，结果帧数: 1205
2025-06-28 23:35:23,005 - __main__ - INFO - ✅ 时间控制插入验证通过
2025-06-28 23:35:23,005 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=3, 分布模式=uniform
2025-06-28 23:35:23,005 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:35:23,440 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-28 23:35:23,440 - __main__ - INFO - 生成预览帧数: 3
2025-06-28 23:35:23,440 - __main__ - INFO - ✅ 时间控制预览生成通过
2025-06-28 23:35:24,365 - __main__ - INFO - === 测试边界情况 ===
2025-06-28 23:35:24,365 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=5, 分布模式=uniform
2025-06-28 23:35:24,365 - src.fusion.insertion_fusion - INFO - 生成了 0 个插入位置
2025-06-28 23:35:24,365 - __main__ - INFO - ✅ 零帧数测试通过
2025-06-28 23:35:24,365 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=0, 分布模式=uniform
2025-06-28 23:35:24,366 - src.fusion.insertion_fusion - INFO - 生成了 0 个插入位置
2025-06-28 23:35:24,366 - __main__ - INFO - ✅ 零插入次数测试通过
2025-06-28 23:35:24,366 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=1000, 分布模式=uniform
2025-06-28 23:35:24,366 - src.fusion.insertion_fusion - INFO - 生成了 10 个插入位置
2025-06-28 23:35:24,366 - __main__ - INFO - ✅ 插入次数大于总帧数测试通过
2025-06-28 23:35:24,366 - src.fusion.insertion_fusion - INFO - 生成时间控制插入位置: 插入次数=1000, 分布模式=custom
2025-06-28 23:35:24,366 - src.fusion.insertion_fusion - INFO - 生成了 3 个插入位置
2025-06-28 23:35:24,366 - __main__ - INFO - ✅ 无效自定义时间点测试通过
2025-06-28 23:35:24,366 - __main__ - INFO - 测试完成: 3/3 通过
2025-06-28 23:35:24,367 - __main__ - INFO - 🎉 所有测试通过！时间维度控制算法工作正常
2025-06-28 23:40:46,096 - __main__ - INFO - 开始测试空间尺寸控制算法
2025-06-28 23:40:46,096 - __main__ - INFO - === 测试空间尺寸控制参数创建 ===
2025-06-28 23:40:46,097 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-28 23:40:46,097 - __main__ - INFO - ✅ 自定义参数创建成功
2025-06-28 23:40:46,097 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-28 23:40:46,097 - __main__ - INFO - === 测试帧尺寸调整功能 ===
2025-06-28 23:40:46,101 - __main__ - INFO - 测试: 等比例缩放
2025-06-28 23:40:46,106 - __main__ - INFO - ✅ 等比例缩放 测试通过
2025-06-28 23:40:46,106 - __main__ - INFO -    原始尺寸: (480, 640, 3)
2025-06-28 23:40:46,106 - __main__ - INFO -    结果尺寸: (720, 1280, 3)
2025-06-28 23:40:46,106 - __main__ - INFO -    预期行为: 保持长宽比，缩放50%
2025-06-28 23:40:46,106 - __main__ - INFO - 测试: 拉伸缩放
2025-06-28 23:40:46,108 - __main__ - ERROR - 帧尺寸调整功能测试失败: 
2025-06-28 23:40:46,108 - __main__ - ERROR - 测试 test_frame_resize_with_spatial_control 失败
2025-06-28 23:40:46,108 - __main__ - INFO - === 测试插入融合空间尺寸控制 ===
2025-06-28 23:40:46,121 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:40:46,121 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:40:46,137 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:40:46,138 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:40:46,138 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 23:40:46,138 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:40:46,138 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:40:46,138 - __main__ - INFO - 测试: 等比例缩放50%
2025-06-28 23:40:46,138 - src.fusion.insertion_fusion - INFO - 开始空间控制插入，插入位置数量: 2
2025-06-28 23:40:46,139 - src.fusion.insertion_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=0.5, 保持长宽比=True, 缩放模式=proportional
2025-06-28 23:40:51,870 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-28 23:40:52,486 - __main__ - INFO - ✅ 等比例缩放50% 测试通过，结果帧数: 1204
2025-06-28 23:40:52,486 - __main__ - INFO - 测试: 拉伸缩放80%
2025-06-28 23:40:52,486 - src.fusion.insertion_fusion - INFO - 开始空间控制插入，插入位置数量: 2
2025-06-28 23:40:52,486 - src.fusion.insertion_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=0.8, 保持长宽比=False, 缩放模式=stretch
2025-06-28 23:40:56,872 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-28 23:40:57,641 - __main__ - INFO - ✅ 拉伸缩放80% 测试通过，结果帧数: 1204
2025-06-28 23:40:57,642 - __main__ - INFO - 测试: 裁剪缩放120%
2025-06-28 23:40:57,642 - src.fusion.insertion_fusion - INFO - 开始空间控制插入，插入位置数量: 2
2025-06-28 23:40:57,642 - src.fusion.insertion_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=1.2, 保持长宽比=True, 缩放模式=crop
2025-06-28 23:40:59,917 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,920 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,922 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,925 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,928 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,930 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,937 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,939 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,943 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,945 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,947 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,950 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,957 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,960 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,963 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,966 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,968 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,975 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,978 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,981 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,983 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,990 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,993 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,995 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:40:59,999 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,001 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,004 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,007 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,013 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,016 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,019 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,021 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,024 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,028 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,031 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,034 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,036 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,048 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,050 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,053 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,055 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,101 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,103 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,105 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,117 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,120 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,124 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,127 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,129 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,132 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,135 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,137 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,140 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,142 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,145 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,148 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,151 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,153 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,156 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,160 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,163 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,165 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,168 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,170 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,172 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,175 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,183 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,185 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,188 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,191 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,193 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,196 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,199 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,201 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,203 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,205 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,208 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,212 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,215 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,221 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,224 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,227 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,230 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,236 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,239 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,242 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,246 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,249 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,251 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,254 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,259 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,261 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,265 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,267 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,269 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,272 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,276 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,278 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,282 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,284 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,287 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,290 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,293 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,300 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,302 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,304 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,312 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,315 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,318 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,326 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,329 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,342 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,345 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,347 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,350 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,353 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,357 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,360 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,363 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,366 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,376 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,381 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,384 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,387 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,392 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,395 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,398 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,401 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,407 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,410 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,413 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,416 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,420 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,423 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,426 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,429 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,435 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,438 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,441 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,446 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,453 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,457 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,459 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,462 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,465 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,467 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,473 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,475 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,478 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,485 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,487 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,491 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,494 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,496 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,499 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,501 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,503 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,507 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,510 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,512 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,519 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,522 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,529 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,534 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,537 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,540 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,543 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,546 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,553 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,555 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,559 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,565 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,572 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,575 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,578 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,585 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,588 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,595 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,597 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,600 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,602 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,604 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,611 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,617 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,620 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,623 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,625 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,628 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,630 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,637 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,639 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,641 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,644 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,647 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,650 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,652 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,654 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,657 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,660 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,666 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,668 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,675 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,677 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,680 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,682 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,688 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,691 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,694 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,697 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,699 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,701 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,703 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,706 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,708 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,711 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,713 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,715 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,717 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,720 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,723 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,725 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,728 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,730 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,732 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,734 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,737 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,740 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,744 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,747 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,749 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,751 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,754 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,756 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,759 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,761 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,763 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,766 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,767 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,770 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,773 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,775 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,778 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,781 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,783 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,786 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,788 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,791 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,794 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,796 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,799 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,801 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,803 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,806 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,809 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,811 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,814 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,816 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,819 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,822 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,824 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,828 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,830 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,832 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,835 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,837 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,840 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,842 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,846 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,848 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,850 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,852 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,856 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,860 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,863 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,869 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:00,872 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (864,1536,3) into shape (72,128,3)
2025-06-28 23:41:02,899 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-28 23:41:03,584 - __main__ - INFO - ✅ 裁剪缩放120% 测试通过，结果帧数: 1204
2025-06-28 23:41:04,771 - __main__ - INFO - === 测试叠加融合空间尺寸控制 ===
2025-06-28 23:41:04,820 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:41:04,820 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:41:04,867 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:41:04,867 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:41:04,867 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 23:41:04,867 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 23:41:04,868 - src.fusion.overlay_fusion - INFO - 开始空间控制叠加融合，模式: normal, 透明度: 0.7
2025-06-28 23:41:04,868 - src.fusion.overlay_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=0.6, 保持长宽比=True, 缩放模式=proportional
2025-06-28 23:41:10,020 - src.fusion.overlay_fusion - INFO - 空间控制叠加融合完成，结果帧数: 1202
2025-06-28 23:41:10,448 - __main__ - ERROR - 叠加融合空间尺寸控制测试失败: 
2025-06-28 23:41:11,321 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_control 失败
2025-06-28 23:41:11,321 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-28 23:41:11,321 - __main__ - ERROR - ❌ 部分测试失败
2025-06-28 23:54:45,692 - __main__ - INFO - 开始测试空间尺寸控制算法
2025-06-28 23:54:45,693 - __main__ - INFO - === 测试空间尺寸控制参数创建 ===
2025-06-28 23:54:45,693 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-28 23:54:45,693 - __main__ - INFO - ✅ 自定义参数创建成功
2025-06-28 23:54:45,693 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-28 23:54:45,693 - __main__ - INFO - === 测试帧尺寸调整功能 ===
2025-06-28 23:54:45,695 - __main__ - INFO - 测试: 等比例缩放
2025-06-28 23:54:45,700 - __main__ - INFO - ✅ 等比例缩放 测试通过
2025-06-28 23:54:45,701 - __main__ - INFO -    原始尺寸: (480, 640, 3)
2025-06-28 23:54:45,701 - __main__ - INFO -    结果尺寸: (720, 1280, 3)
2025-06-28 23:54:45,701 - __main__ - INFO -    预期行为: 保持长宽比，缩放50%
2025-06-28 23:54:45,701 - __main__ - INFO - 测试: 拉伸缩放
2025-06-28 23:54:45,702 - __main__ - ERROR - 帧尺寸调整功能测试失败: 
2025-06-28 23:54:45,702 - __main__ - ERROR - 测试 test_frame_resize_with_spatial_control 失败
2025-06-28 23:54:45,702 - __main__ - INFO - === 测试插入融合空间尺寸控制 ===
2025-06-28 23:54:45,713 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:54:45,713 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:54:45,730 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:54:45,730 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:54:45,731 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-28 23:54:45,731 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:54:45,731 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:54:45,731 - __main__ - INFO - 测试: 等比例缩放50%
2025-06-28 23:54:45,731 - src.fusion.insertion_fusion - INFO - 开始空间控制插入，插入位置数量: 2
2025-06-28 23:54:45,731 - src.fusion.insertion_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=0.5, 保持长宽比=True, 缩放模式=proportional
2025-06-28 23:54:51,302 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-28 23:54:51,907 - __main__ - INFO - ✅ 等比例缩放50% 测试通过，结果帧数: 1204
2025-06-28 23:54:51,907 - __main__ - INFO - 测试: 拉伸缩放80%
2025-06-28 23:54:51,907 - src.fusion.insertion_fusion - INFO - 开始空间控制插入，插入位置数量: 2
2025-06-28 23:54:51,908 - src.fusion.insertion_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=0.8, 保持长宽比=False, 缩放模式=stretch
2025-06-28 23:54:56,467 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-28 23:54:57,258 - __main__ - INFO - ✅ 拉伸缩放80% 测试通过，结果帧数: 1204
2025-06-28 23:54:57,258 - __main__ - INFO - 测试: 裁剪缩放120%
2025-06-28 23:54:57,258 - src.fusion.insertion_fusion - INFO - 开始空间控制插入，插入位置数量: 2
2025-06-28 23:54:57,258 - src.fusion.insertion_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=1.2, 保持长宽比=True, 缩放模式=crop
2025-06-28 23:54:59,738 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,744 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,746 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,749 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,751 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,753 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,759 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,764 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,767 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,769 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,771 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,777 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,779 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,781 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,783 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,787 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,789 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,795 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,797 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,799 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,802 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,803 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,809 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,811 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,813 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,819 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,821 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,824 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,826 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,828 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,834 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,836 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,838 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,840 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,843 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,844 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,847 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,849 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,852 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,855 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,857 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,859 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,861 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,863 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,865 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,867 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,869 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,871 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,873 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,874 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,876 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,879 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,881 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,883 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,889 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,891 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,893 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,895 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,897 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,899 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,901 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,903 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,905 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,907 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,909 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,911 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,913 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,915 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,917 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,919 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,921 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,923 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,925 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,927 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,929 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,932 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,934 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,937 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,940 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,943 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,946 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,948 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,951 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,953 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,955 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,957 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,959 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,961 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,963 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,965 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,967 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,969 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,971 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,973 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,975 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,977 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,979 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,980 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,982 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,984 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,986 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,988 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,990 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,992 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,994 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,995 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,997 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:54:59,999 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,002 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,004 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,006 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,008 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,010 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,012 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,014 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,017 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,019 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,021 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,022 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,025 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,027 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,034 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,036 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,038 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,041 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,043 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,045 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,047 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,050 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,057 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,058 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,061 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,063 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,065 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,067 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,070 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,072 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,074 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,076 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,078 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,080 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,083 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,085 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,087 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,089 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,096 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,098 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,100 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,103 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,105 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,111 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,116 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,119 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,121 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,123 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,125 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,127 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,135 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,137 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,141 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,143 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,145 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,148 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,150 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,152 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,160 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,162 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,166 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,168 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,170 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,172 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,174 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,176 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,178 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,180 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,183 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,185 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,195 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,197 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,199 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,201 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,204 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,206 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,208 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,210 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,212 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,215 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,217 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,219 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,221 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,227 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,230 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,237 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,245 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,247 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,249 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,251 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,253 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,260 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,262 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,264 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,266 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,268 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,271 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,273 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,279 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,281 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,284 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,286 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,288 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,290 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,292 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,294 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,296 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,298 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,300 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,302 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,304 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,306 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,310 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,312 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,314 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,317 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,319 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,320 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,326 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,328 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,330 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,333 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,339 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,341 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,343 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,345 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,348 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,350 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,352 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,354 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,356 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,360 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,362 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,364 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,367 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,369 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,371 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,373 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,375 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,377 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,379 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,382 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,384 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,386 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,389 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,391 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,393 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,395 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,397 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,399 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,405 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,408 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,410 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,413 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,415 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,421 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,423 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,425 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,427 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,429 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,431 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,436 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,439 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,441 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,446 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,448 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,450 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,452 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:00,454 - src.fusion.insertion_fusion - ERROR - 使用空间尺寸控制调整帧大小失败: could not broadcast input array from shape (792,1408,3) into shape (72,128,3)
2025-06-28 23:55:02,018 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-28 23:55:02,695 - __main__ - INFO - ✅ 裁剪缩放120% 测试通过，结果帧数: 1204
2025-06-28 23:55:03,716 - __main__ - INFO - === 测试叠加融合空间尺寸控制 ===
2025-06-28 23:55:03,755 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:55:03,755 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:55:03,802 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:55:03,802 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:55:03,803 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 23:55:03,803 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 23:55:03,803 - src.fusion.overlay_fusion - INFO - 开始空间控制叠加融合，模式: normal, 透明度: 0.7
2025-06-28 23:55:03,803 - src.fusion.overlay_fusion - INFO - 空间控制参数: 对齐=True, 缩放比例=0.6, 保持长宽比=True, 缩放模式=proportional
2025-06-28 23:55:08,327 - src.fusion.overlay_fusion - INFO - 空间控制叠加融合完成，结果帧数: 1202
2025-06-28 23:55:08,729 - __main__ - ERROR - 叠加融合空间尺寸控制测试失败: 
2025-06-28 23:55:09,590 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_control 失败
2025-06-28 23:55:09,590 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-28 23:55:09,591 - __main__ - ERROR - ❌ 部分测试失败
2025-06-28 23:59:37,390 - __main__ - INFO - 开始测试空间位置控制算法
2025-06-28 23:59:37,390 - __main__ - INFO - === 测试空间位置控制参数创建 ===
2025-06-28 23:59:37,390 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-28 23:59:37,390 - __main__ - INFO - ✅ 动态位置参数创建成功
2025-06-28 23:59:37,390 - __main__ - INFO - ✅ 自定义路径参数创建成功
2025-06-28 23:59:37,390 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-28 23:59:37,390 - __main__ - INFO - === 测试空间位置计算 ===
2025-06-28 23:59:37,391 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 23:59:37,391 - __main__ - INFO - ✅ 静态位置计算正确: (300, 560)
2025-06-28 23:59:37,391 - __main__ - INFO - ✅ 水平滚动位置计算正确: [(0, 400), (250, 400), (500, 400), (750, 400), (800, 400)]
2025-06-28 23:59:37,391 - __main__ - INFO - ✅ 圆形轨迹位置计算正确: [(750, 400), (500, 600), (250, 400), (499, 200)]
2025-06-28 23:59:37,391 - __main__ - INFO - === 测试叠加融合空间位置控制 ===
2025-06-28 23:59:37,402 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:59:37,402 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:59:37,416 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:59:37,417 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:59:37,417 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-28 23:59:37,417 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-28 23:59:37,417 - __main__ - INFO - 测试: 静态位置
2025-06-28 23:59:37,417 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.6
2025-06-28 23:59:37,417 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=True, 运动轨迹=static, 运动速度=1.0
2025-06-28 23:59:39,871 - src.fusion.overlay_fusion - ERROR - 空间位置控制叠加融合失败: 'OverlayFusion' object has no attribute '_resize_frame'
2025-06-28 23:59:39,871 - __main__ - ERROR - 叠加融合空间位置控制测试失败: 'OverlayFusion' object has no attribute '_resize_frame'
2025-06-28 23:59:40,462 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_position_control 失败
2025-06-28 23:59:40,462 - __main__ - INFO - === 测试混合融合空间位置控制 ===
2025-06-28 23:59:40,472 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-28 23:59:40,472 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-28 23:59:40,506 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-28 23:59:40,506 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-28 23:59:40,506 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-28 23:59:40,507 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-28 23:59:40,507 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.5
2025-06-28 23:59:40,507 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=vertical_scroll, 运动速度=1.0
2025-06-28 23:59:43,573 - src.fusion.blend_fusion - ERROR - 在指定位置混合帧失败: 'BlendFusion' object has no attribute '_apply_blend_mode'
2025-06-28 23:59:43,575 - src.fusion.blend_fusion - ERROR - 在指定位置混合帧失败: 'BlendFusion' object has no attribute '_apply_blend_mode'
2025-06-28 23:59:43,577 - src.fusion.blend_fusion - ERROR - 在指定位置混合帧失败: 'BlendFusion' object has no attribute '_apply_blend_mode'
2025-06-28 23:59:43,579 - src.fusion.blend_fusion - ERROR - 在指定位置混合帧失败: 'BlendFusion' object has no attribute '_apply_blend_mode'
2025-06-28 23:59:43,581 - src.fusion.blend_fusion - ERROR - 在指定位置混合帧失败: 'BlendFusion' object has no attribute '_apply_blend_mode'
2025-06-28 23:59:45,382 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-28 23:59:45,771 - __main__ - ERROR - 混合融合空间位置控制测试失败: 
2025-06-28 23:59:46,573 - __main__ - ERROR - 测试 test_blend_fusion_spatial_position_control 失败
2025-06-28 23:59:46,574 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-28 23:59:46,574 - __main__ - ERROR - ❌ 部分测试失败
