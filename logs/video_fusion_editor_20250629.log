2025-06-29 00:01:27,740 - __main__ - INFO - 开始测试空间位置控制算法
2025-06-29 00:01:27,740 - __main__ - INFO - === 测试空间位置控制参数创建 ===
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 动态位置参数创建成功
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 自定义路径参数创建成功
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:01:27,740 - __main__ - INFO - === 测试空间位置计算 ===
2025-06-29 00:01:27,740 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 静态位置计算正确: (300, 560)
2025-06-29 00:01:27,741 - __main__ - INFO - ✅ 水平滚动位置计算正确: [(0, 400), (250, 400), (500, 400), (750, 400), (800, 400)]
2025-06-29 00:01:27,741 - __main__ - INFO - ✅ 圆形轨迹位置计算正确: [(750, 400), (500, 600), (250, 400), (499, 200)]
2025-06-29 00:01:27,741 - __main__ - INFO - === 测试叠加融合空间位置控制 ===
2025-06-29 00:01:27,752 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:01:27,752 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:01:27,766 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:01:27,766 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 00:01:27,766 - __main__ - INFO - 测试: 静态位置
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.6
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=True, 运动轨迹=static, 运动速度=1.0
2025-06-29 00:01:32,278 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 00:01:32,717 - __main__ - ERROR - 叠加融合空间位置控制测试失败: 
2025-06-29 00:01:33,443 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_position_control 失败
2025-06-29 00:01:33,444 - __main__ - INFO - === 测试混合融合空间位置控制 ===
2025-06-29 00:01:33,471 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:01:33,472 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:01:33,496 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:01:33,496 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.5
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=vertical_scroll, 运动速度=1.0
2025-06-29 00:01:37,786 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-29 00:01:38,208 - __main__ - ERROR - 混合融合空间位置控制测试失败: 
2025-06-29 00:01:38,938 - __main__ - ERROR - 测试 test_blend_fusion_spatial_position_control 失败
2025-06-29 00:01:38,938 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-29 00:01:38,938 - __main__ - ERROR - ❌ 部分测试失败
2025-06-29 00:02:31,021 - __main__ - INFO - 开始测试空间位置控制算法
2025-06-29 00:02:31,021 - __main__ - INFO - === 测试空间位置控制参数创建 ===
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 动态位置参数创建成功
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 自定义路径参数创建成功
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:02:31,021 - __main__ - INFO - === 测试空间位置计算 ===
2025-06-29 00:02:31,022 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:02:31,022 - __main__ - INFO - ✅ 静态位置计算正确: (300, 560)
2025-06-29 00:02:31,022 - __main__ - INFO - ✅ 水平滚动位置计算正确: [(0, 400), (250, 400), (500, 400), (750, 400), (800, 400)]
2025-06-29 00:02:31,022 - __main__ - INFO - ✅ 圆形轨迹位置计算正确: [(750, 400), (500, 600), (250, 400), (499, 200)]
2025-06-29 00:02:31,022 - __main__ - INFO - === 测试叠加融合空间位置控制 ===
2025-06-29 00:02:31,032 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:02:31,032 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:02:31,047 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:02:31,047 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 00:02:31,047 - __main__ - INFO - 测试: 静态位置
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.6
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=True, 运动轨迹=static, 运动速度=1.0
2025-06-29 00:02:35,624 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 00:02:36,093 - __main__ - ERROR - 预期5帧，实际得到1202帧
2025-06-29 00:02:36,789 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_position_control 失败
2025-06-29 00:02:36,789 - __main__ - INFO - === 测试混合融合空间位置控制 ===
2025-06-29 00:02:36,819 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:02:36,820 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:02:36,833 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:02:36,833 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:02:36,833 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:02:36,833 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 00:02:36,834 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.5
2025-06-29 00:02:36,834 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=vertical_scroll, 运动速度=1.0
2025-06-29 00:02:41,362 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-29 00:02:41,796 - __main__ - ERROR - 预期5帧，实际得到1202帧
2025-06-29 00:02:42,502 - __main__ - ERROR - 测试 test_blend_fusion_spatial_position_control 失败
2025-06-29 00:02:42,502 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-29 00:02:42,502 - __main__ - ERROR - ❌ 部分测试失败
2025-06-29 00:07:23,489 - __main__ - INFO - 开始测试图像处理控制功能
2025-06-29 00:07:23,489 - __main__ - INFO - === 测试图像处理控制参数创建 ===
2025-06-29 00:07:23,489 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:07:23,489 - __main__ - INFO - ✅ 自定义参数创建成功
2025-06-29 00:07:23,489 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:07:23,489 - __main__ - INFO - === 测试图像处理控制器 ===
2025-06-29 00:07:23,489 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,489 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,490 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,490 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,490 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,490 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,494 - __main__ - INFO - ✅ 不启用预处理测试通过
2025-06-29 00:07:23,509 - __main__ - INFO - ✅ 边缘检测测试通过
2025-06-29 00:07:23,511 - __main__ - INFO - ✅ Gamma校正测试通过
2025-06-29 00:07:23,517 - __main__ - INFO - ✅ 多种预处理方法组合测试通过
2025-06-29 00:07:23,517 - __main__ - INFO - === 测试融合方法 ===
2025-06-29 00:07:23,517 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,517 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,517 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,517 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,517 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,517 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 alpha_blend 测试通过
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 normal 测试通过
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 multiply 测试通过
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 screen 测试通过
2025-06-29 00:07:23,521 - __main__ - INFO - ✅ 融合方法 overlay 测试通过
2025-06-29 00:07:23,522 - __main__ - INFO - ✅ 融合方法 feather 测试通过
2025-06-29 00:07:23,522 - __main__ - INFO - === 测试可用方法列表 ===
2025-06-29 00:07:23,522 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,522 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,523 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,523 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,523 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,523 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,523 - __main__ - INFO - ✅ 可用方法列表测试通过
2025-06-29 00:07:23,523 - __main__ - INFO -    预处理方法: ['edge_detection', 'histogram_equalization', 'histogram_matching', 'gamma_correction', 'gaussian_blur', 'sharpen', 'noise_reduction']
2025-06-29 00:07:23,523 - __main__ - INFO -    边缘检测方法: ['canny', 'sobel', 'laplacian', 'scharr']
2025-06-29 00:07:23,523 - __main__ - INFO -    融合方法: ['alpha_blend', 'normal', 'multiply', 'screen', 'overlay', 'feather']
2025-06-29 00:07:23,523 - __main__ - INFO - === 测试插入融合图像处理控制 ===
2025-06-29 00:07:23,532 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:07:23,533 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:07:23,546 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:07:23,547 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:07:23,547 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,547 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,547 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,547 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,547 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,547 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:07:23,547 - __main__ - INFO - 测试: 边缘检测
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['edge_detection']
2025-06-29 00:07:28,537 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:29,021 - __main__ - INFO - ✅ 边缘检测 测试通过，结果帧数: 1204
2025-06-29 00:07:29,021 - __main__ - INFO - 测试: 直方图均衡化
2025-06-29 00:07:29,021 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:29,021 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['histogram_equalization']
2025-06-29 00:07:33,816 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:34,490 - __main__ - INFO - ✅ 直方图均衡化 测试通过，结果帧数: 1204
2025-06-29 00:07:34,490 - __main__ - INFO - 测试: Gamma校正
2025-06-29 00:07:34,490 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:34,490 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['gamma_correction']
2025-06-29 00:07:38,618 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:39,266 - __main__ - INFO - ✅ Gamma校正 测试通过，结果帧数: 1204
2025-06-29 00:07:39,266 - __main__ - INFO - 测试: 组合处理
2025-06-29 00:07:39,266 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:39,266 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['gamma_correction', 'gaussian_blur']
2025-06-29 00:07:43,627 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:44,255 - __main__ - INFO - ✅ 组合处理 测试通过，结果帧数: 1204
2025-06-29 00:07:45,287 - __main__ - INFO - 测试完成: 5/5 通过
2025-06-29 00:07:45,287 - __main__ - INFO - 🎉 所有测试通过！图像处理控制功能工作正常
2025-06-29 00:12:02,675 - __main__ - INFO - 开始测试文字内容控制功能
2025-06-29 00:12:02,675 - __main__ - INFO - === 测试文字内容控制参数创建 ===
2025-06-29 00:12:02,675 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:12:02,675 - __main__ - INFO - ✅ 自定义参数创建成功
2025-06-29 00:12:02,676 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:12:02,676 - __main__ - INFO - === 测试文字内容控制器 ===
2025-06-29 00:12:02,676 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,676 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,701 - __main__ - INFO - ✅ 不启用文字叠加测试通过
2025-06-29 00:12:02,701 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,701 - src.effects.text_content_controller - INFO - 文字内容: '静态文字', 位置模式: static
2025-06-29 00:12:02,712 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,712 - __main__ - INFO - ✅ 静态文字叠加测试通过
2025-06-29 00:12:02,713 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,713 - src.effects.text_content_controller - INFO - 文字内容: '动态文字', 位置模式: dynamic
2025-06-29 00:12:02,724 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,725 - __main__ - INFO - ✅ 动态文字叠加测试通过
2025-06-29 00:12:02,726 - __main__ - INFO - ✅ 单帧文字叠加测试通过
2025-06-29 00:12:02,727 - __main__ - INFO - === 测试文字样式和效果 ===
2025-06-29 00:12:02,727 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,728 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,739 - __main__ - INFO - ✅ 字体测试通过，测试了 5 种字体
2025-06-29 00:12:02,741 - __main__ - INFO - ✅ 描边效果测试通过
2025-06-29 00:12:02,742 - __main__ - INFO - ✅ 阴影效果测试通过
2025-06-29 00:12:02,747 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 20
2025-06-29 00:12:02,748 - src.effects.text_content_controller - INFO - 文字内容: '淡入淡出文字', 位置模式: static
2025-06-29 00:12:02,779 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 20 帧
2025-06-29 00:12:02,779 - __main__ - INFO - ✅ 淡入淡出效果测试通过
2025-06-29 00:12:02,783 - __main__ - INFO - === 测试运动轨迹 ===
2025-06-29 00:12:02,783 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,783 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,806 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,806 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: linear_random', 位置模式: dynamic
2025-06-29 00:12:02,815 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,815 - __main__ - INFO - ✅ 轨迹 linear_random 测试通过
2025-06-29 00:12:02,815 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,815 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: curve_random', 位置模式: dynamic
2025-06-29 00:12:02,827 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,828 - __main__ - INFO - ✅ 轨迹 curve_random 测试通过
2025-06-29 00:12:02,828 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,828 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: circular', 位置模式: dynamic
2025-06-29 00:12:02,835 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,836 - __main__ - INFO - ✅ 轨迹 circular 测试通过
2025-06-29 00:12:02,836 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,836 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: elliptical', 位置模式: dynamic
2025-06-29 00:12:02,845 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,846 - __main__ - INFO - ✅ 轨迹 elliptical 测试通过
2025-06-29 00:12:02,846 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,846 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: horizontal_scroll', 位置模式: dynamic
2025-06-29 00:12:02,856 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,856 - __main__ - INFO - ✅ 轨迹 horizontal_scroll 测试通过
2025-06-29 00:12:02,856 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,856 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: vertical_scroll', 位置模式: dynamic
2025-06-29 00:12:02,865 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,866 - __main__ - INFO - ✅ 轨迹 vertical_scroll 测试通过
2025-06-29 00:12:02,866 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,866 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: diagonal', 位置模式: dynamic
2025-06-29 00:12:02,876 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,876 - __main__ - INFO - ✅ 轨迹 diagonal 测试通过
2025-06-29 00:12:02,876 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,876 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: custom_path', 位置模式: dynamic
2025-06-29 00:12:02,886 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,887 - __main__ - INFO - ✅ 轨迹 custom_path 测试通过
2025-06-29 00:12:02,890 - __main__ - INFO - === 测试插入融合文字内容控制 ===
2025-06-29 00:12:02,900 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:12:02,900 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:12:02,913 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:12:02,914 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:12:02,915 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:12:02,915 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:12:02,916 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:12:02,916 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:12:02,916 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:12:02,916 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:12:02,916 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,916 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,916 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:12:02,916 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 00:12:02,916 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:12:02,917 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:12:02,917 - __main__ - INFO - 测试: 静态文字
2025-06-29 00:12:02,917 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 1
2025-06-29 00:12:02,917 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:12:08,573 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1203
2025-06-29 00:12:08,574 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1203
2025-06-29 00:12:08,574 - src.effects.text_content_controller - INFO - 文字内容: '静态测试文字', 位置模式: static
2025-06-29 00:12:10,726 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1203 帧
2025-06-29 00:12:10,954 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1203
2025-06-29 00:12:11,718 - __main__ - INFO - ✅ 静态文字 测试通过，结果帧数: 1203
2025-06-29 00:12:11,718 - __main__ - INFO - 测试: 滚动文字
2025-06-29 00:12:11,718 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 1
2025-06-29 00:12:11,718 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:12:16,035 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1203
2025-06-29 00:12:16,035 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1203
2025-06-29 00:12:16,035 - src.effects.text_content_controller - INFO - 文字内容: '滚动测试文字', 位置模式: dynamic
2025-06-29 00:12:18,194 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1203 帧
2025-06-29 00:12:18,241 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1203
2025-06-29 00:12:19,442 - __main__ - INFO - ✅ 滚动文字 测试通过，结果帧数: 1203
2025-06-29 00:12:19,442 - __main__ - INFO - 测试: 带效果文字
2025-06-29 00:12:19,442 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 1
2025-06-29 00:12:19,442 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:12:23,663 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1203
2025-06-29 00:12:23,663 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1203
2025-06-29 00:12:23,663 - src.effects.text_content_controller - INFO - 文字内容: '效果测试文字', 位置模式: static
2025-06-29 00:12:25,820 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1203 帧
2025-06-29 00:12:25,887 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1203
2025-06-29 00:12:27,025 - __main__ - INFO - ✅ 带效果文字 测试通过，结果帧数: 1203
2025-06-29 00:12:28,230 - __main__ - INFO - 测试完成: 5/5 通过
2025-06-29 00:12:28,231 - __main__ - INFO - 🎉 所有测试通过！文字内容控制功能工作正常
2025-06-29 00:14:20,534 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,534 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,534 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,534 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,534 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:14:20,534 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:14:20,535 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:14:20,535 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:14:20,535 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:14:20,535 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,535 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,535 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,535 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,535 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:14:20,535 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:14:20,535 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:14:20,535 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:14:20,536 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:14:20,536 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,536 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,536 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,536 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,536 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:14:20,536 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:14:20,536 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:14:20,536 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:14:20,536 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:14:20,536 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:14:20,536 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:14:20,537 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:14:20,537 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:14:20,537 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:14:20,862 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 00:14:20,862 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 00:14:20,863 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 00:14:20,863 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 00:14:20,877 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 00:14:20,878 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 00:14:20,878 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 00:23:52,684 - __main__ - INFO - 开始五维控制系统综合测试
2025-06-29 00:23:52,684 - __main__ - INFO - === 测试融合参数创建和序列化 ===
2025-06-29 00:23:52,684 - __main__ - ERROR - 融合参数创建和序列化测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:23:52,684 - __main__ - ERROR - 测试 test_fusion_params_creation_and_serialization 失败
2025-06-29 00:23:52,684 - __main__ - INFO - === 测试融合引擎GUI集成 ===
2025-06-29 00:23:52,685 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,685 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,686 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,686 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,686 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,686 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,686 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,686 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,686 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,686 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,687 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,687 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,687 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,687 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,687 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,688 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,688 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,688 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,688 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,688 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,688 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,688 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,689 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:23:52,689 - __main__ - ERROR - 融合引擎GUI集成测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:23:52,689 - __main__ - ERROR - 测试 test_fusion_engine_gui_integration 失败
2025-06-29 00:23:52,689 - __main__ - INFO - === 测试综合控制插入融合 ===
2025-06-29 00:23:52,689 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,689 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,689 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,689 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,689 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,689 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,689 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,689 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,689 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,690 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,690 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,690 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,691 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,691 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,691 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,691 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,691 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,691 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,691 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,691 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,691 - __main__ - ERROR - 综合控制插入融合测试失败: 'FusionEngine' object has no attribute 'load_videos'
2025-06-29 00:23:52,691 - __main__ - ERROR - 测试 test_comprehensive_insertion_fusion 失败
2025-06-29 00:23:52,691 - __main__ - INFO - === 测试综合控制叠加融合 ===
2025-06-29 00:23:52,691 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,691 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,691 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,691 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,691 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,691 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,692 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,692 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,692 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,692 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,692 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,692 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,693 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,693 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,693 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,693 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,693 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,693 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,693 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,693 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,693 - __main__ - ERROR - 综合控制叠加融合测试失败: 'FusionEngine' object has no attribute 'load_videos'
2025-06-29 00:23:52,693 - __main__ - ERROR - 测试 test_comprehensive_overlay_fusion 失败
2025-06-29 00:23:52,693 - __main__ - INFO - === 测试综合控制混合融合 ===
2025-06-29 00:23:52,693 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,693 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,694 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,694 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,694 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,694 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,694 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,694 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,695 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,695 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,695 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,695 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,695 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,695 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,695 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,695 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,695 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,695 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,695 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,695 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,695 - __main__ - ERROR - 综合控制混合融合测试失败: 'FusionEngine' object has no attribute 'load_videos'
2025-06-29 00:23:52,695 - __main__ - ERROR - 测试 test_comprehensive_blend_fusion 失败
2025-06-29 00:23:52,696 - __main__ - INFO - 综合测试完成: 0/5 通过
2025-06-29 00:23:52,696 - __main__ - ERROR - ❌ 部分综合测试失败
2025-06-29 00:26:02,886 - __main__ - INFO - 开始五维控制系统综合测试
2025-06-29 00:26:02,886 - __main__ - INFO - === 测试融合参数创建和序列化 ===
2025-06-29 00:26:02,886 - __main__ - ERROR - 融合参数创建和序列化测试失败: __init__() got an unexpected keyword argument 'resize_mode'
2025-06-29 00:26:02,886 - __main__ - ERROR - 测试 test_fusion_params_creation_and_serialization 失败
2025-06-29 00:26:02,887 - __main__ - INFO - === 测试融合引擎GUI集成 ===
2025-06-29 00:26:02,887 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,888 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,888 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,888 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,888 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,888 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,888 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,889 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,889 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,889 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,889 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,889 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,889 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,889 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,889 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,889 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,889 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,889 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,890 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,890 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,890 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,890 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,890 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,890 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,890 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,890 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,890 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,890 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,890 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,891 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,891 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,891 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,891 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,891 - __main__ - ERROR - 融合引擎GUI集成测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,891 - __main__ - ERROR - 测试 test_fusion_engine_gui_integration 失败
2025-06-29 00:26:02,891 - __main__ - INFO - === 测试综合控制插入融合 ===
2025-06-29 00:26:02,891 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,891 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,891 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,892 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,892 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,892 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,892 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,892 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,892 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,893 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,893 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,893 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,893 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,893 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,893 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,893 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,893 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,893 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,893 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,893 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,906 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,906 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:26:02,906 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,924 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,924 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=30, duration=1)
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=60, duration=1)
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,924 - __main__ - ERROR - 综合控制插入融合测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,929 - __main__ - ERROR - 测试 test_comprehensive_insertion_fusion 失败
2025-06-29 00:26:02,929 - __main__ - INFO - === 测试综合控制叠加融合 ===
2025-06-29 00:26:02,929 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,929 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,929 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,930 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,930 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,930 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,930 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,930 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,930 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,930 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,930 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,930 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,930 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,930 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,931 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,931 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,931 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,931 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,931 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,931 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,931 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,931 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,931 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,931 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,931 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,931 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,931 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,932 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,940 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,940 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:26:02,940 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,954 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,954 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:26:02,954 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,954 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,954 - __main__ - ERROR - 综合控制叠加融合测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,957 - __main__ - ERROR - 测试 test_comprehensive_overlay_fusion 失败
2025-06-29 00:26:02,958 - __main__ - INFO - === 测试综合控制混合融合 ===
2025-06-29 00:26:02,958 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,958 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,958 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,958 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,959 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,959 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,959 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,959 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,959 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,959 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,959 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,959 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,959 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,959 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,960 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,960 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,960 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,960 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,960 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,960 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,960 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,960 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,960 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,960 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,960 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,960 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,960 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,960 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,970 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,970 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:26:02,970 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,984 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,984 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:26:02,984 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,984 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,984 - __main__ - ERROR - 综合控制混合融合测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,987 - __main__ - ERROR - 测试 test_comprehensive_blend_fusion 失败
2025-06-29 00:26:02,988 - __main__ - INFO - 综合测试完成: 0/5 通过
2025-06-29 00:26:02,988 - __main__ - ERROR - ❌ 部分综合测试失败
2025-06-29 00:28:20,890 - __main__ - INFO - 开始五维控制系统综合测试
2025-06-29 00:28:20,890 - __main__ - INFO - === 测试融合参数创建和序列化 ===
2025-06-29 00:28:20,891 - __main__ - INFO - ✅ 融合参数序列化测试通过
2025-06-29 00:28:20,891 - __main__ - INFO - ✅ 融合参数反序列化测试通过
2025-06-29 00:28:20,891 - __main__ - INFO - === 测试融合引擎GUI集成 ===
2025-06-29 00:28:20,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,892 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,892 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,893 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,893 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,893 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:20,893 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,893 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,893 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,894 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,894 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,894 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,894 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:20,894 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,894 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,894 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,894 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,894 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,894 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,894 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,894 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,894 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:20,895 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:20,895 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:20,895 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:20,895 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:20,895 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:20,895 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.7, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 4, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.2, 'maintain_aspect_ratio': False, 'scale_mode': 'crop'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.3, 'static_position_y': 0.7, 'motion_trajectory': 'vertical_scroll', 'motion_speed': 2.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': True, 'preprocessing_methods': ['edge_detection', 'histogram_equalization'], 'edge_detection_method': 'sobel', 'edge_low_threshold': 30, 'edge_high_threshold': 120, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.5, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': 'GUI测试文字', 'text_encoding': 'utf-8', 'text_position_mode': 'dynamic', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'circular', 'text_motion_speed': 0.8, 'font_family': 'Times New Roman', 'font_size': 32, 'font_color': [0, 255, 0], 'font_alpha': 1.0, 'font_bold': True, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 2, 'continuous_frames': 50, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:20,895 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:20,895 - __main__ - ERROR - 融合引擎GUI集成测试失败: 'SpatialSizeControl' object has no attribute 'resize_mode'
2025-06-29 00:28:20,896 - __main__ - ERROR - 测试 test_fusion_engine_gui_integration 失败
2025-06-29 00:28:20,896 - __main__ - INFO - === 测试综合控制插入融合 ===
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,896 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,896 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,896 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,896 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,896 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,896 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,896 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,896 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,897 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,897 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,897 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,897 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:20,897 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,897 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,897 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,897 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,897 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,897 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,897 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:20,897 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:20,897 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:20,898 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:20,898 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:20,898 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:20,909 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:20,910 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:20,910 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:20,927 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:20,927 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=30, duration=1)
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=60, duration=1)
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [{'frame': 30, 'duration': 1}, {'frame': 60, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 2, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.2, 'static_position_y': 0.3, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': True, 'preprocessing_methods': ['gamma_correction'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.3, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': '综合测试', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.9, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 0], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 100, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:20,928 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:20,928 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:20,928 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 00:28:20,928 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 28, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 块大小设置为: 28
2025-06-29 00:28:20,929 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 00:28:20,929 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 00:28:20,929 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 2
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 图像处理控制: 启用预处理=True
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:28:27,654 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:28:27,655 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1204
2025-06-29 00:28:27,655 - src.effects.text_content_controller - INFO - 文字内容: '综合测试', 位置模式: static
2025-06-29 00:28:30,071 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1204 帧
2025-06-29 00:28:30,471 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1204
2025-06-29 00:28:31,183 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 00:28:31,183 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 10.25秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 00:28:31,183 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 10.253596067428589, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 00:28:32,191 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 00:28:32,191 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 00:28:32,192 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:32,201 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 00:28:32,202 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 00:28:32,203 - __main__ - INFO - ✅ 综合控制插入融合测试通过，结果帧数: 1204
2025-06-29 00:28:33,181 - __main__ - INFO - === 测试综合控制叠加融合 ===
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,182 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,182 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,182 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:33,182 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:33,182 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:33,182 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:33,182 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,182 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,183 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:33,183 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:33,183 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:33,183 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:33,183 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,183 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,183 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,183 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:33,183 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:33,183 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:33,183 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:33,184 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:33,184 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:33,184 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:33,184 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:33,184 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:33,243 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:33,243 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:33,243 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:33,281 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:33,281 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:33,281 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:33,281 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 0.7, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'circular', 'motion_speed': 1.5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': '叠加测试', 'text_encoding': 'utf-8', 'text_position_mode': 'dynamic', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 2.0, 'font_family': 'Arial', 'font_size': 20, 'font_color': [0, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:33,281 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:33,282 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:33,282 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 00:28:33,282 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 27, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 块大小设置为: 27
2025-06-29 00:28:33,282 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 00:28:33,283 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 00:28:33,283 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 00:28:33,283 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.5
2025-06-29 00:28:33,283 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=circular, 运动速度=1.5
2025-06-29 00:28:52,651 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 00:28:53,133 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1202
2025-06-29 00:28:53,133 - src.effects.text_content_controller - INFO - 文字内容: '叠加测试', 位置模式: dynamic
2025-06-29 00:28:54,064 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1202 帧
2025-06-29 00:28:54,064 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 00:28:54,064 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 20.78秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 00:28:54,064 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 20.78084897994995, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 00:28:54,398 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 00:28:54,398 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 00:28:54,398 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:54,408 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 00:28:54,409 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 00:28:54,409 - __main__ - INFO - ✅ 综合控制叠加融合测试通过，结果帧数: 1202
2025-06-29 00:28:55,797 - __main__ - INFO - === 测试综合控制混合融合 ===
2025-06-29 00:28:55,797 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,797 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,797 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,797 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,797 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:55,798 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:55,798 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:55,798 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:55,798 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:55,798 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,798 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,798 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,798 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,798 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:55,798 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:55,798 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:55,798 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:55,798 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:55,798 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,798 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,799 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,799 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,799 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:55,799 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:55,799 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:55,799 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:55,799 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:55,799 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:55,799 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:55,799 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:55,799 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:55,799 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:55,810 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:55,810 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:55,811 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:55,848 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:55,849 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:55,849 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:55,849 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.4, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'diagonal', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': True, 'preprocessing_methods': ['edge_detection', 'gaussian_blur'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': '混合测试', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 26, 'font_color': [255, 0, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': True, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': True, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:55,849 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:55,849 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:55,850 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 00:28:55,850 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 27, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 块大小设置为: 27
2025-06-29 00:28:55,850 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 00:28:55,851 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 00:28:55,851 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 00:28:55,851 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.4
2025-06-29 00:28:55,851 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=diagonal, 运动速度=1.0
2025-06-29 00:29:24,353 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-29 00:29:24,842 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1202
2025-06-29 00:29:24,842 - src.effects.text_content_controller - INFO - 文字内容: '混合测试', 位置模式: static
2025-06-29 00:29:25,713 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1202 帧
2025-06-29 00:29:25,713 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 00:29:25,713 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 29.86秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 00:29:25,713 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 29.862234115600586, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 00:29:26,013 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 00:29:26,014 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 00:29:26,014 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:29:26,024 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 00:29:26,024 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 00:29:26,024 - __main__ - INFO - ✅ 综合控制混合融合测试通过，结果帧数: 1202
2025-06-29 00:29:27,343 - __main__ - INFO - 综合测试完成: 4/5 通过
2025-06-29 00:29:27,343 - __main__ - ERROR - ❌ 部分综合测试失败
2025-06-29 00:32:49,186 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,186 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,187 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,187 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,187 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:32:49,187 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:32:49,187 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:32:49,187 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:32:49,187 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:32:49,188 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,188 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,188 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,188 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,188 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:32:49,188 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:32:49,188 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:32:49,188 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:32:49,188 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:32:49,188 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,188 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,189 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,189 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,189 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:32:49,189 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:32:49,189 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:32:49,189 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:32:49,189 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:32:49,189 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:32:49,189 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:32:49,189 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:32:49,190 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:32:49,190 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:32:49,475 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 00:32:49,476 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 00:32:49,476 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 00:32:49,476 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 00:32:49,492 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 00:32:49,493 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 00:32:49,493 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 07:49:29,051 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 07:49:51,088 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,088 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,089 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,089 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,089 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:49:51,089 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:49:51,089 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:49:51,089 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:49:51,089 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:49:51,090 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,090 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,090 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,090 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,090 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:49:51,090 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:49:51,090 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:49:51,090 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:49:51,090 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:49:51,091 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,091 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,091 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,091 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,091 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:49:51,091 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:49:51,091 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:49:51,091 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:49:51,091 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:49:51,091 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:49:51,091 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:49:51,092 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:49:51,092 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:49:51,092 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:49:51,391 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 07:49:51,391 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 07:49:51,392 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 07:49:51,392 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 07:49:51,405 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 07:49:51,405 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 07:49:51,405 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 07:52:17,400 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:52:17,400 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:52:17,401 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:52:17,401 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:52:17,401 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:52:17,401 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:52:19,913 - __main__ - INFO - 🚀 开始最终系统验证测试
2025-06-29 07:52:19,914 - __main__ - INFO - 
--- 执行测试: 应用程序启动 ---
2025-06-29 07:52:19,914 - __main__ - INFO - === 测试应用程序启动 ===
2025-06-29 07:52:20,001 - __main__ - INFO - ✅ 所有主要模块导入成功
2025-06-29 07:52:20,002 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,003 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,003 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,003 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,003 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,003 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,004 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,004 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,004 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:52:20,004 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,004 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,004 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,004 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,004 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,005 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,005 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,005 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,005 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:52:20,005 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,005 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,005 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,005 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,005 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,006 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,006 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,006 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,006 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:52:20,006 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:52:20,006 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:52:20,007 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:52:20,007 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:52:20,007 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:52:20,007 - __main__ - INFO - ✅ 融合引擎初始化成功
2025-06-29 07:52:20,007 - __main__ - INFO - ✅ 应用程序启动 测试通过
2025-06-29 07:52:20,008 - __main__ - INFO - 
--- 执行测试: 五维控制系统 ---
2025-06-29 07:52:20,008 - __main__ - INFO - === 测试五维控制系统 ===
2025-06-29 07:52:20,008 - __main__ - INFO - ✅ 五维控制系统模块导入成功
2025-06-29 07:52:20,008 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,008 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,008 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,008 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,008 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,008 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,008 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,008 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,008 - __main__ - INFO - ✅ 控制器初始化成功
2025-06-29 07:52:20,009 - __main__ - INFO - ✅ 五维控制参数创建成功
2025-06-29 07:52:20,009 - __main__ - INFO - ✅ 五维控制系统 测试通过
2025-06-29 07:52:20,009 - __main__ - INFO - 
--- 执行测试: 融合算法 ---
2025-06-29 07:52:20,009 - __main__ - INFO - === 测试融合算法 ===
2025-06-29 07:52:20,009 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,009 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,009 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,009 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,009 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,009 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,009 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,009 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,009 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:52:20,009 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,010 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,010 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,010 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,010 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:52:20,010 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,011 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,011 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,011 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,011 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:52:20,011 - __main__ - INFO - ✅ 所有融合算法初始化成功
2025-06-29 07:52:20,011 - __main__ - INFO - ✅ 五维控制集成验证成功
2025-06-29 07:52:20,011 - __main__ - INFO - ✅ 融合算法 测试通过
2025-06-29 07:52:20,011 - __main__ - INFO - 
--- 执行测试: GUI集成 ---
2025-06-29 07:52:20,011 - __main__ - INFO - === 测试GUI集成 ===
2025-06-29 07:53:15,601 - __main__ - INFO - 🚀 开始无头模式系统验证测试
2025-06-29 07:53:15,601 - __main__ - INFO - 
--- 执行测试: 核心模块 ---
2025-06-29 07:53:15,601 - __main__ - INFO - === 测试核心模块 ===
2025-06-29 07:53:15,918 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,919 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,919 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,919 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,919 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,919 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,920 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,920 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,920 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:53:15,920 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,920 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,920 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,921 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,921 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,921 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,921 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,921 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,921 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:53:15,921 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,921 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,921 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,922 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,922 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,922 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,922 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,922 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,922 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:53:15,922 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:53:15,922 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:53:15,923 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:53:15,923 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:53:15,923 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 融合引擎模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 五维控制参数模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 融合算法模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 控制器模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 核心模块 测试通过
2025-06-29 07:53:15,923 - __main__ - INFO - 
--- 执行测试: 视频处理 ---
2025-06-29 07:53:15,923 - __main__ - INFO - === 测试视频处理 ===
2025-06-29 07:53:15,941 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 07:53:15,941 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 07:53:15,961 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 07:53:15,961 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 07:53:15,961 - __main__ - INFO - ✅ 视频加载功能正常
2025-06-29 07:53:15,963 - __main__ - INFO - ✅ 视频处理 测试通过
2025-06-29 07:53:15,963 - __main__ - INFO - 
--- 执行测试: 性能模块 ---
2025-06-29 07:53:15,963 - __main__ - INFO - === 测试性能模块 ===
2025-06-29 07:53:15,963 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:53:15,963 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:53:15,963 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:53:15,964 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 5
2025-06-29 07:53:15,964 - __main__ - INFO - ✅ 性能模块正常
2025-06-29 07:53:15,964 - __main__ - INFO - ✅ 性能模块 测试通过
2025-06-29 07:53:15,964 - __main__ - INFO - 
--- 执行测试: 五维控制集成 ---
2025-06-29 07:53:15,964 - __main__ - INFO - === 测试五维控制集成 ===
2025-06-29 07:53:15,964 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,964 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,964 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,964 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,965 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,965 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,965 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:53:15,965 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,965 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,965 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,965 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,965 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,965 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,966 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:53:15,966 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,966 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,966 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,966 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,966 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,966 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,966 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,966 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,966 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:53:15,966 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:53:15,966 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:53:15,966 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:53:15,967 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:53:15,967 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:53:15,967 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 3, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:15,967 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:15,967 - __main__ - INFO - ✅ 五维控制集成正常
2025-06-29 07:53:15,967 - __main__ - INFO - ✅ 五维控制集成 测试通过
2025-06-29 07:53:15,967 - __main__ - INFO - 
--- 执行测试: 文档完整性 ---
2025-06-29 07:53:15,967 - __main__ - INFO - === 测试文档完整性 ===
2025-06-29 07:53:15,967 - __main__ - INFO - ✅ 存在的文档: ['README.md', 'USER_GUIDE.md', 'prd.md', 'requirements.txt', 'activate_env.sh', 'run.sh']
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 文档完整性 测试通过
2025-06-29 07:53:15,968 - __main__ - INFO - 
🏁 系统验证测试完成: 5/5 通过
2025-06-29 07:53:15,968 - __main__ - INFO - 🎉 恭喜！系统验证测试全部通过！
2025-06-29 07:53:15,968 - __main__ - INFO - 📋 五维视频融合控制系统核心功能正常
2025-06-29 07:53:15,968 - __main__ - INFO - 🚀 系统已准备就绪
2025-06-29 07:53:15,968 - __main__ - INFO - 
============================================================
2025-06-29 07:53:15,968 - __main__ - INFO - 📊 五维视频融合控制系统 - 最终验证报告
2025-06-29 07:53:15,968 - __main__ - INFO - ============================================================
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 1️⃣ 时间维度控制 - 插入频次/时间控制
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 2️⃣ 空间尺寸控制 - 图像缩放/长宽比控制
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 3️⃣ 空间位置控制 - 静态/动态运动模式
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 4️⃣ 图像处理控制 - 预处理和混合方法控制
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 5️⃣ 文字内容控制 - 文字叠加位置/样式/时间控制
2025-06-29 07:53:15,968 - __main__ - INFO - ============================================================
2025-06-29 07:53:15,969 - __main__ - INFO - 🎯 项目状态: 完成 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - 🔧 核心功能: 正常 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - 📚 文档体系: 完整 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - 🧪 测试验证: 通过 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - ============================================================
2025-06-29 07:53:35,599 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:35,600 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:35,600 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:35,600 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:35,600 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:35,600 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:37,582 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:37,583 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:37,583 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:37,583 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:37,583 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:37,584 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:39,818 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:39,818 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:41,515 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:41,515 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:41,515 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:41,516 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:41,516 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:41,516 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:01,638 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,638 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,638 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,638 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,639 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:06:01,639 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:06:01,639 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:06:01,639 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:06:01,639 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 08:06:01,640 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,640 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,640 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,640 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,640 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:06:01,640 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:06:01,640 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:06:01,640 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:06:01,640 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 08:06:01,640 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,641 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,641 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,641 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,641 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:06:01,641 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:06:01,641 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:06:01,641 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:06:01,641 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 08:06:01,642 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 08:06:01,642 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 08:06:01,642 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 08:06:01,642 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 08:06:01,642 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 08:06:02,024 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 08:06:02,024 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 08:06:02,024 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 08:06:02,025 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 08:06:02,041 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 08:06:02,041 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 08:06:02,042 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 08:06:02,448 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,448 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,448 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:02,449 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,449 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,449 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:02,551 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,551 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,551 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:02,551 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,552 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,552 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:07,219 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 08:06:17,239 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,254 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,254 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:17,268 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,269 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:17,269 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,269 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:22,025 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,055 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,055 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:22,080 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,081 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:22,081 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,081 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:27,821 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:27,821 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 08:06:27,821 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:27,822 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:27,846 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-29 08:06:27,846 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-29 08:06:29,398 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:06:34,923 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:34,923 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:34,923 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-29 08:06:34,924 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:34,924 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:34,924 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:34,924 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:35,975 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:35,975 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 08:06:35,975 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:35,975 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:36,355 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-29 08:06:36,370 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-29 08:06:36,744 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:06:41,021 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:41,021 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 08:06:41,022 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:41,022 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:41,377 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-29 08:06:41,387 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-29 08:06:41,408 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:06:48,873 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-29 08:06:48,876 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:48,876 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:48,876 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:48,877 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:48,877 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:48,877 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:50,554 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:50,555 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:06:50,555 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:06:50,804 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:06:50,808 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:06:50,827 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:09:43,056 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:09:43,056 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:43,056 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:09:43,268 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:09:43,270 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:09:43,891 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:09:43,891 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:43,891 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:09:44,112 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:09:44,114 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:09:45,522 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:09:45,523 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:45,523 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:09:45,732 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:09:45,734 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:09:47,023 - src.gui.main_window - INFO - 开始融合处理...
2025-06-29 08:09:47,025 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 08:09:47,025 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 08:09:47,025 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 22, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 块大小设置为: 22
2025-06-29 08:09:47,026 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 08:09:47,027 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 08:09:47,027 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:47,027 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.5
2025-06-29 08:09:47,027 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=horizontal_scroll, 运动速度=1.0
2025-06-29 08:10:20,442 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 08:10:21,092 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 08:10:21,092 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 34.07秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 08:10:21,092 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 34.06509613990784, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 08:10:21,115 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 08:10:21,115 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 08:10:21,116 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 08:10:21,125 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 08:10:21,126 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 08:10:21,126 - src.gui.main_window - INFO - 融合处理成功完成
2025-06-29 08:11:19,645 - src.gui.main_window - INFO - 正在导出视频到: /Users/<USER>/Documents/augment-projects/X/output/1.mp4
2025-06-29 08:11:20,583 - src.fusion.overlay_fusion - INFO - 已写入 100/1202 帧
2025-06-29 08:11:21,489 - src.fusion.overlay_fusion - INFO - 已写入 200/1202 帧
2025-06-29 08:11:22,485 - src.fusion.overlay_fusion - INFO - 已写入 300/1202 帧
2025-06-29 08:11:23,380 - src.fusion.overlay_fusion - INFO - 已写入 400/1202 帧
2025-06-29 08:11:24,233 - src.fusion.overlay_fusion - INFO - 已写入 500/1202 帧
2025-06-29 08:11:25,097 - src.fusion.overlay_fusion - INFO - 已写入 600/1202 帧
2025-06-29 08:11:25,927 - src.fusion.overlay_fusion - INFO - 已写入 700/1202 帧
2025-06-29 08:11:26,754 - src.fusion.overlay_fusion - INFO - 已写入 800/1202 帧
2025-06-29 08:11:27,610 - src.fusion.overlay_fusion - INFO - 已写入 900/1202 帧
2025-06-29 08:11:28,454 - src.fusion.overlay_fusion - INFO - 已写入 1000/1202 帧
2025-06-29 08:11:29,303 - src.fusion.overlay_fusion - INFO - 已写入 1100/1202 帧
2025-06-29 08:11:30,213 - src.fusion.overlay_fusion - INFO - 已写入 1200/1202 帧
2025-06-29 08:11:30,231 - src.fusion.overlay_fusion - INFO - 叠加融合视频保存完成: /Users/<USER>/Documents/augment-projects/X/output/1.mp4
2025-06-29 08:11:30,231 - src.gui.main_window - INFO - 视频导出成功: /Users/<USER>/Documents/augment-projects/X/output/1.mp4
2025-06-29 08:11:39,126 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:11:39,126 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:11:39,126 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:11:39,353 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:11:39,355 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:11:42,998 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:11:42,998 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:11:42,999 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:11:43,212 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:11:43,214 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:25,531 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-29 08:12:25,532 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:25,533 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:25,533 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:25,533 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:25,533 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:25,533 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:26,914 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:26,914 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:26,915 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:27,128 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:27,130 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:27,130 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:29,229 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:29,230 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:29,230 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:29,444 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:29,446 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:29,446 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:31,275 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:31,276 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:31,276 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:31,276 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:31,276 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:31,276 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:32,227 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:32,228 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:32,228 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:32,442 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:32,443 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:32,444 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:34,463 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:34,464 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:34,464 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:34,677 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:34,679 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:34,679 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:35,596 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:35,596 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:35,597 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:35,809 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:35,810 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:35,810 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:38,749 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-29 08:12:38,749 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:38,749 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:38,750 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:38,750 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:38,750 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:38,750 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:39,700 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:39,701 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:39,701 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:39,929 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:39,930 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:39,931 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:45,515 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:45,516 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:45,516 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:45,516 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:45,516 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:45,516 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:46,566 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:46,566 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:46,566 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:46,799 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:46,800 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:46,800 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:54,711 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:54,711 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:54,711 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:54,932 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:54,934 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:56,676 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:56,676 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:56,676 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:56,895 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:56,897 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:00,350 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:00,350 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:01,301 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:01,301 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:01,302 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:01,519 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:01,521 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:01,521 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:03,499 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:03,500 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:03,500 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:03,500 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:03,500 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:03,500 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:04,551 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:04,551 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:04,552 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:04,771 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:04,773 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:04,773 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:08,597 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:08,597 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:08,597 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:08,598 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:08,598 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:08,598 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:09,606 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:09,607 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:09,607 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:09,821 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:09,823 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:09,823 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,710 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,711 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:18,938 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,939 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,939 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:18,939 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,939 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,939 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ces', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,042 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ces', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,042 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,593 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,593 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,593 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,593 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,594 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,594 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,746 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,747 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,747 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,747 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,747 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,747 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,909 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,909 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,909 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,910 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,910 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,910 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:20,960 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:20,961 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:20,961 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:21,171 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:21,173 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:21,173 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:21,937 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 't', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:21,937 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:21,937 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:21,938 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 't', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:21,938 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:21,938 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,025 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'te', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,025 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,025 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,026 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'te', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,026 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,026 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,149 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'tes', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,149 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,149 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,150 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'tes', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,150 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,150 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,244 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,244 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,967 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,967 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,967 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,967 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,968 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,968 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:24,019 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:24,019 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:24,020 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:24,239 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:24,240 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:24,242 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:24,643 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:24,643 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:24,643 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:24,643 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:24,644 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:24,644 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:25,606 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:25,606 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:25,607 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:25,826 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:25,827 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:25,827 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:32,545 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:32,545 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:32,545 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:32,762 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:32,764 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:32,764 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:35,295 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:35,295 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:35,295 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:35,510 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:35,512 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:36,660 - src.gui.main_window - INFO - 开始融合处理...
2025-06-29 08:13:36,661 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 08:13:36,661 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 23, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 块大小设置为: 23
2025-06-29 08:13:36,661 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 08:13:36,662 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 08:13:36,662 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:36,662 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.5
2025-06-29 08:13:36,662 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=horizontal_scroll, 运动速度=1.0
2025-06-29 08:14:09,263 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 08:14:10,076 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1202
2025-06-29 08:14:10,076 - src.effects.text_content_controller - INFO - 文字内容: 'test ', 位置模式: 静态位置
2025-06-29 08:14:10,910 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1202 帧
2025-06-29 08:14:10,910 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 08:14:10,910 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 34.25秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 08:14:10,910 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 34.24817085266113, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 08:14:11,076 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 08:14:11,077 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 08:14:11,077 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 08:14:11,087 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 08:14:11,088 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 08:14:11,090 - src.gui.main_window - INFO - 融合处理成功完成
2025-06-29 08:14:33,569 - src.gui.main_window - INFO - 正在导出视频到: /Users/<USER>/Documents/augment-projects/X/output/2.mp4
2025-06-29 08:14:34,494 - src.fusion.overlay_fusion - INFO - 已写入 100/1202 帧
2025-06-29 08:14:35,419 - src.fusion.overlay_fusion - INFO - 已写入 200/1202 帧
2025-06-29 08:14:36,339 - src.fusion.overlay_fusion - INFO - 已写入 300/1202 帧
2025-06-29 08:14:37,234 - src.fusion.overlay_fusion - INFO - 已写入 400/1202 帧
2025-06-29 08:14:38,107 - src.fusion.overlay_fusion - INFO - 已写入 500/1202 帧
2025-06-29 08:14:38,961 - src.fusion.overlay_fusion - INFO - 已写入 600/1202 帧
2025-06-29 08:14:39,814 - src.fusion.overlay_fusion - INFO - 已写入 700/1202 帧
2025-06-29 08:14:40,708 - src.fusion.overlay_fusion - INFO - 已写入 800/1202 帧
2025-06-29 08:14:41,690 - src.fusion.overlay_fusion - INFO - 已写入 900/1202 帧
2025-06-29 08:14:42,559 - src.fusion.overlay_fusion - INFO - 已写入 1000/1202 帧
2025-06-29 08:14:43,432 - src.fusion.overlay_fusion - INFO - 已写入 1100/1202 帧
2025-06-29 08:14:44,388 - src.fusion.overlay_fusion - INFO - 已写入 1200/1202 帧
2025-06-29 08:14:44,410 - src.fusion.overlay_fusion - INFO - 叠加融合视频保存完成: /Users/<USER>/Documents/augment-projects/X/output/2.mp4
2025-06-29 08:14:44,410 - src.gui.main_window - INFO - 视频导出成功: /Users/<USER>/Documents/augment-projects/X/output/2.mp4
2025-06-29 08:15:37,527 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 08:18:42,719 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,720 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,720 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,720 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,720 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:18:42,720 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:18:42,721 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:18:42,721 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:18:42,721 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 08:18:42,721 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,721 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,721 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,722 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,722 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:18:42,722 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:18:42,722 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:18:42,722 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:18:42,722 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 08:18:42,722 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,722 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,722 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,722 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,723 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:18:42,723 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:18:42,723 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:18:42,723 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:18:42,723 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 08:18:42,723 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 08:18:42,723 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 08:18:42,724 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 08:18:42,724 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 08:18:42,724 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 08:18:43,083 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 08:18:43,083 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 08:18:43,083 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 08:18:43,083 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 08:18:43,100 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 08:18:43,101 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 08:18:43,101 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 08:18:51,548 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 08:27:14,596 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:27:14,596 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:27:14,597 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:27:14,597 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:27:14,597 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:27:14,597 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:27:14,597 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:27:14,598 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:27:14,598 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 08:27:14,598 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:27:14,598 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:27:14,598 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:27:14,598 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:27:14,598 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:27:14,598 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:27:14,599 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:27:14,599 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:27:14,599 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 08:27:14,599 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:27:14,599 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:27:14,599 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:27:14,599 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:27:14,599 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:27:14,599 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:27:14,599 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:27:14,600 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:27:14,600 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 08:27:14,600 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 08:27:14,600 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 08:27:14,600 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 08:27:14,600 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 08:27:14,600 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 08:27:14,932 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 08:27:14,932 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 08:27:14,932 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 08:27:14,932 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 08:27:14,950 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 08:27:14,951 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 08:27:14,951 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 09:17:03,431 - test - INFO - 播放状态变更: 播放
2025-06-29 09:17:03,431 - test - ERROR - 视频加载失败 [/test/video.mp4]: 测试错误
2025-06-29 09:17:03,431 - test - ERROR - 帧处理失败 [帧号: 10]: 帧错误
2025-06-29 09:17:03,431 - test - ERROR - 播放操作失败 [播放]: 播放错误
2025-06-29 09:17:03,432 - test - INFO - 错误历史已清除
2025-06-29 09:17:03,435 - test - ERROR - 播放操作失败 [失败操作]: 测试失败
2025-06-29 09:17:03,436 - test - INFO - 播放状态变更: 播放
2025-06-29 09:18:05,272 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 09:18:05,272 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 09:18:05,272 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 09:18:05,273 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 09:18:05,273 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 09:18:05,273 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 09:18:05,273 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 09:18:05,273 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 09:18:05,273 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 09:18:05,274 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 09:18:05,274 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 09:18:05,274 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 09:18:05,274 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 09:18:05,274 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 09:18:05,274 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 09:18:05,274 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 09:18:05,274 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 09:18:05,274 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 09:18:05,275 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 09:18:05,275 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 09:18:05,275 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 09:18:05,275 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 09:18:05,275 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 09:18:05,275 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 09:18:05,275 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 09:18:05,275 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 09:18:05,275 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 09:18:05,275 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 09:18:05,276 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 09:18:05,276 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 09:18:05,276 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 09:18:05,276 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 09:18:05,625 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 09:18:05,625 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 09:18:05,625 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 09:18:05,626 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 09:18:05,643 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 09:18:05,643 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 09:18:05,643 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 10:39:04,579 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:39:04,579 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:39:04,580 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:39:04,580 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:39:04,580 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:39:04,580 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:39:04,580 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:39:04,581 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:39:04,581 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 10:39:04,581 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:39:04,581 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:39:04,581 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:39:04,581 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:39:04,581 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:39:04,581 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:39:04,581 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:39:04,581 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:39:04,582 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 10:39:04,582 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:39:04,582 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:39:04,582 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:39:04,582 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:39:04,582 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:39:04,582 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:39:04,582 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:39:04,582 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:39:04,582 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 10:39:04,583 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 10:39:04,583 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 10:39:04,583 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 10:39:04,583 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 10:39:04,583 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 10:39:05,273 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 10:39:05,274 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 10:39:05,274 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 10:39:05,274 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 10:39:05,314 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 10:39:05,314 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 10:39:05,314 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 10:39:37,053 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-29 10:39:37,054 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-29 10:39:37,072 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 10:39:37,072 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 10:39:37,095 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 10:39:42,066 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-29 10:39:42,066 - src.gui.main_window - INFO - B视频播放状态: 暂停
2025-06-29 10:39:42,093 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 10:39:42,093 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 10:39:42,153 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 10:39:50,938 - src.gui.video_player - INFO - 播放状态变更: 播放
2025-06-29 10:39:50,938 - src.gui.main_window - INFO - A视频播放状态: 播放
2025-06-29 10:39:50,947 - src.gui.main_window - INFO - A视频播放位置: 第0帧
2025-06-29 10:39:50,994 - src.gui.main_window - INFO - A视频播放位置: 第1帧
2025-06-29 10:39:51,048 - src.gui.main_window - INFO - A视频播放位置: 第2帧
2025-06-29 10:39:51,101 - src.gui.main_window - INFO - A视频播放位置: 第3帧
2025-06-29 10:39:51,155 - src.gui.main_window - INFO - A视频播放位置: 第4帧
2025-06-29 10:39:51,208 - src.gui.main_window - INFO - A视频播放位置: 第5帧
2025-06-29 10:39:51,260 - src.gui.main_window - INFO - A视频播放位置: 第6帧
2025-06-29 10:39:51,309 - src.gui.main_window - INFO - A视频播放位置: 第7帧
2025-06-29 10:39:51,360 - src.gui.main_window - INFO - A视频播放位置: 第8帧
2025-06-29 10:39:51,414 - src.gui.main_window - INFO - A视频播放位置: 第9帧
2025-06-29 10:39:51,469 - src.gui.main_window - INFO - A视频播放位置: 第10帧
2025-06-29 10:39:51,520 - src.gui.main_window - INFO - A视频播放位置: 第11帧
2025-06-29 10:39:51,575 - src.gui.main_window - INFO - A视频播放位置: 第12帧
2025-06-29 10:39:51,633 - src.gui.main_window - INFO - A视频播放位置: 第13帧
2025-06-29 10:39:51,685 - src.gui.main_window - INFO - A视频播放位置: 第14帧
2025-06-29 10:39:51,742 - src.gui.main_window - INFO - A视频播放位置: 第15帧
2025-06-29 10:39:51,798 - src.gui.main_window - INFO - A视频播放位置: 第16帧
2025-06-29 10:39:51,854 - src.gui.main_window - INFO - A视频播放位置: 第17帧
2025-06-29 10:39:51,910 - src.gui.main_window - INFO - A视频播放位置: 第18帧
2025-06-29 10:39:51,969 - src.gui.main_window - INFO - A视频播放位置: 第19帧
2025-06-29 10:39:52,027 - src.gui.main_window - INFO - A视频播放位置: 第20帧
2025-06-29 10:39:52,084 - src.gui.main_window - INFO - A视频播放位置: 第21帧
2025-06-29 10:39:52,140 - src.gui.main_window - INFO - A视频播放位置: 第22帧
2025-06-29 10:39:52,199 - src.gui.main_window - INFO - A视频播放位置: 第23帧
2025-06-29 10:39:52,255 - src.gui.main_window - INFO - A视频播放位置: 第24帧
2025-06-29 10:39:52,313 - src.gui.main_window - INFO - A视频播放位置: 第25帧
2025-06-29 10:39:52,371 - src.gui.main_window - INFO - A视频播放位置: 第26帧
2025-06-29 10:39:52,427 - src.gui.main_window - INFO - A视频播放位置: 第27帧
2025-06-29 10:39:52,489 - src.gui.main_window - INFO - A视频播放位置: 第28帧
2025-06-29 10:39:52,547 - src.gui.main_window - INFO - A视频播放位置: 第29帧
2025-06-29 10:39:52,609 - src.gui.main_window - INFO - A视频播放位置: 第30帧
2025-06-29 10:39:52,669 - src.gui.main_window - INFO - A视频播放位置: 第31帧
2025-06-29 10:39:52,727 - src.gui.main_window - INFO - A视频播放位置: 第32帧
2025-06-29 10:39:52,790 - src.gui.main_window - INFO - A视频播放位置: 第33帧
2025-06-29 10:39:52,852 - src.gui.main_window - INFO - A视频播放位置: 第34帧
2025-06-29 10:39:52,914 - src.gui.main_window - INFO - A视频播放位置: 第35帧
2025-06-29 10:39:52,976 - src.gui.main_window - INFO - A视频播放位置: 第36帧
2025-06-29 10:39:53,035 - src.gui.main_window - INFO - A视频播放位置: 第37帧
2025-06-29 10:39:53,095 - src.gui.main_window - INFO - A视频播放位置: 第38帧
2025-06-29 10:39:53,159 - src.gui.main_window - INFO - A视频播放位置: 第39帧
2025-06-29 10:39:53,224 - src.gui.main_window - INFO - A视频播放位置: 第40帧
2025-06-29 10:39:53,289 - src.gui.main_window - INFO - A视频播放位置: 第41帧
2025-06-29 10:39:53,351 - src.gui.main_window - INFO - A视频播放位置: 第42帧
2025-06-29 10:39:53,416 - src.gui.main_window - INFO - A视频播放位置: 第43帧
2025-06-29 10:39:53,480 - src.gui.main_window - INFO - A视频播放位置: 第44帧
2025-06-29 10:39:53,544 - src.gui.main_window - INFO - A视频播放位置: 第45帧
2025-06-29 10:39:53,610 - src.gui.main_window - INFO - A视频播放位置: 第46帧
2025-06-29 10:39:53,673 - src.gui.main_window - INFO - A视频播放位置: 第47帧
2025-06-29 10:39:53,737 - src.gui.main_window - INFO - A视频播放位置: 第48帧
2025-06-29 10:39:53,803 - src.gui.main_window - INFO - A视频播放位置: 第49帧
2025-06-29 10:39:53,866 - src.gui.main_window - INFO - A视频播放位置: 第50帧
2025-06-29 10:39:53,929 - src.gui.main_window - INFO - A视频播放位置: 第51帧
2025-06-29 10:39:53,994 - src.gui.main_window - INFO - A视频播放位置: 第52帧
2025-06-29 10:39:54,058 - src.gui.main_window - INFO - A视频播放位置: 第53帧
2025-06-29 10:39:54,122 - src.gui.main_window - INFO - A视频播放位置: 第54帧
2025-06-29 10:39:54,185 - src.gui.main_window - INFO - A视频播放位置: 第55帧
2025-06-29 10:39:54,251 - src.gui.main_window - INFO - A视频播放位置: 第56帧
2025-06-29 10:39:54,316 - src.gui.main_window - INFO - A视频播放位置: 第57帧
2025-06-29 10:39:54,384 - src.gui.main_window - INFO - A视频播放位置: 第58帧
2025-06-29 10:39:54,451 - src.gui.main_window - INFO - A视频播放位置: 第59帧
2025-06-29 10:39:54,518 - src.gui.main_window - INFO - A视频播放位置: 第60帧
2025-06-29 10:39:54,590 - src.gui.main_window - INFO - A视频播放位置: 第61帧
2025-06-29 10:39:54,655 - src.gui.main_window - INFO - A视频播放位置: 第62帧
2025-06-29 10:39:54,721 - src.gui.main_window - INFO - A视频播放位置: 第63帧
2025-06-29 10:39:54,788 - src.gui.main_window - INFO - A视频播放位置: 第64帧
2025-06-29 10:39:54,856 - src.gui.main_window - INFO - A视频播放位置: 第65帧
2025-06-29 10:39:54,926 - src.gui.main_window - INFO - A视频播放位置: 第66帧
2025-06-29 10:39:54,995 - src.gui.main_window - INFO - A视频播放位置: 第67帧
2025-06-29 10:39:55,062 - src.gui.main_window - INFO - A视频播放位置: 第68帧
2025-06-29 10:39:55,127 - src.gui.main_window - INFO - A视频播放位置: 第69帧
2025-06-29 10:39:55,198 - src.gui.main_window - INFO - A视频播放位置: 第70帧
2025-06-29 10:39:55,271 - src.gui.main_window - INFO - A视频播放位置: 第71帧
2025-06-29 10:39:55,347 - src.gui.main_window - INFO - A视频播放位置: 第72帧
2025-06-29 10:39:55,422 - src.gui.main_window - INFO - A视频播放位置: 第73帧
2025-06-29 10:39:55,497 - src.gui.main_window - INFO - A视频播放位置: 第74帧
2025-06-29 10:39:55,575 - src.gui.main_window - INFO - A视频播放位置: 第75帧
2025-06-29 10:39:55,645 - src.gui.main_window - INFO - A视频播放位置: 第76帧
2025-06-29 10:39:55,716 - src.gui.main_window - INFO - A视频播放位置: 第77帧
2025-06-29 10:39:55,791 - src.gui.main_window - INFO - A视频播放位置: 第78帧
2025-06-29 10:39:55,866 - src.gui.main_window - INFO - A视频播放位置: 第79帧
2025-06-29 10:39:55,941 - src.gui.main_window - INFO - A视频播放位置: 第80帧
2025-06-29 10:39:56,016 - src.gui.main_window - INFO - A视频播放位置: 第81帧
2025-06-29 10:39:56,093 - src.gui.main_window - INFO - A视频播放位置: 第82帧
2025-06-29 10:39:56,166 - src.gui.main_window - INFO - A视频播放位置: 第83帧
2025-06-29 10:39:56,245 - src.gui.main_window - INFO - A视频播放位置: 第84帧
2025-06-29 10:39:56,322 - src.gui.main_window - INFO - A视频播放位置: 第85帧
2025-06-29 10:39:56,397 - src.gui.main_window - INFO - A视频播放位置: 第86帧
2025-06-29 10:39:56,475 - src.gui.main_window - INFO - A视频播放位置: 第87帧
2025-06-29 10:39:56,557 - src.gui.main_window - INFO - A视频播放位置: 第88帧
2025-06-29 10:39:56,616 - src.gui.main_window - INFO - A视频播放位置: 第89帧
2025-06-29 10:39:56,670 - src.gui.main_window - INFO - A视频播放位置: 第90帧
2025-06-29 10:39:56,730 - src.gui.main_window - INFO - A视频播放位置: 第91帧
2025-06-29 10:39:56,788 - src.gui.main_window - INFO - A视频播放位置: 第92帧
2025-06-29 10:39:56,847 - src.gui.main_window - INFO - A视频播放位置: 第93帧
2025-06-29 10:39:56,902 - src.gui.main_window - INFO - A视频播放位置: 第94帧
2025-06-29 10:39:56,963 - src.gui.main_window - INFO - A视频播放位置: 第95帧
2025-06-29 10:39:57,018 - src.gui.main_window - INFO - A视频播放位置: 第96帧
2025-06-29 10:39:57,077 - src.gui.main_window - INFO - A视频播放位置: 第97帧
2025-06-29 10:39:57,136 - src.gui.main_window - INFO - A视频播放位置: 第98帧
2025-06-29 10:39:57,195 - src.gui.main_window - INFO - A视频播放位置: 第99帧
2025-06-29 10:39:57,252 - src.gui.main_window - INFO - A视频播放位置: 第100帧
2025-06-29 10:39:57,312 - src.gui.main_window - INFO - A视频播放位置: 第101帧
2025-06-29 10:39:57,373 - src.gui.main_window - INFO - A视频播放位置: 第102帧
2025-06-29 10:39:57,435 - src.gui.main_window - INFO - A视频播放位置: 第103帧
2025-06-29 10:39:57,498 - src.gui.main_window - INFO - A视频播放位置: 第104帧
2025-06-29 10:39:57,557 - src.gui.main_window - INFO - A视频播放位置: 第105帧
2025-06-29 10:39:57,620 - src.gui.main_window - INFO - A视频播放位置: 第106帧
2025-06-29 10:39:57,682 - src.gui.main_window - INFO - A视频播放位置: 第107帧
2025-06-29 10:39:57,741 - src.gui.main_window - INFO - A视频播放位置: 第108帧
2025-06-29 10:39:57,804 - src.gui.main_window - INFO - A视频播放位置: 第109帧
2025-06-29 10:39:57,865 - src.gui.main_window - INFO - A视频播放位置: 第110帧
2025-06-29 10:39:57,926 - src.gui.main_window - INFO - A视频播放位置: 第111帧
2025-06-29 10:39:57,990 - src.gui.main_window - INFO - A视频播放位置: 第112帧
2025-06-29 10:39:58,053 - src.gui.main_window - INFO - A视频播放位置: 第113帧
2025-06-29 10:39:58,117 - src.gui.main_window - INFO - A视频播放位置: 第114帧
2025-06-29 10:39:58,184 - src.gui.main_window - INFO - A视频播放位置: 第115帧
2025-06-29 10:39:58,250 - src.gui.main_window - INFO - A视频播放位置: 第116帧
2025-06-29 10:39:58,313 - src.gui.main_window - INFO - A视频播放位置: 第117帧
2025-06-29 10:39:58,375 - src.gui.main_window - INFO - A视频播放位置: 第118帧
2025-06-29 10:39:58,437 - src.gui.main_window - INFO - A视频播放位置: 第119帧
2025-06-29 10:39:58,502 - src.gui.main_window - INFO - A视频播放位置: 第120帧
2025-06-29 10:39:58,570 - src.gui.main_window - INFO - A视频播放位置: 第121帧
2025-06-29 10:39:58,632 - src.gui.main_window - INFO - A视频播放位置: 第122帧
2025-06-29 10:39:58,695 - src.gui.main_window - INFO - A视频播放位置: 第123帧
2025-06-29 10:39:58,762 - src.gui.main_window - INFO - A视频播放位置: 第124帧
2025-06-29 10:39:58,828 - src.gui.main_window - INFO - A视频播放位置: 第125帧
2025-06-29 10:39:58,896 - src.gui.main_window - INFO - A视频播放位置: 第126帧
2025-06-29 10:39:58,964 - src.gui.main_window - INFO - A视频播放位置: 第127帧
2025-06-29 10:39:59,033 - src.gui.main_window - INFO - A视频播放位置: 第128帧
2025-06-29 10:39:59,101 - src.gui.main_window - INFO - A视频播放位置: 第129帧
2025-06-29 10:39:59,167 - src.gui.main_window - INFO - A视频播放位置: 第130帧
2025-06-29 10:39:59,233 - src.gui.main_window - INFO - A视频播放位置: 第131帧
2025-06-29 10:39:59,309 - src.gui.main_window - INFO - A视频播放位置: 第132帧
2025-06-29 10:39:59,375 - src.gui.main_window - INFO - A视频播放位置: 第133帧
2025-06-29 10:39:59,444 - src.gui.main_window - INFO - A视频播放位置: 第134帧
2025-06-29 10:39:59,513 - src.gui.main_window - INFO - A视频播放位置: 第135帧
2025-06-29 10:39:59,581 - src.gui.main_window - INFO - A视频播放位置: 第136帧
2025-06-29 10:39:59,648 - src.gui.main_window - INFO - A视频播放位置: 第137帧
2025-06-29 10:39:59,718 - src.gui.main_window - INFO - A视频播放位置: 第138帧
2025-06-29 10:39:59,790 - src.gui.main_window - INFO - A视频播放位置: 第139帧
2025-06-29 10:39:59,860 - src.gui.main_window - INFO - A视频播放位置: 第140帧
2025-06-29 10:39:59,929 - src.gui.main_window - INFO - A视频播放位置: 第141帧
2025-06-29 10:39:59,999 - src.gui.main_window - INFO - A视频播放位置: 第142帧
2025-06-29 10:40:00,072 - src.gui.main_window - INFO - A视频播放位置: 第143帧
2025-06-29 10:40:00,148 - src.gui.main_window - INFO - A视频播放位置: 第144帧
2025-06-29 10:40:00,221 - src.gui.main_window - INFO - A视频播放位置: 第145帧
2025-06-29 10:40:00,299 - src.gui.main_window - INFO - A视频播放位置: 第146帧
2025-06-29 10:40:00,372 - src.gui.main_window - INFO - A视频播放位置: 第147帧
2025-06-29 10:40:00,448 - src.gui.main_window - INFO - A视频播放位置: 第148帧
2025-06-29 10:40:00,524 - src.gui.main_window - INFO - A视频播放位置: 第149帧
2025-06-29 10:40:00,603 - src.gui.main_window - INFO - A视频播放位置: 第150帧
2025-06-29 10:40:00,682 - src.gui.main_window - INFO - A视频播放位置: 第151帧
2025-06-29 10:40:00,758 - src.gui.main_window - INFO - A视频播放位置: 第152帧
2025-06-29 10:40:00,835 - src.gui.main_window - INFO - A视频播放位置: 第153帧
2025-06-29 10:40:00,912 - src.gui.main_window - INFO - A视频播放位置: 第154帧
2025-06-29 10:40:00,991 - src.gui.main_window - INFO - A视频播放位置: 第155帧
2025-06-29 10:40:01,067 - src.gui.main_window - INFO - A视频播放位置: 第156帧
2025-06-29 10:40:01,146 - src.gui.main_window - INFO - A视频播放位置: 第157帧
2025-06-29 10:40:01,223 - src.gui.main_window - INFO - A视频播放位置: 第158帧
2025-06-29 10:40:01,303 - src.gui.main_window - INFO - A视频播放位置: 第159帧
2025-06-29 10:40:01,383 - src.gui.main_window - INFO - A视频播放位置: 第160帧
2025-06-29 10:40:01,436 - src.gui.main_window - INFO - A视频播放位置: 第161帧
2025-06-29 10:40:01,492 - src.gui.main_window - INFO - A视频播放位置: 第162帧
2025-06-29 10:40:01,547 - src.gui.main_window - INFO - A视频播放位置: 第163帧
2025-06-29 10:40:01,608 - src.gui.main_window - INFO - A视频播放位置: 第164帧
2025-06-29 10:40:01,666 - src.gui.main_window - INFO - A视频播放位置: 第165帧
2025-06-29 10:40:01,721 - src.gui.main_window - INFO - A视频播放位置: 第166帧
2025-06-29 10:40:01,779 - src.gui.main_window - INFO - A视频播放位置: 第167帧
2025-06-29 10:40:01,832 - src.gui.main_window - INFO - A视频播放位置: 第168帧
2025-06-29 10:40:01,891 - src.gui.main_window - INFO - A视频播放位置: 第169帧
2025-06-29 10:40:01,947 - src.gui.main_window - INFO - A视频播放位置: 第170帧
2025-06-29 10:40:02,006 - src.gui.main_window - INFO - A视频播放位置: 第171帧
2025-06-29 10:40:02,068 - src.gui.main_window - INFO - A视频播放位置: 第172帧
2025-06-29 10:40:02,129 - src.gui.main_window - INFO - A视频播放位置: 第173帧
2025-06-29 10:40:02,201 - src.gui.main_window - INFO - A视频播放位置: 第174帧
2025-06-29 10:40:02,261 - src.gui.main_window - INFO - A视频播放位置: 第175帧
2025-06-29 10:40:02,319 - src.gui.main_window - INFO - A视频播放位置: 第176帧
2025-06-29 10:40:02,377 - src.gui.main_window - INFO - A视频播放位置: 第177帧
2025-06-29 10:40:02,435 - src.gui.main_window - INFO - A视频播放位置: 第178帧
2025-06-29 10:40:02,497 - src.gui.main_window - INFO - A视频播放位置: 第179帧
2025-06-29 10:40:02,557 - src.gui.main_window - INFO - A视频播放位置: 第180帧
2025-06-29 10:40:02,617 - src.gui.main_window - INFO - A视频播放位置: 第181帧
2025-06-29 10:40:02,678 - src.gui.main_window - INFO - A视频播放位置: 第182帧
2025-06-29 10:40:02,738 - src.gui.main_window - INFO - A视频播放位置: 第183帧
2025-06-29 10:40:02,798 - src.gui.main_window - INFO - A视频播放位置: 第184帧
2025-06-29 10:40:02,860 - src.gui.main_window - INFO - A视频播放位置: 第185帧
2025-06-29 10:40:02,922 - src.gui.main_window - INFO - A视频播放位置: 第186帧
2025-06-29 10:40:02,986 - src.gui.main_window - INFO - A视频播放位置: 第187帧
2025-06-29 10:40:03,050 - src.gui.main_window - INFO - A视频播放位置: 第188帧
2025-06-29 10:40:03,115 - src.gui.main_window - INFO - A视频播放位置: 第189帧
2025-06-29 10:40:03,181 - src.gui.main_window - INFO - A视频播放位置: 第190帧
2025-06-29 10:40:03,246 - src.gui.main_window - INFO - A视频播放位置: 第191帧
2025-06-29 10:40:03,315 - src.gui.main_window - INFO - A视频播放位置: 第192帧
2025-06-29 10:40:03,380 - src.gui.main_window - INFO - A视频播放位置: 第193帧
2025-06-29 10:40:03,444 - src.gui.main_window - INFO - A视频播放位置: 第194帧
2025-06-29 10:40:03,512 - src.gui.main_window - INFO - A视频播放位置: 第195帧
2025-06-29 10:40:03,579 - src.gui.main_window - INFO - A视频播放位置: 第196帧
2025-06-29 10:40:03,646 - src.gui.main_window - INFO - A视频播放位置: 第197帧
2025-06-29 10:40:03,713 - src.gui.main_window - INFO - A视频播放位置: 第198帧
2025-06-29 10:40:03,781 - src.gui.main_window - INFO - A视频播放位置: 第199帧
2025-06-29 10:40:03,847 - src.gui.main_window - INFO - A视频播放位置: 第200帧
2025-06-29 10:40:03,913 - src.gui.main_window - INFO - A视频播放位置: 第201帧
2025-06-29 10:40:03,980 - src.gui.main_window - INFO - A视频播放位置: 第202帧
2025-06-29 10:40:04,048 - src.gui.main_window - INFO - A视频播放位置: 第203帧
2025-06-29 10:40:04,115 - src.gui.main_window - INFO - A视频播放位置: 第204帧
2025-06-29 10:40:04,184 - src.gui.main_window - INFO - A视频播放位置: 第205帧
2025-06-29 10:40:04,250 - src.gui.main_window - INFO - A视频播放位置: 第206帧
2025-06-29 10:40:04,320 - src.gui.main_window - INFO - A视频播放位置: 第207帧
2025-06-29 10:40:04,391 - src.gui.main_window - INFO - A视频播放位置: 第208帧
2025-06-29 10:40:04,458 - src.gui.main_window - INFO - A视频播放位置: 第209帧
2025-06-29 10:40:04,531 - src.gui.main_window - INFO - A视频播放位置: 第210帧
2025-06-29 10:40:04,605 - src.gui.main_window - INFO - A视频播放位置: 第211帧
2025-06-29 10:40:04,675 - src.gui.main_window - INFO - A视频播放位置: 第212帧
2025-06-29 10:40:04,746 - src.gui.main_window - INFO - A视频播放位置: 第213帧
2025-06-29 10:40:04,818 - src.gui.main_window - INFO - A视频播放位置: 第214帧
2025-06-29 10:40:04,886 - src.gui.main_window - INFO - A视频播放位置: 第215帧
2025-06-29 10:40:04,957 - src.gui.main_window - INFO - A视频播放位置: 第216帧
2025-06-29 10:40:05,031 - src.gui.main_window - INFO - A视频播放位置: 第217帧
2025-06-29 10:40:05,106 - src.gui.main_window - INFO - A视频播放位置: 第218帧
2025-06-29 10:40:05,178 - src.gui.main_window - INFO - A视频播放位置: 第219帧
2025-06-29 10:40:05,252 - src.gui.main_window - INFO - A视频播放位置: 第220帧
2025-06-29 10:40:05,329 - src.gui.main_window - INFO - A视频播放位置: 第221帧
2025-06-29 10:40:05,406 - src.gui.main_window - INFO - A视频播放位置: 第222帧
2025-06-29 10:40:05,482 - src.gui.main_window - INFO - A视频播放位置: 第223帧
2025-06-29 10:40:05,559 - src.gui.main_window - INFO - A视频播放位置: 第224帧
2025-06-29 10:40:05,635 - src.gui.main_window - INFO - A视频播放位置: 第225帧
2025-06-29 10:40:05,712 - src.gui.main_window - INFO - A视频播放位置: 第226帧
2025-06-29 10:40:05,793 - src.gui.main_window - INFO - A视频播放位置: 第227帧
2025-06-29 10:40:05,871 - src.gui.main_window - INFO - A视频播放位置: 第228帧
2025-06-29 10:40:05,948 - src.gui.main_window - INFO - A视频播放位置: 第229帧
2025-06-29 10:40:06,023 - src.gui.main_window - INFO - A视频播放位置: 第230帧
2025-06-29 10:40:06,100 - src.gui.main_window - INFO - A视频播放位置: 第231帧
2025-06-29 10:40:06,179 - src.gui.main_window - INFO - A视频播放位置: 第232帧
2025-06-29 10:40:06,254 - src.gui.main_window - INFO - A视频播放位置: 第233帧
2025-06-29 10:40:06,330 - src.gui.main_window - INFO - A视频播放位置: 第234帧
2025-06-29 10:40:06,393 - src.gui.main_window - INFO - A视频播放位置: 第235帧
2025-06-29 10:40:06,450 - src.gui.main_window - INFO - A视频播放位置: 第236帧
2025-06-29 10:40:06,521 - src.gui.main_window - INFO - A视频播放位置: 第237帧
2025-06-29 10:40:06,610 - src.gui.main_window - INFO - A视频播放位置: 第238帧
2025-06-29 10:40:06,669 - src.gui.main_window - INFO - A视频播放位置: 第239帧
2025-06-29 10:40:06,729 - src.gui.main_window - INFO - A视频播放位置: 第240帧
2025-06-29 10:40:06,788 - src.gui.main_window - INFO - A视频播放位置: 第241帧
2025-06-29 10:40:06,846 - src.gui.main_window - INFO - A视频播放位置: 第242帧
2025-06-29 10:40:06,904 - src.gui.main_window - INFO - A视频播放位置: 第243帧
2025-06-29 10:40:06,960 - src.gui.main_window - INFO - A视频播放位置: 第244帧
2025-06-29 10:40:07,017 - src.gui.main_window - INFO - A视频播放位置: 第245帧
2025-06-29 10:40:07,074 - src.gui.main_window - INFO - A视频播放位置: 第246帧
2025-06-29 10:40:07,131 - src.gui.main_window - INFO - A视频播放位置: 第247帧
2025-06-29 10:40:07,192 - src.gui.main_window - INFO - A视频播放位置: 第248帧
2025-06-29 10:40:07,252 - src.gui.main_window - INFO - A视频播放位置: 第249帧
2025-06-29 10:40:07,316 - src.gui.main_window - INFO - A视频播放位置: 第250帧
2025-06-29 10:40:07,377 - src.gui.main_window - INFO - A视频播放位置: 第251帧
2025-06-29 10:40:07,437 - src.gui.main_window - INFO - A视频播放位置: 第252帧
2025-06-29 10:40:07,499 - src.gui.main_window - INFO - A视频播放位置: 第253帧
2025-06-29 10:40:07,560 - src.gui.main_window - INFO - A视频播放位置: 第254帧
2025-06-29 10:40:07,622 - src.gui.main_window - INFO - A视频播放位置: 第255帧
2025-06-29 10:40:07,685 - src.gui.main_window - INFO - A视频播放位置: 第256帧
2025-06-29 10:40:07,749 - src.gui.main_window - INFO - A视频播放位置: 第257帧
2025-06-29 10:40:07,809 - src.gui.main_window - INFO - A视频播放位置: 第258帧
2025-06-29 10:40:07,871 - src.gui.main_window - INFO - A视频播放位置: 第259帧
2025-06-29 10:40:07,934 - src.gui.main_window - INFO - A视频播放位置: 第260帧
2025-06-29 10:40:07,994 - src.gui.main_window - INFO - A视频播放位置: 第261帧
2025-06-29 10:40:08,056 - src.gui.main_window - INFO - A视频播放位置: 第262帧
2025-06-29 10:40:08,122 - src.gui.main_window - INFO - A视频播放位置: 第263帧
2025-06-29 10:40:08,184 - src.gui.main_window - INFO - A视频播放位置: 第264帧
2025-06-29 10:40:08,245 - src.gui.main_window - INFO - A视频播放位置: 第265帧
2025-06-29 10:40:08,314 - src.gui.main_window - INFO - A视频播放位置: 第266帧
2025-06-29 10:40:08,381 - src.gui.main_window - INFO - A视频播放位置: 第267帧
2025-06-29 10:40:08,446 - src.gui.main_window - INFO - A视频播放位置: 第268帧
2025-06-29 10:40:08,508 - src.gui.main_window - INFO - A视频播放位置: 第269帧
2025-06-29 10:40:08,572 - src.gui.main_window - INFO - A视频播放位置: 第270帧
2025-06-29 10:40:08,638 - src.gui.main_window - INFO - A视频播放位置: 第271帧
2025-06-29 10:40:08,703 - src.gui.main_window - INFO - A视频播放位置: 第272帧
2025-06-29 10:40:08,768 - src.gui.main_window - INFO - A视频播放位置: 第273帧
2025-06-29 10:40:08,831 - src.gui.main_window - INFO - A视频播放位置: 第274帧
2025-06-29 10:40:08,894 - src.gui.main_window - INFO - A视频播放位置: 第275帧
2025-06-29 10:40:08,894 - src.gui.video_player - INFO - 播放状态变更: 暂停
2025-06-29 10:40:08,902 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-29 10:40:08,902 - src.gui.video_player - INFO - 视频播放完成
2025-06-29 10:40:10,508 - src.gui.video_player - INFO - 播放状态变更: 播放
2025-06-29 10:40:10,508 - src.gui.main_window - INFO - A视频播放状态: 播放
2025-06-29 10:40:10,518 - src.gui.main_window - INFO - A视频播放位置: 第0帧
2025-06-29 10:40:10,568 - src.gui.main_window - INFO - A视频播放位置: 第1帧
2025-06-29 10:40:10,619 - src.gui.main_window - INFO - A视频播放位置: 第2帧
2025-06-29 10:40:10,673 - src.gui.main_window - INFO - A视频播放位置: 第3帧
2025-06-29 10:40:10,723 - src.gui.main_window - INFO - A视频播放位置: 第4帧
2025-06-29 10:40:10,774 - src.gui.main_window - INFO - A视频播放位置: 第5帧
2025-06-29 10:40:10,827 - src.gui.main_window - INFO - A视频播放位置: 第6帧
2025-06-29 10:40:10,879 - src.gui.main_window - INFO - A视频播放位置: 第7帧
2025-06-29 10:40:10,935 - src.gui.main_window - INFO - A视频播放位置: 第8帧
2025-06-29 10:40:10,991 - src.gui.main_window - INFO - A视频播放位置: 第9帧
2025-06-29 10:40:11,041 - src.gui.main_window - INFO - A视频播放位置: 第10帧
2025-06-29 10:40:11,097 - src.gui.main_window - INFO - A视频播放位置: 第11帧
2025-06-29 10:40:11,153 - src.gui.main_window - INFO - A视频播放位置: 第12帧
2025-06-29 10:40:11,209 - src.gui.main_window - INFO - A视频播放位置: 第13帧
2025-06-29 10:40:11,267 - src.gui.main_window - INFO - A视频播放位置: 第14帧
2025-06-29 10:40:11,322 - src.gui.main_window - INFO - A视频播放位置: 第15帧
2025-06-29 10:40:11,375 - src.gui.main_window - INFO - A视频播放位置: 第16帧
2025-06-29 10:40:11,432 - src.gui.main_window - INFO - A视频播放位置: 第17帧
2025-06-29 10:40:11,489 - src.gui.main_window - INFO - A视频播放位置: 第18帧
2025-06-29 10:40:11,543 - src.gui.main_window - INFO - A视频播放位置: 第19帧
2025-06-29 10:40:11,598 - src.gui.main_window - INFO - A视频播放位置: 第20帧
2025-06-29 10:40:11,655 - src.gui.main_window - INFO - A视频播放位置: 第21帧
2025-06-29 10:40:11,710 - src.gui.main_window - INFO - A视频播放位置: 第22帧
2025-06-29 10:40:11,766 - src.gui.main_window - INFO - A视频播放位置: 第23帧
2025-06-29 10:40:11,825 - src.gui.main_window - INFO - A视频播放位置: 第24帧
2025-06-29 10:40:11,886 - src.gui.main_window - INFO - A视频播放位置: 第25帧
2025-06-29 10:40:11,943 - src.gui.main_window - INFO - A视频播放位置: 第26帧
2025-06-29 10:40:12,000 - src.gui.main_window - INFO - A视频播放位置: 第27帧
2025-06-29 10:40:12,057 - src.gui.main_window - INFO - A视频播放位置: 第28帧
2025-06-29 10:40:12,118 - src.gui.main_window - INFO - A视频播放位置: 第29帧
2025-06-29 10:40:12,176 - src.gui.main_window - INFO - A视频播放位置: 第30帧
2025-06-29 10:40:12,237 - src.gui.main_window - INFO - A视频播放位置: 第31帧
2025-06-29 10:40:12,311 - src.gui.main_window - INFO - A视频播放位置: 第32帧
2025-06-29 10:40:12,368 - src.gui.main_window - INFO - A视频播放位置: 第33帧
2025-06-29 10:40:12,426 - src.gui.main_window - INFO - A视频播放位置: 第34帧
2025-06-29 10:40:12,487 - src.gui.main_window - INFO - A视频播放位置: 第35帧
2025-06-29 10:40:12,551 - src.gui.main_window - INFO - A视频播放位置: 第36帧
2025-06-29 10:40:12,615 - src.gui.main_window - INFO - A视频播放位置: 第37帧
2025-06-29 10:40:12,675 - src.gui.main_window - INFO - A视频播放位置: 第38帧
2025-06-29 10:40:12,740 - src.gui.main_window - INFO - A视频播放位置: 第39帧
2025-06-29 10:40:12,803 - src.gui.main_window - INFO - A视频播放位置: 第40帧
2025-06-29 10:40:12,867 - src.gui.main_window - INFO - A视频播放位置: 第41帧
2025-06-29 10:40:12,932 - src.gui.main_window - INFO - A视频播放位置: 第42帧
2025-06-29 10:40:12,995 - src.gui.main_window - INFO - A视频播放位置: 第43帧
2025-06-29 10:40:13,060 - src.gui.main_window - INFO - A视频播放位置: 第44帧
2025-06-29 10:40:13,125 - src.gui.main_window - INFO - A视频播放位置: 第45帧
2025-06-29 10:40:13,189 - src.gui.main_window - INFO - A视频播放位置: 第46帧
2025-06-29 10:40:13,253 - src.gui.main_window - INFO - A视频播放位置: 第47帧
2025-06-29 10:40:13,324 - src.gui.main_window - INFO - A视频播放位置: 第48帧
2025-06-29 10:40:13,358 - src.gui.video_player - INFO - 播放状态变更: 播放
2025-06-29 10:40:13,358 - src.gui.main_window - INFO - B视频播放状态: 播放
2025-06-29 10:40:13,385 - src.gui.main_window - INFO - B视频播放位置: 第0帧
2025-06-29 10:40:13,395 - src.gui.main_window - INFO - A视频播放位置: 第49帧
2025-06-29 10:40:13,440 - src.gui.main_window - INFO - B视频播放位置: 第1帧
2025-06-29 10:40:13,463 - src.gui.main_window - INFO - A视频播放位置: 第50帧
2025-06-29 10:40:13,494 - src.gui.main_window - INFO - B视频播放位置: 第2帧
2025-06-29 10:40:13,532 - src.gui.main_window - INFO - A视频播放位置: 第51帧
2025-06-29 10:40:13,557 - src.gui.main_window - INFO - B视频播放位置: 第3帧
2025-06-29 10:40:13,612 - src.gui.main_window - INFO - A视频播放位置: 第52帧
2025-06-29 10:40:13,626 - src.gui.main_window - INFO - B视频播放位置: 第4帧
2025-06-29 10:40:13,683 - src.gui.main_window - INFO - B视频播放位置: 第5帧
2025-06-29 10:40:13,693 - src.gui.main_window - INFO - A视频播放位置: 第53帧
2025-06-29 10:40:13,744 - src.gui.main_window - INFO - B视频播放位置: 第6帧
2025-06-29 10:40:13,757 - src.gui.main_window - INFO - A视频播放位置: 第54帧
2025-06-29 10:40:13,806 - src.gui.main_window - INFO - B视频播放位置: 第7帧
2025-06-29 10:40:13,827 - src.gui.main_window - INFO - A视频播放位置: 第55帧
2025-06-29 10:40:13,869 - src.gui.main_window - INFO - B视频播放位置: 第8帧
2025-06-29 10:40:13,894 - src.gui.main_window - INFO - A视频播放位置: 第56帧
2025-06-29 10:40:13,925 - src.gui.main_window - INFO - B视频播放位置: 第9帧
2025-06-29 10:40:13,965 - src.gui.main_window - INFO - A视频播放位置: 第57帧
2025-06-29 10:40:13,986 - src.gui.main_window - INFO - B视频播放位置: 第10帧
2025-06-29 10:40:14,031 - src.gui.main_window - INFO - A视频播放位置: 第58帧
2025-06-29 10:40:14,053 - src.gui.main_window - INFO - B视频播放位置: 第11帧
2025-06-29 10:40:14,115 - src.gui.main_window - INFO - A视频播放位置: 第59帧
2025-06-29 10:40:14,125 - src.gui.main_window - INFO - B视频播放位置: 第12帧
2025-06-29 10:40:14,184 - src.gui.main_window - INFO - A视频播放位置: 第60帧
2025-06-29 10:40:14,202 - src.gui.main_window - INFO - B视频播放位置: 第13帧
2025-06-29 10:40:14,259 - src.gui.main_window - INFO - A视频播放位置: 第61帧
2025-06-29 10:40:14,313 - src.gui.main_window - INFO - B视频播放位置: 第14帧
2025-06-29 10:40:14,337 - src.gui.main_window - INFO - A视频播放位置: 第62帧
2025-06-29 10:40:14,364 - src.gui.main_window - INFO - B视频播放位置: 第15帧
2025-06-29 10:40:14,410 - src.gui.main_window - INFO - A视频播放位置: 第63帧
2025-06-29 10:40:14,438 - src.gui.main_window - INFO - B视频播放位置: 第16帧
2025-06-29 10:40:14,479 - src.gui.main_window - INFO - A视频播放位置: 第64帧
2025-06-29 10:40:14,511 - src.gui.main_window - INFO - B视频播放位置: 第17帧
2025-06-29 10:40:14,549 - src.gui.main_window - INFO - A视频播放位置: 第65帧
2025-06-29 10:40:14,585 - src.gui.main_window - INFO - B视频播放位置: 第18帧
2025-06-29 10:40:14,619 - src.gui.main_window - INFO - A视频播放位置: 第66帧
2025-06-29 10:40:14,658 - src.gui.main_window - INFO - B视频播放位置: 第19帧
2025-06-29 10:40:14,690 - src.gui.main_window - INFO - A视频播放位置: 第67帧
2025-06-29 10:40:14,733 - src.gui.main_window - INFO - B视频播放位置: 第20帧
2025-06-29 10:40:14,762 - src.gui.main_window - INFO - A视频播放位置: 第68帧
2025-06-29 10:40:14,802 - src.gui.main_window - INFO - B视频播放位置: 第21帧
2025-06-29 10:40:14,832 - src.gui.main_window - INFO - A视频播放位置: 第69帧
2025-06-29 10:40:14,880 - src.gui.main_window - INFO - B视频播放位置: 第22帧
2025-06-29 10:40:14,904 - src.gui.main_window - INFO - A视频播放位置: 第70帧
2025-06-29 10:40:14,956 - src.gui.main_window - INFO - B视频播放位置: 第23帧
2025-06-29 10:40:14,981 - src.gui.main_window - INFO - A视频播放位置: 第71帧
2025-06-29 10:40:15,036 - src.gui.main_window - INFO - B视频播放位置: 第24帧
2025-06-29 10:40:15,060 - src.gui.main_window - INFO - A视频播放位置: 第72帧
2025-06-29 10:40:15,115 - src.gui.main_window - INFO - B视频播放位置: 第25帧
2025-06-29 10:40:15,140 - src.gui.main_window - INFO - A视频播放位置: 第73帧
2025-06-29 10:40:15,211 - src.gui.main_window - INFO - B视频播放位置: 第26帧
2025-06-29 10:40:15,221 - src.gui.main_window - INFO - A视频播放位置: 第74帧
2025-06-29 10:40:15,332 - src.gui.main_window - INFO - B视频播放位置: 第27帧
2025-06-29 10:40:15,333 - src.gui.main_window - INFO - A视频播放位置: 第75帧
2025-06-29 10:40:15,411 - src.gui.main_window - INFO - B视频播放位置: 第28帧
2025-06-29 10:40:15,422 - src.gui.main_window - INFO - A视频播放位置: 第76帧
2025-06-29 10:40:15,508 - src.gui.main_window - INFO - B视频播放位置: 第29帧
2025-06-29 10:40:15,520 - src.gui.main_window - INFO - A视频播放位置: 第77帧
2025-06-29 10:40:15,604 - src.gui.main_window - INFO - A视频播放位置: 第78帧
2025-06-29 10:40:15,619 - src.gui.main_window - INFO - B视频播放位置: 第30帧
2025-06-29 10:40:15,703 - src.gui.main_window - INFO - B视频播放位置: 第31帧
2025-06-29 10:40:15,713 - src.gui.main_window - INFO - A视频播放位置: 第79帧
2025-06-29 10:40:15,797 - src.gui.main_window - INFO - B视频播放位置: 第32帧
2025-06-29 10:40:15,808 - src.gui.main_window - INFO - A视频播放位置: 第80帧
2025-06-29 10:40:15,891 - src.gui.main_window - INFO - B视频播放位置: 第33帧
2025-06-29 10:40:15,902 - src.gui.main_window - INFO - A视频播放位置: 第81帧
2025-06-29 10:40:15,985 - src.gui.main_window - INFO - B视频播放位置: 第34帧
2025-06-29 10:40:15,998 - src.gui.main_window - INFO - A视频播放位置: 第82帧
2025-06-29 10:40:16,077 - src.gui.main_window - INFO - B视频播放位置: 第35帧
2025-06-29 10:40:16,090 - src.gui.main_window - INFO - A视频播放位置: 第83帧
2025-06-29 10:40:16,166 - src.gui.main_window - INFO - B视频播放位置: 第36帧
2025-06-29 10:40:16,186 - src.gui.main_window - INFO - A视频播放位置: 第84帧
2025-06-29 10:40:16,263 - src.gui.main_window - INFO - B视频播放位置: 第37帧
2025-06-29 10:40:16,316 - src.gui.main_window - INFO - A视频播放位置: 第85帧
2025-06-29 10:40:16,381 - src.gui.main_window - INFO - B视频播放位置: 第38帧
2025-06-29 10:40:16,392 - src.gui.main_window - INFO - A视频播放位置: 第86帧
2025-06-29 10:40:16,469 - src.gui.main_window - INFO - A视频播放位置: 第87帧
2025-06-29 10:40:16,492 - src.gui.main_window - INFO - B视频播放位置: 第39帧
2025-06-29 10:40:16,591 - src.gui.main_window - INFO - A视频播放位置: 第88帧
2025-06-29 10:40:16,605 - src.gui.main_window - INFO - B视频播放位置: 第40帧
2025-06-29 10:40:16,659 - src.gui.main_window - INFO - A视频播放位置: 第89帧
2025-06-29 10:40:16,697 - src.gui.main_window - INFO - B视频播放位置: 第41帧
2025-06-29 10:40:16,713 - src.gui.main_window - INFO - A视频播放位置: 第90帧
2025-06-29 10:40:16,781 - src.gui.main_window - INFO - A视频播放位置: 第91帧
2025-06-29 10:40:16,796 - src.gui.main_window - INFO - B视频播放位置: 第42帧
2025-06-29 10:40:16,838 - src.gui.main_window - INFO - A视频播放位置: 第92帧
2025-06-29 10:40:16,890 - src.gui.main_window - INFO - B视频播放位置: 第43帧
2025-06-29 10:40:16,903 - src.gui.main_window - INFO - A视频播放位置: 第93帧
2025-06-29 10:40:16,969 - src.gui.main_window - INFO - A视频播放位置: 第94帧
2025-06-29 10:40:16,996 - src.gui.main_window - INFO - B视频播放位置: 第44帧
2025-06-29 10:40:17,029 - src.gui.main_window - INFO - A视频播放位置: 第95帧
2025-06-29 10:40:17,100 - src.gui.main_window - INFO - B视频播放位置: 第45帧
2025-06-29 10:40:17,101 - src.gui.main_window - INFO - A视频播放位置: 第96帧
2025-06-29 10:40:17,172 - src.gui.main_window - INFO - A视频播放位置: 第97帧
2025-06-29 10:40:17,213 - src.gui.main_window - INFO - B视频播放位置: 第46帧
2025-06-29 10:40:17,230 - src.gui.main_window - INFO - A视频播放位置: 第98帧
2025-06-29 10:40:17,336 - src.gui.main_window - INFO - A视频播放位置: 第99帧
2025-06-29 10:40:17,338 - src.gui.main_window - INFO - B视频播放位置: 第47帧
2025-06-29 10:40:17,406 - src.gui.main_window - INFO - A视频播放位置: 第100帧
2025-06-29 10:40:17,436 - src.gui.main_window - INFO - B视频播放位置: 第48帧
2025-06-29 10:40:17,467 - src.gui.main_window - INFO - A视频播放位置: 第101帧
2025-06-29 10:40:17,550 - src.gui.main_window - INFO - B视频播放位置: 第49帧
2025-06-29 10:40:17,560 - src.gui.main_window - INFO - A视频播放位置: 第102帧
2025-06-29 10:40:17,634 - src.gui.main_window - INFO - A视频播放位置: 第103帧
2025-06-29 10:40:17,667 - src.gui.main_window - INFO - B视频播放位置: 第50帧
2025-06-29 10:40:17,695 - src.gui.main_window - INFO - A视频播放位置: 第104帧
2025-06-29 10:40:17,771 - src.gui.main_window - INFO - A视频播放位置: 第105帧
2025-06-29 10:40:17,783 - src.gui.main_window - INFO - B视频播放位置: 第51帧
2025-06-29 10:40:17,849 - src.gui.main_window - INFO - A视频播放位置: 第106帧
2025-06-29 10:40:17,887 - src.gui.main_window - INFO - B视频播放位置: 第52帧
2025-06-29 10:40:17,909 - src.gui.main_window - INFO - A视频播放位置: 第107帧
2025-06-29 10:40:17,985 - src.gui.main_window - INFO - A视频播放位置: 第108帧
2025-06-29 10:40:17,998 - src.gui.main_window - INFO - B视频播放位置: 第53帧
2025-06-29 10:40:18,046 - src.gui.main_window - INFO - A视频播放位置: 第109帧
2025-06-29 10:40:18,113 - src.gui.main_window - INFO - A视频播放位置: 第110帧
2025-06-29 10:40:18,128 - src.gui.main_window - INFO - B视频播放位置: 第54帧
2025-06-29 10:40:18,191 - src.gui.main_window - INFO - A视频播放位置: 第111帧
2025-06-29 10:40:18,242 - src.gui.main_window - INFO - B视频播放位置: 第55帧
2025-06-29 10:40:18,258 - src.gui.main_window - INFO - A视频播放位置: 第112帧
2025-06-29 10:40:18,346 - src.gui.main_window - INFO - A视频播放位置: 第113帧
2025-06-29 10:40:18,368 - src.gui.main_window - INFO - B视频播放位置: 第56帧
2025-06-29 10:40:18,413 - src.gui.main_window - INFO - A视频播放位置: 第114帧
2025-06-29 10:40:18,488 - src.gui.main_window - INFO - B视频播放位置: 第57帧
2025-06-29 10:40:18,498 - src.gui.main_window - INFO - A视频播放位置: 第115帧
2025-06-29 10:40:18,583 - src.gui.main_window - INFO - A视频播放位置: 第116帧
2025-06-29 10:40:18,621 - src.gui.main_window - INFO - B视频播放位置: 第58帧
2025-06-29 10:40:18,651 - src.gui.main_window - INFO - A视频播放位置: 第117帧
2025-06-29 10:40:18,734 - src.gui.main_window - INFO - A视频播放位置: 第118帧
2025-06-29 10:40:18,745 - src.gui.main_window - INFO - B视频播放位置: 第59帧
2025-06-29 10:40:18,802 - src.gui.main_window - INFO - A视频播放位置: 第119帧
2025-06-29 10:40:18,869 - src.gui.main_window - INFO - A视频播放位置: 第120帧
2025-06-29 10:40:18,882 - src.gui.main_window - INFO - B视频播放位置: 第60帧
2025-06-29 10:40:18,940 - src.gui.main_window - INFO - A视频播放位置: 第121帧
2025-06-29 10:40:19,010 - src.gui.main_window - INFO - B视频播放位置: 第61帧
2025-06-29 10:40:19,023 - src.gui.main_window - INFO - A视频播放位置: 第122帧
2025-06-29 10:40:19,104 - src.gui.main_window - INFO - A视频播放位置: 第123帧
2025-06-29 10:40:19,131 - src.gui.main_window - INFO - B视频播放位置: 第62帧
2025-06-29 10:40:19,175 - src.gui.main_window - INFO - A视频播放位置: 第124帧
2025-06-29 10:40:19,253 - src.gui.main_window - INFO - A视频播放位置: 第125帧
2025-06-29 10:40:19,265 - src.gui.main_window - INFO - B视频播放位置: 第63帧
2025-06-29 10:40:19,347 - src.gui.main_window - INFO - A视频播放位置: 第126帧
2025-06-29 10:40:19,400 - src.gui.main_window - INFO - B视频播放位置: 第64帧
2025-06-29 10:40:19,414 - src.gui.main_window - INFO - A视频播放位置: 第127帧
2025-06-29 10:40:19,496 - src.gui.main_window - INFO - A视频播放位置: 第128帧
2025-06-29 10:40:19,530 - src.gui.main_window - INFO - B视频播放位置: 第65帧
2025-06-29 10:40:19,568 - src.gui.main_window - INFO - A视频播放位置: 第129帧
2025-06-29 10:40:19,667 - src.gui.main_window - INFO - B视频播放位置: 第66帧
2025-06-29 10:40:19,678 - src.gui.main_window - INFO - A视频播放位置: 第130帧
2025-06-29 10:40:19,752 - src.gui.main_window - INFO - A视频播放位置: 第131帧
2025-06-29 10:40:19,804 - src.gui.main_window - INFO - B视频播放位置: 第67帧
2025-06-29 10:40:19,824 - src.gui.main_window - INFO - A视频播放位置: 第132帧
2025-06-29 10:40:19,914 - src.gui.main_window - INFO - A视频播放位置: 第133帧
2025-06-29 10:40:19,935 - src.gui.main_window - INFO - B视频播放位置: 第68帧
2025-06-29 10:40:19,990 - src.gui.main_window - INFO - A视频播放位置: 第134帧
2025-06-29 10:40:20,064 - src.gui.main_window - INFO - A视频播放位置: 第135帧
2025-06-29 10:40:20,085 - src.gui.main_window - INFO - B视频播放位置: 第69帧
2025-06-29 10:40:20,145 - src.gui.main_window - INFO - A视频播放位置: 第136帧
2025-06-29 10:40:20,222 - src.gui.main_window - INFO - B视频播放位置: 第70帧
2025-06-29 10:40:20,235 - src.gui.main_window - INFO - A视频播放位置: 第137帧
2025-06-29 10:40:20,353 - src.gui.main_window - INFO - A视频播放位置: 第138帧
2025-06-29 10:40:20,367 - src.gui.main_window - INFO - B视频播放位置: 第71帧
2025-06-29 10:40:20,444 - src.gui.main_window - INFO - A视频播放位置: 第139帧
2025-06-29 10:40:20,501 - src.gui.main_window - INFO - B视频播放位置: 第72帧
2025-06-29 10:40:20,521 - src.gui.main_window - INFO - A视频播放位置: 第140帧
2025-06-29 10:40:20,627 - src.gui.main_window - INFO - A视频播放位置: 第141帧
2025-06-29 10:40:20,652 - src.gui.main_window - INFO - B视频播放位置: 第73帧
2025-06-29 10:40:20,695 - src.gui.main_window - INFO - A视频播放位置: 第142帧
2025-06-29 10:40:20,792 - src.gui.main_window - INFO - A视频播放位置: 第143帧
2025-06-29 10:40:20,803 - src.gui.main_window - INFO - B视频播放位置: 第74帧
2025-06-29 10:40:20,883 - src.gui.main_window - INFO - A视频播放位置: 第144帧
2025-06-29 10:40:20,931 - src.gui.main_window - INFO - B视频播放位置: 第75帧
2025-06-29 10:40:20,961 - src.gui.main_window - INFO - A视频播放位置: 第145帧
2025-06-29 10:40:21,059 - src.gui.main_window - INFO - A视频播放位置: 第146帧
2025-06-29 10:40:21,071 - src.gui.main_window - INFO - B视频播放位置: 第76帧
2025-06-29 10:40:21,159 - src.gui.main_window - INFO - A视频播放位置: 第147帧
2025-06-29 10:40:21,210 - src.gui.main_window - INFO - B视频播放位置: 第77帧
2025-06-29 10:40:21,237 - src.gui.main_window - INFO - A视频播放位置: 第148帧
2025-06-29 10:40:21,358 - src.gui.main_window - INFO - A视频播放位置: 第149帧
2025-06-29 10:40:21,370 - src.gui.main_window - INFO - B视频播放位置: 第78帧
2025-06-29 10:40:21,463 - src.gui.main_window - INFO - A视频播放位置: 第150帧
2025-06-29 10:40:21,506 - src.gui.main_window - INFO - B视频播放位置: 第79帧
2025-06-29 10:40:21,545 - src.gui.main_window - INFO - A视频播放位置: 第151帧
2025-06-29 10:40:21,651 - src.gui.main_window - INFO - A视频播放位置: 第152帧
2025-06-29 10:40:21,667 - src.gui.main_window - INFO - B视频播放位置: 第80帧
2025-06-29 10:40:21,732 - src.gui.main_window - INFO - A视频播放位置: 第153帧
2025-06-29 10:40:21,826 - src.gui.main_window - INFO - B视频播放位置: 第81帧
2025-06-29 10:40:21,836 - src.gui.main_window - INFO - A视频播放位置: 第154帧
2025-06-29 10:40:21,940 - src.gui.main_window - INFO - A视频播放位置: 第155帧
2025-06-29 10:40:21,971 - src.gui.main_window - INFO - B视频播放位置: 第82帧
2025-06-29 10:40:22,025 - src.gui.main_window - INFO - A视频播放位置: 第156帧
2025-06-29 10:40:22,122 - src.gui.main_window - INFO - B视频播放位置: 第83帧
2025-06-29 10:40:22,124 - src.gui.main_window - INFO - A视频播放位置: 第157帧
2025-06-29 10:40:22,208 - src.gui.main_window - INFO - A视频播放位置: 第158帧
2025-06-29 10:40:22,339 - src.gui.main_window - INFO - B视频播放位置: 第84帧
2025-06-29 10:40:22,340 - src.gui.main_window - INFO - A视频播放位置: 第159帧
2025-06-29 10:40:22,430 - src.gui.main_window - INFO - A视频播放位置: 第160帧
2025-06-29 10:40:22,475 - src.gui.main_window - INFO - B视频播放位置: 第85帧
2025-06-29 10:40:22,488 - src.gui.main_window - INFO - A视频播放位置: 第161帧
2025-06-29 10:40:22,569 - src.gui.main_window - INFO - A视频播放位置: 第162帧
2025-06-29 10:40:22,624 - src.gui.main_window - INFO - A视频播放位置: 第163帧
2025-06-29 10:40:22,640 - src.gui.main_window - INFO - B视频播放位置: 第86帧
2025-06-29 10:40:22,680 - src.gui.main_window - INFO - A视频播放位置: 第164帧
2025-06-29 10:40:22,738 - src.gui.main_window - INFO - A视频播放位置: 第165帧
2025-06-29 10:40:22,791 - src.gui.main_window - INFO - B视频播放位置: 第87帧
2025-06-29 10:40:22,802 - src.gui.main_window - INFO - A视频播放位置: 第166帧
2025-06-29 10:40:22,854 - src.gui.main_window - INFO - A视频播放位置: 第167帧
2025-06-29 10:40:22,923 - src.gui.main_window - INFO - A视频播放位置: 第168帧
2025-06-29 10:40:22,950 - src.gui.main_window - INFO - B视频播放位置: 第88帧
2025-06-29 10:40:22,982 - src.gui.main_window - INFO - A视频播放位置: 第169帧
2025-06-29 10:40:23,051 - src.gui.main_window - INFO - A视频播放位置: 第170帧
2025-06-29 10:40:23,098 - src.gui.main_window - INFO - B视频播放位置: 第89帧
2025-06-29 10:40:23,111 - src.gui.main_window - INFO - A视频播放位置: 第171帧
2025-06-29 10:40:23,186 - src.gui.main_window - INFO - A视频播放位置: 第172帧
2025-06-29 10:40:23,252 - src.gui.main_window - INFO - A视频播放位置: 第173帧
2025-06-29 10:40:23,265 - src.gui.main_window - INFO - B视频播放位置: 第90帧
2025-06-29 10:40:23,343 - src.gui.main_window - INFO - A视频播放位置: 第174帧
2025-06-29 10:40:23,413 - src.gui.main_window - INFO - A视频播放位置: 第175帧
2025-06-29 10:40:23,427 - src.gui.main_window - INFO - B视频播放位置: 第91帧
2025-06-29 10:40:23,482 - src.gui.main_window - INFO - A视频播放位置: 第176帧
2025-06-29 10:40:23,565 - src.gui.main_window - INFO - A视频播放位置: 第177帧
2025-06-29 10:40:23,586 - src.gui.main_window - INFO - B视频播放位置: 第92帧
2025-06-29 10:40:23,643 - src.gui.main_window - INFO - A视频播放位置: 第178帧
2025-06-29 10:40:23,706 - src.gui.main_window - INFO - A视频播放位置: 第179帧
2025-06-29 10:40:23,751 - src.gui.main_window - INFO - B视频播放位置: 第93帧
2025-06-29 10:40:23,767 - src.gui.main_window - INFO - A视频播放位置: 第180帧
2025-06-29 10:40:23,844 - src.gui.main_window - INFO - A视频播放位置: 第181帧
2025-06-29 10:40:23,911 - src.gui.main_window - INFO - B视频播放位置: 第94帧
2025-06-29 10:40:23,923 - src.gui.main_window - INFO - A视频播放位置: 第182帧
2025-06-29 10:40:23,994 - src.gui.main_window - INFO - A视频播放位置: 第183帧
2025-06-29 10:40:24,058 - src.gui.main_window - INFO - A视频播放位置: 第184帧
2025-06-29 10:40:24,079 - src.gui.main_window - INFO - B视频播放位置: 第95帧
2025-06-29 10:40:24,118 - src.gui.main_window - INFO - A视频播放位置: 第185帧
2025-06-29 10:40:24,200 - src.gui.main_window - INFO - A视频播放位置: 第186帧
2025-06-29 10:40:24,235 - src.gui.main_window - INFO - B视频播放位置: 第96帧
2025-06-29 10:40:24,263 - src.gui.main_window - INFO - A视频播放位置: 第187帧
2025-06-29 10:40:24,365 - src.gui.main_window - INFO - A视频播放位置: 第188帧
2025-06-29 10:40:24,398 - src.gui.main_window - INFO - B视频播放位置: 第97帧
2025-06-29 10:40:24,425 - src.gui.main_window - INFO - A视频播放位置: 第189帧
2025-06-29 10:40:24,492 - src.gui.main_window - INFO - A视频播放位置: 第190帧
2025-06-29 10:40:24,557 - src.gui.main_window - INFO - A视频播放位置: 第191帧
2025-06-29 10:40:24,580 - src.gui.main_window - INFO - B视频播放位置: 第98帧
2025-06-29 10:40:24,624 - src.gui.main_window - INFO - A视频播放位置: 第192帧
2025-06-29 10:40:24,691 - src.gui.main_window - INFO - A视频播放位置: 第193帧
2025-06-29 10:40:24,765 - src.gui.main_window - INFO - B视频播放位置: 第99帧
2025-06-29 10:40:24,774 - src.gui.main_window - INFO - A视频播放位置: 第194帧
2025-06-29 10:40:24,850 - src.gui.main_window - INFO - A视频播放位置: 第195帧
2025-06-29 10:40:24,932 - src.gui.main_window - INFO - B视频播放位置: 第100帧
2025-06-29 10:40:24,942 - src.gui.main_window - INFO - A视频播放位置: 第196帧
2025-06-29 10:40:25,008 - src.gui.main_window - INFO - A视频播放位置: 第197帧
2025-06-29 10:40:25,076 - src.gui.main_window - INFO - A视频播放位置: 第198帧
2025-06-29 10:40:25,105 - src.gui.main_window - INFO - B视频播放位置: 第101帧
2025-06-29 10:40:25,139 - src.gui.main_window - INFO - A视频播放位置: 第199帧
2025-06-29 10:40:25,229 - src.gui.main_window - INFO - A视频播放位置: 第200帧
2025-06-29 10:40:25,265 - src.gui.main_window - INFO - B视频播放位置: 第102帧
2025-06-29 10:40:25,350 - src.gui.main_window - INFO - A视频播放位置: 第201帧
2025-06-29 10:40:25,413 - src.gui.main_window - INFO - A视频播放位置: 第202帧
2025-06-29 10:40:25,436 - src.gui.main_window - INFO - B视频播放位置: 第103帧
2025-06-29 10:40:25,487 - src.gui.main_window - INFO - A视频播放位置: 第203帧
2025-06-29 10:40:25,557 - src.gui.main_window - INFO - A视频播放位置: 第204帧
2025-06-29 10:40:25,632 - src.gui.main_window - INFO - A视频播放位置: 第205帧
2025-06-29 10:40:25,645 - src.gui.main_window - INFO - B视频播放位置: 第104帧
2025-06-29 10:40:25,703 - src.gui.main_window - INFO - A视频播放位置: 第206帧
2025-06-29 10:40:25,794 - src.gui.main_window - INFO - A视频播放位置: 第207帧
2025-06-29 10:40:25,825 - src.gui.main_window - INFO - B视频播放位置: 第105帧
2025-06-29 10:40:25,868 - src.gui.main_window - INFO - A视频播放位置: 第208帧
2025-06-29 10:40:25,964 - src.gui.main_window - INFO - A视频播放位置: 第209帧
2025-06-29 10:40:25,993 - src.gui.main_window - INFO - B视频播放位置: 第106帧
2025-06-29 10:40:26,038 - src.gui.main_window - INFO - A视频播放位置: 第210帧
2025-06-29 10:40:26,112 - src.gui.main_window - INFO - A视频播放位置: 第211帧
2025-06-29 10:40:26,178 - src.gui.main_window - INFO - B视频播放位置: 第107帧
2025-06-29 10:40:26,190 - src.gui.main_window - INFO - A视频播放位置: 第212帧
2025-06-29 10:40:26,260 - src.gui.main_window - INFO - A视频播放位置: 第213帧
2025-06-29 10:40:26,359 - src.gui.main_window - INFO - A视频播放位置: 第214帧
2025-06-29 10:40:26,380 - src.gui.main_window - INFO - B视频播放位置: 第108帧
2025-06-29 10:40:26,429 - src.gui.main_window - INFO - A视频播放位置: 第215帧
2025-06-29 10:40:26,516 - src.gui.main_window - INFO - A视频播放位置: 第216帧
2025-06-29 10:40:26,578 - src.gui.main_window - INFO - B视频播放位置: 第109帧
2025-06-29 10:40:26,595 - src.gui.main_window - INFO - A视频播放位置: 第217帧
2025-06-29 10:40:26,703 - src.gui.main_window - INFO - A视频播放位置: 第218帧
2025-06-29 10:40:26,763 - src.gui.main_window - INFO - B视频播放位置: 第110帧
2025-06-29 10:40:26,787 - src.gui.main_window - INFO - A视频播放位置: 第219帧
2025-06-29 10:40:26,880 - src.gui.main_window - INFO - A视频播放位置: 第220帧
2025-06-29 10:40:26,935 - src.gui.main_window - INFO - B视频播放位置: 第111帧
2025-06-29 10:40:26,961 - src.gui.main_window - INFO - A视频播放位置: 第221帧
2025-06-29 10:40:27,068 - src.gui.main_window - INFO - A视频播放位置: 第222帧
2025-06-29 10:40:27,118 - src.gui.main_window - INFO - B视频播放位置: 第112帧
2025-06-29 10:40:27,152 - src.gui.main_window - INFO - A视频播放位置: 第223帧
2025-06-29 10:40:27,255 - src.gui.main_window - INFO - A视频播放位置: 第224帧
2025-06-29 10:40:27,355 - src.gui.main_window - INFO - B视频播放位置: 第113帧
2025-06-29 10:40:27,356 - src.gui.main_window - INFO - A视频播放位置: 第225帧
2025-06-29 10:40:27,459 - src.gui.main_window - INFO - A视频播放位置: 第226帧
2025-06-29 10:40:27,520 - src.gui.main_window - INFO - B视频播放位置: 第114帧
2025-06-29 10:40:27,548 - src.gui.main_window - INFO - A视频播放位置: 第227帧
2025-06-29 10:40:27,648 - src.gui.main_window - INFO - A视频播放位置: 第228帧
2025-06-29 10:40:27,727 - src.gui.main_window - INFO - A视频播放位置: 第229帧
2025-06-29 10:40:27,742 - src.gui.main_window - INFO - B视频播放位置: 第115帧
2025-06-29 10:40:27,837 - src.gui.main_window - INFO - A视频播放位置: 第230帧
2025-06-29 10:40:27,939 - src.gui.main_window - INFO - B视频播放位置: 第116帧
2025-06-29 10:40:27,950 - src.gui.main_window - INFO - A视频播放位置: 第231帧
2025-06-29 10:40:28,032 - src.gui.main_window - INFO - A视频播放位置: 第232帧
2025-06-29 10:40:28,120 - src.gui.main_window - INFO - A视频播放位置: 第233帧
2025-06-29 10:40:28,158 - src.gui.main_window - INFO - B视频播放位置: 第117帧
2025-06-29 10:40:28,177 - src.gui.main_window - INFO - A视频播放位置: 第234帧
2025-06-29 10:40:28,232 - src.gui.main_window - INFO - A视频播放位置: 第235帧
2025-06-29 10:40:28,357 - src.gui.main_window - INFO - A视频播放位置: 第236帧
2025-06-29 10:40:28,371 - src.gui.main_window - INFO - B视频播放位置: 第118帧
2025-06-29 10:40:28,380 - src.gui.main_window - INFO - A视频播放位置: 第237帧
2025-06-29 10:40:28,447 - src.gui.main_window - INFO - A视频播放位置: 第238帧
2025-06-29 10:40:28,504 - src.gui.main_window - INFO - A视频播放位置: 第239帧
2025-06-29 10:40:28,552 - src.gui.main_window - INFO - B视频播放位置: 第119帧
2025-06-29 10:40:28,565 - src.gui.main_window - INFO - A视频播放位置: 第240帧
2025-06-29 10:40:28,627 - src.gui.main_window - INFO - A视频播放位置: 第241帧
2025-06-29 10:40:28,688 - src.gui.main_window - INFO - A视频播放位置: 第242帧
2025-06-29 10:40:28,747 - src.gui.main_window - INFO - A视频播放位置: 第243帧
2025-06-29 10:40:28,759 - src.gui.main_window - INFO - B视频播放位置: 第120帧
2025-06-29 10:40:28,805 - src.gui.main_window - INFO - A视频播放位置: 第244帧
2025-06-29 10:40:28,865 - src.gui.main_window - INFO - A视频播放位置: 第245帧
2025-06-29 10:40:28,925 - src.gui.main_window - INFO - A视频播放位置: 第246帧
2025-06-29 10:40:28,949 - src.gui.main_window - INFO - B视频播放位置: 第121帧
2025-06-29 10:40:28,986 - src.gui.main_window - INFO - A视频播放位置: 第247帧
2025-06-29 10:40:29,045 - src.gui.main_window - INFO - A视频播放位置: 第248帧
2025-06-29 10:40:29,106 - src.gui.main_window - INFO - A视频播放位置: 第249帧
2025-06-29 10:40:29,138 - src.gui.main_window - INFO - B视频播放位置: 第122帧
2025-06-29 10:40:29,167 - src.gui.main_window - INFO - A视频播放位置: 第250帧
2025-06-29 10:40:29,240 - src.gui.main_window - INFO - A视频播放位置: 第251帧
2025-06-29 10:40:29,354 - src.gui.main_window - INFO - A视频播放位置: 第252帧
2025-06-29 10:40:29,359 - src.gui.main_window - INFO - B视频播放位置: 第123帧
2025-06-29 10:40:29,411 - src.gui.main_window - INFO - A视频播放位置: 第253帧
2025-06-29 10:40:29,484 - src.gui.main_window - INFO - A视频播放位置: 第254帧
2025-06-29 10:40:29,531 - src.gui.main_window - INFO - B视频播放位置: 第124帧
2025-06-29 10:40:29,550 - src.gui.main_window - INFO - A视频播放位置: 第255帧
2025-06-29 10:40:29,615 - src.gui.main_window - INFO - A视频播放位置: 第256帧
2025-06-29 10:40:29,678 - src.gui.main_window - INFO - A视频播放位置: 第257帧
2025-06-29 10:40:29,743 - src.gui.main_window - INFO - A视频播放位置: 第258帧
2025-06-29 10:40:29,793 - src.gui.main_window - INFO - B视频播放位置: 第125帧
2025-06-29 10:40:29,809 - src.gui.main_window - INFO - A视频播放位置: 第259帧
2025-06-29 10:40:29,874 - src.gui.main_window - INFO - A视频播放位置: 第260帧
2025-06-29 10:40:29,939 - src.gui.main_window - INFO - A视频播放位置: 第261帧
2025-06-29 10:40:30,002 - src.gui.main_window - INFO - A视频播放位置: 第262帧
2025-06-29 10:40:30,014 - src.gui.main_window - INFO - B视频播放位置: 第126帧
2025-06-29 10:40:30,071 - src.gui.main_window - INFO - A视频播放位置: 第263帧
2025-06-29 10:40:30,152 - src.gui.main_window - INFO - A视频播放位置: 第264帧
2025-06-29 10:40:30,213 - src.gui.main_window - INFO - B视频播放位置: 第127帧
2025-06-29 10:40:30,224 - src.gui.main_window - INFO - A视频播放位置: 第265帧
2025-06-29 10:40:30,373 - src.gui.main_window - INFO - A视频播放位置: 第266帧
2025-06-29 10:40:30,419 - src.gui.main_window - INFO - B视频播放位置: 第128帧
2025-06-29 10:40:30,428 - src.gui.main_window - INFO - A视频播放位置: 第267帧
2025-06-29 10:40:30,500 - src.gui.main_window - INFO - A视频播放位置: 第268帧
2025-06-29 10:40:30,577 - src.gui.main_window - INFO - A视频播放位置: 第269帧
2025-06-29 10:40:30,648 - src.gui.main_window - INFO - B视频播放位置: 第129帧
2025-06-29 10:40:30,658 - src.gui.main_window - INFO - A视频播放位置: 第270帧
2025-06-29 10:40:30,738 - src.gui.main_window - INFO - A视频播放位置: 第271帧
2025-06-29 10:40:30,825 - src.gui.main_window - INFO - A视频播放位置: 第272帧
2025-06-29 10:40:30,849 - src.gui.main_window - INFO - B视频播放位置: 第130帧
2025-06-29 10:40:30,897 - src.gui.main_window - INFO - A视频播放位置: 第273帧
2025-06-29 10:40:30,980 - src.gui.main_window - INFO - A视频播放位置: 第274帧
2025-06-29 10:40:31,048 - src.gui.main_window - INFO - B视频播放位置: 第131帧
2025-06-29 10:40:31,059 - src.gui.main_window - INFO - A视频播放位置: 第275帧
2025-06-29 10:40:31,059 - src.gui.video_player - INFO - 播放状态变更: 暂停
2025-06-29 10:40:31,067 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-29 10:40:31,068 - src.gui.video_player - INFO - 视频播放完成
2025-06-29 10:40:31,232 - src.gui.main_window - INFO - B视频播放位置: 第132帧
2025-06-29 10:40:31,418 - src.gui.main_window - INFO - B视频播放位置: 第133帧
2025-06-29 10:40:31,610 - src.gui.main_window - INFO - B视频播放位置: 第134帧
2025-06-29 10:40:31,802 - src.gui.main_window - INFO - B视频播放位置: 第135帧
2025-06-29 10:40:31,988 - src.gui.main_window - INFO - B视频播放位置: 第136帧
2025-06-29 10:40:32,173 - src.gui.main_window - INFO - B视频播放位置: 第137帧
2025-06-29 10:40:32,369 - src.gui.main_window - INFO - B视频播放位置: 第138帧
2025-06-29 10:40:32,559 - src.gui.main_window - INFO - B视频播放位置: 第139帧
2025-06-29 10:40:32,754 - src.gui.main_window - INFO - B视频播放位置: 第140帧
2025-06-29 10:40:32,944 - src.gui.main_window - INFO - B视频播放位置: 第141帧
2025-06-29 10:40:33,147 - src.gui.main_window - INFO - B视频播放位置: 第142帧
2025-06-29 10:40:33,370 - src.gui.main_window - INFO - B视频播放位置: 第143帧
2025-06-29 10:40:33,575 - src.gui.main_window - INFO - B视频播放位置: 第144帧
2025-06-29 10:40:33,781 - src.gui.main_window - INFO - B视频播放位置: 第145帧
2025-06-29 10:40:33,978 - src.gui.main_window - INFO - B视频播放位置: 第146帧
2025-06-29 10:40:34,072 - src.gui.video_player - INFO - 播放状态变更: 暂停
2025-06-29 10:40:34,230 - src.gui.main_window - INFO - B视频播放状态: 暂停
2025-06-29 10:40:34,234 - src.gui.main_window - INFO - B视频播放位置: 第147帧
2025-06-29 10:40:45,695 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 10:47:03,430 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 10:47:44,261 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 10:47:44,261 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 10:47:44,261 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 10:47:44,261 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 10:47:44,261 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 10:47:44,262 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 10:47:53,114 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 10:47:54,873 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 10:47:54,874 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 10:47:54,874 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 10:47:54,874 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 10:47:54,874 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 10:47:54,874 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 10:47:57,568 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 10:48:06,258 - src.gui.video_player - INFO - 播放状态变更: 播放
2025-06-29 10:48:06,258 - src.gui.main_window - INFO - A视频播放状态: 播放
2025-06-29 10:48:06,266 - src.gui.main_window - INFO - A视频播放位置: 第0帧
2025-06-29 10:48:06,349 - src.gui.main_window - INFO - A视频播放位置: 第1帧
2025-06-29 10:48:06,400 - src.gui.main_window - INFO - A视频播放位置: 第2帧
2025-06-29 10:48:06,452 - src.gui.main_window - INFO - A视频播放位置: 第3帧
2025-06-29 10:48:06,507 - src.gui.main_window - INFO - A视频播放位置: 第4帧
2025-06-29 10:48:06,559 - src.gui.main_window - INFO - A视频播放位置: 第5帧
2025-06-29 10:48:06,610 - src.gui.main_window - INFO - A视频播放位置: 第6帧
2025-06-29 10:48:06,666 - src.gui.main_window - INFO - A视频播放位置: 第7帧
2025-06-29 10:48:06,720 - src.gui.main_window - INFO - A视频播放位置: 第8帧
2025-06-29 10:48:06,776 - src.gui.main_window - INFO - A视频播放位置: 第9帧
2025-06-29 10:48:06,831 - src.gui.main_window - INFO - A视频播放位置: 第10帧
2025-06-29 10:48:06,886 - src.gui.main_window - INFO - A视频播放位置: 第11帧
2025-06-29 10:48:06,943 - src.gui.main_window - INFO - A视频播放位置: 第12帧
2025-06-29 10:48:07,002 - src.gui.main_window - INFO - A视频播放位置: 第13帧
2025-06-29 10:48:07,055 - src.gui.main_window - INFO - A视频播放位置: 第14帧
2025-06-29 10:48:07,065 - src.gui.video_player - INFO - 播放状态变更: 暂停
2025-06-29 10:48:07,065 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-29 10:48:29,196 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 10:51:51,209 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpyjmpxpku.mp4
2025-06-29 10:51:51,209 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmpyjmpxpku.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 90
  时长: 3.00秒
  编码: FMP4
  大小: 0.44 MB
2025-06-29 10:52:12,012 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:52:12,012 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:52:12,013 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:52:12,013 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:52:12,013 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:52:12,013 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:52:12,013 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:52:12,014 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:52:12,014 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 10:52:12,014 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:52:12,014 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:52:12,014 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:52:12,014 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:52:12,014 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:52:12,014 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:52:12,014 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:52:12,014 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:52:12,015 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 10:52:12,015 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:52:12,015 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:52:12,015 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:52:12,015 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:52:12,015 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:52:12,015 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:52:12,015 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:52:12,015 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:52:12,015 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 10:52:12,016 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 10:52:12,016 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 10:52:12,016 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 10:52:12,016 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 10:52:12,016 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 10:52:12,390 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 10:52:12,390 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 10:52:12,390 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 10:52:12,390 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 10:52:12,414 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 10:52:12,415 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 10:52:12,415 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 10:52:23,198 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-29 10:52:23,198 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-29 10:52:23,216 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 10:52:23,216 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 10:52:23,239 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 10:52:28,173 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-29 10:52:28,173 - src.gui.main_window - INFO - B视频播放状态: 暂停
2025-06-29 10:52:28,196 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 10:52:28,196 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 10:52:28,255 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 10:52:42,369 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 10:53:22,361 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpemqjmdfj.mp4
2025-06-29 10:53:22,361 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmpemqjmdfj.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 90
  时长: 3.00秒
  编码: FMP4
  大小: 0.44 MB
2025-06-29 10:53:23,243 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:23,244 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:23,244 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:23,244 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:23,244 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:53:23,244 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:53:23,245 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:53:23,245 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:53:23,245 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 10:53:23,245 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:23,245 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:23,245 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:23,245 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:23,246 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:53:23,246 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:53:23,246 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:53:23,246 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:53:23,246 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 10:53:23,246 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:23,246 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:23,246 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:23,246 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:23,246 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:53:23,247 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:53:23,247 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:53:23,247 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:53:23,247 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 10:53:23,247 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 10:53:23,247 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 10:53:23,248 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 10:53:23,248 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 10:53:23,248 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 10:53:23,249 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpsr_c8zry.mp4
2025-06-29 10:53:23,249 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmpsr_c8zry.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 60
  时长: 2.00秒
  编码: FMP4
  大小: 0.30 MB
2025-06-29 10:53:23,249 - src.fusion.fusion_engine - INFO - A视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpsr_c8zry.mp4
2025-06-29 10:53:23,250 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpdnjmu5nl.mp4
2025-06-29 10:53:23,251 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmpdnjmu5nl.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 90
  时长: 3.00秒
  编码: FMP4
  大小: 0.44 MB
2025-06-29 10:53:23,251 - src.fusion.fusion_engine - INFO - B视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpdnjmu5nl.mp4
2025-06-29 10:53:24,052 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:24,052 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:24,052 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:24,052 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:24,052 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:53:24,052 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:53:24,052 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:53:24,052 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:53:24,052 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 10:53:24,052 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:24,052 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:24,053 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:24,053 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:24,053 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:53:24,053 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:53:24,053 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:53:24,053 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:53:24,053 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 10:53:24,053 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:24,053 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:24,053 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:53:24,053 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:53:24,053 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:53:24,053 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:53:24,053 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:53:24,053 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:53:24,054 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 10:53:24,054 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 10:53:24,054 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 10:53:24,054 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 10:53:24,054 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 10:53:24,054 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 10:53:24,055 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmppvrmiycr.mp4
2025-06-29 10:53:24,055 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmppvrmiycr.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 60
  时长: 2.00秒
  编码: FMP4
  大小: 0.30 MB
2025-06-29 10:53:24,056 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmppvrmiycr.mp4
2025-06-29 10:53:24,056 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmppvrmiycr.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 60
  时长: 2.00秒
  编码: FMP4
  大小: 0.30 MB
2025-06-29 10:53:24,057 - src.fusion.fusion_engine - INFO - A视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmppvrmiycr.mp4
2025-06-29 10:53:24,058 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpwt55mz5j.mp4
2025-06-29 10:53:24,058 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmpwt55mz5j.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 90
  时长: 3.00秒
  编码: FMP4
  大小: 0.44 MB
2025-06-29 10:53:24,059 - src.video.video_loader - INFO - 视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpwt55mz5j.mp4
2025-06-29 10:53:24,059 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: tmpwt55mz5j.mp4
  分辨率: 640x480
  帧率: 30.00 FPS
  帧数: 90
  时长: 3.00秒
  编码: FMP4
  大小: 0.44 MB
2025-06-29 10:53:24,059 - src.fusion.fusion_engine - INFO - B视频加载成功: /var/folders/52/p23zsc257w1g4__7pq4zpnt00000gn/T/tmpwt55mz5j.mp4
2025-06-29 10:54:23,261 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:54:23,262 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:54:23,262 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:54:23,262 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:54:23,262 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:54:23,262 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:54:23,263 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:54:23,263 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:54:23,263 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 10:54:23,263 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:54:23,263 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:54:23,263 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:54:23,263 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:54:23,263 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:54:23,264 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:54:23,264 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:54:23,264 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:54:23,264 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 10:54:23,264 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:54:23,264 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:54:23,264 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 10:54:23,264 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 10:54:23,264 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 10:54:23,265 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 10:54:23,265 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 10:54:23,265 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 10:54:23,265 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 10:54:23,265 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 10:54:23,265 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 10:54:23,266 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 10:54:23,266 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 10:54:23,266 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 10:54:23,631 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 10:54:23,632 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 10:54:23,632 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 10:54:23,632 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 10:54:23,652 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 10:54:23,653 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 10:54:23,653 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 10:54:33,472 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-29 10:54:33,472 - src.gui.main_window - INFO - A视频播放状态: 暂停
2025-06-29 10:54:33,490 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 10:54:33,490 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 10:54:33,515 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 10:54:38,366 - src.gui.video_player - WARNING - 帧号超出范围: 0
2025-06-29 10:54:38,367 - src.gui.main_window - INFO - B视频播放状态: 暂停
2025-06-29 10:54:38,389 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 10:54:38,389 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 10:54:38,449 - src.gui.video_player - INFO - 视频播放器加载视频成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 10:54:42,911 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 10:54:48,542 - src.gui.main_window - INFO - 应用程序退出
