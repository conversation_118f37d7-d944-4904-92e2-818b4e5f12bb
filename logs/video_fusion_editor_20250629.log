2025-06-29 00:01:27,740 - __main__ - INFO - 开始测试空间位置控制算法
2025-06-29 00:01:27,740 - __main__ - INFO - === 测试空间位置控制参数创建 ===
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 动态位置参数创建成功
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 自定义路径参数创建成功
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:01:27,740 - __main__ - INFO - === 测试空间位置计算 ===
2025-06-29 00:01:27,740 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:01:27,740 - __main__ - INFO - ✅ 静态位置计算正确: (300, 560)
2025-06-29 00:01:27,741 - __main__ - INFO - ✅ 水平滚动位置计算正确: [(0, 400), (250, 400), (500, 400), (750, 400), (800, 400)]
2025-06-29 00:01:27,741 - __main__ - INFO - ✅ 圆形轨迹位置计算正确: [(750, 400), (500, 600), (250, 400), (499, 200)]
2025-06-29 00:01:27,741 - __main__ - INFO - === 测试叠加融合空间位置控制 ===
2025-06-29 00:01:27,752 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:01:27,752 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:01:27,766 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:01:27,766 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 00:01:27,766 - __main__ - INFO - 测试: 静态位置
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.6
2025-06-29 00:01:27,766 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=True, 运动轨迹=static, 运动速度=1.0
2025-06-29 00:01:32,278 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 00:01:32,717 - __main__ - ERROR - 叠加融合空间位置控制测试失败: 
2025-06-29 00:01:33,443 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_position_control 失败
2025-06-29 00:01:33,444 - __main__ - INFO - === 测试混合融合空间位置控制 ===
2025-06-29 00:01:33,471 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:01:33,472 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:01:33,496 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:01:33,496 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.5
2025-06-29 00:01:33,497 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=vertical_scroll, 运动速度=1.0
2025-06-29 00:01:37,786 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-29 00:01:38,208 - __main__ - ERROR - 混合融合空间位置控制测试失败: 
2025-06-29 00:01:38,938 - __main__ - ERROR - 测试 test_blend_fusion_spatial_position_control 失败
2025-06-29 00:01:38,938 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-29 00:01:38,938 - __main__ - ERROR - ❌ 部分测试失败
2025-06-29 00:02:31,021 - __main__ - INFO - 开始测试空间位置控制算法
2025-06-29 00:02:31,021 - __main__ - INFO - === 测试空间位置控制参数创建 ===
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 动态位置参数创建成功
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 自定义路径参数创建成功
2025-06-29 00:02:31,021 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:02:31,021 - __main__ - INFO - === 测试空间位置计算 ===
2025-06-29 00:02:31,022 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:02:31,022 - __main__ - INFO - ✅ 静态位置计算正确: (300, 560)
2025-06-29 00:02:31,022 - __main__ - INFO - ✅ 水平滚动位置计算正确: [(0, 400), (250, 400), (500, 400), (750, 400), (800, 400)]
2025-06-29 00:02:31,022 - __main__ - INFO - ✅ 圆形轨迹位置计算正确: [(750, 400), (500, 600), (250, 400), (499, 200)]
2025-06-29 00:02:31,022 - __main__ - INFO - === 测试叠加融合空间位置控制 ===
2025-06-29 00:02:31,032 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:02:31,032 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:02:31,047 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:02:31,047 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 00:02:31,047 - __main__ - INFO - 测试: 静态位置
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.6
2025-06-29 00:02:31,047 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=True, 运动轨迹=static, 运动速度=1.0
2025-06-29 00:02:35,624 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 00:02:36,093 - __main__ - ERROR - 预期5帧，实际得到1202帧
2025-06-29 00:02:36,789 - __main__ - ERROR - 测试 test_overlay_fusion_spatial_position_control 失败
2025-06-29 00:02:36,789 - __main__ - INFO - === 测试混合融合空间位置控制 ===
2025-06-29 00:02:36,819 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:02:36,820 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:02:36,833 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:02:36,833 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:02:36,833 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:02:36,833 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 00:02:36,834 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.5
2025-06-29 00:02:36,834 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=vertical_scroll, 运动速度=1.0
2025-06-29 00:02:41,362 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-29 00:02:41,796 - __main__ - ERROR - 预期5帧，实际得到1202帧
2025-06-29 00:02:42,502 - __main__ - ERROR - 测试 test_blend_fusion_spatial_position_control 失败
2025-06-29 00:02:42,502 - __main__ - INFO - 测试完成: 2/4 通过
2025-06-29 00:02:42,502 - __main__ - ERROR - ❌ 部分测试失败
2025-06-29 00:07:23,489 - __main__ - INFO - 开始测试图像处理控制功能
2025-06-29 00:07:23,489 - __main__ - INFO - === 测试图像处理控制参数创建 ===
2025-06-29 00:07:23,489 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:07:23,489 - __main__ - INFO - ✅ 自定义参数创建成功
2025-06-29 00:07:23,489 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:07:23,489 - __main__ - INFO - === 测试图像处理控制器 ===
2025-06-29 00:07:23,489 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,489 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,490 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,490 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,490 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,490 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,494 - __main__ - INFO - ✅ 不启用预处理测试通过
2025-06-29 00:07:23,509 - __main__ - INFO - ✅ 边缘检测测试通过
2025-06-29 00:07:23,511 - __main__ - INFO - ✅ Gamma校正测试通过
2025-06-29 00:07:23,517 - __main__ - INFO - ✅ 多种预处理方法组合测试通过
2025-06-29 00:07:23,517 - __main__ - INFO - === 测试融合方法 ===
2025-06-29 00:07:23,517 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,517 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,517 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,517 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,517 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,517 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 alpha_blend 测试通过
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 normal 测试通过
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 multiply 测试通过
2025-06-29 00:07:23,520 - __main__ - INFO - ✅ 融合方法 screen 测试通过
2025-06-29 00:07:23,521 - __main__ - INFO - ✅ 融合方法 overlay 测试通过
2025-06-29 00:07:23,522 - __main__ - INFO - ✅ 融合方法 feather 测试通过
2025-06-29 00:07:23,522 - __main__ - INFO - === 测试可用方法列表 ===
2025-06-29 00:07:23,522 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,522 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,523 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,523 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,523 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,523 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,523 - __main__ - INFO - ✅ 可用方法列表测试通过
2025-06-29 00:07:23,523 - __main__ - INFO -    预处理方法: ['edge_detection', 'histogram_equalization', 'histogram_matching', 'gamma_correction', 'gaussian_blur', 'sharpen', 'noise_reduction']
2025-06-29 00:07:23,523 - __main__ - INFO -    边缘检测方法: ['canny', 'sobel', 'laplacian', 'scharr']
2025-06-29 00:07:23,523 - __main__ - INFO -    融合方法: ['alpha_blend', 'normal', 'multiply', 'screen', 'overlay', 'feather']
2025-06-29 00:07:23,523 - __main__ - INFO - === 测试插入融合图像处理控制 ===
2025-06-29 00:07:23,532 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:07:23,533 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:07:23,546 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:07:23,547 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:07:23,547 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,547 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,547 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:07:23,547 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:07:23,547 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:07:23,547 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:07:23,547 - __main__ - INFO - 测试: 边缘检测
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:23,547 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['edge_detection']
2025-06-29 00:07:28,537 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:29,021 - __main__ - INFO - ✅ 边缘检测 测试通过，结果帧数: 1204
2025-06-29 00:07:29,021 - __main__ - INFO - 测试: 直方图均衡化
2025-06-29 00:07:29,021 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:29,021 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['histogram_equalization']
2025-06-29 00:07:33,816 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:34,490 - __main__ - INFO - ✅ 直方图均衡化 测试通过，结果帧数: 1204
2025-06-29 00:07:34,490 - __main__ - INFO - 测试: Gamma校正
2025-06-29 00:07:34,490 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:34,490 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['gamma_correction']
2025-06-29 00:07:38,618 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:39,266 - __main__ - INFO - ✅ Gamma校正 测试通过，结果帧数: 1204
2025-06-29 00:07:39,266 - __main__ - INFO - 测试: 组合处理
2025-06-29 00:07:39,266 - src.fusion.insertion_fusion - INFO - 开始图像处理控制插入，插入位置数量: 2
2025-06-29 00:07:39,266 - src.fusion.insertion_fusion - INFO - 图像处理控制参数: 启用预处理=True, 预处理方法=['gamma_correction', 'gaussian_blur']
2025-06-29 00:07:43,627 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:07:44,255 - __main__ - INFO - ✅ 组合处理 测试通过，结果帧数: 1204
2025-06-29 00:07:45,287 - __main__ - INFO - 测试完成: 5/5 通过
2025-06-29 00:07:45,287 - __main__ - INFO - 🎉 所有测试通过！图像处理控制功能工作正常
2025-06-29 00:12:02,675 - __main__ - INFO - 开始测试文字内容控制功能
2025-06-29 00:12:02,675 - __main__ - INFO - === 测试文字内容控制参数创建 ===
2025-06-29 00:12:02,675 - __main__ - INFO - ✅ 默认参数创建成功
2025-06-29 00:12:02,675 - __main__ - INFO - ✅ 自定义参数创建成功
2025-06-29 00:12:02,676 - __main__ - INFO - ✅ 参数序列化测试通过
2025-06-29 00:12:02,676 - __main__ - INFO - === 测试文字内容控制器 ===
2025-06-29 00:12:02,676 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,676 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,701 - __main__ - INFO - ✅ 不启用文字叠加测试通过
2025-06-29 00:12:02,701 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,701 - src.effects.text_content_controller - INFO - 文字内容: '静态文字', 位置模式: static
2025-06-29 00:12:02,712 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,712 - __main__ - INFO - ✅ 静态文字叠加测试通过
2025-06-29 00:12:02,713 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,713 - src.effects.text_content_controller - INFO - 文字内容: '动态文字', 位置模式: dynamic
2025-06-29 00:12:02,724 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,725 - __main__ - INFO - ✅ 动态文字叠加测试通过
2025-06-29 00:12:02,726 - __main__ - INFO - ✅ 单帧文字叠加测试通过
2025-06-29 00:12:02,727 - __main__ - INFO - === 测试文字样式和效果 ===
2025-06-29 00:12:02,727 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,728 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,739 - __main__ - INFO - ✅ 字体测试通过，测试了 5 种字体
2025-06-29 00:12:02,741 - __main__ - INFO - ✅ 描边效果测试通过
2025-06-29 00:12:02,742 - __main__ - INFO - ✅ 阴影效果测试通过
2025-06-29 00:12:02,747 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 20
2025-06-29 00:12:02,748 - src.effects.text_content_controller - INFO - 文字内容: '淡入淡出文字', 位置模式: static
2025-06-29 00:12:02,779 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 20 帧
2025-06-29 00:12:02,779 - __main__ - INFO - ✅ 淡入淡出效果测试通过
2025-06-29 00:12:02,783 - __main__ - INFO - === 测试运动轨迹 ===
2025-06-29 00:12:02,783 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,783 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,806 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,806 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: linear_random', 位置模式: dynamic
2025-06-29 00:12:02,815 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,815 - __main__ - INFO - ✅ 轨迹 linear_random 测试通过
2025-06-29 00:12:02,815 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,815 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: curve_random', 位置模式: dynamic
2025-06-29 00:12:02,827 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,828 - __main__ - INFO - ✅ 轨迹 curve_random 测试通过
2025-06-29 00:12:02,828 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,828 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: circular', 位置模式: dynamic
2025-06-29 00:12:02,835 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,836 - __main__ - INFO - ✅ 轨迹 circular 测试通过
2025-06-29 00:12:02,836 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,836 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: elliptical', 位置模式: dynamic
2025-06-29 00:12:02,845 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,846 - __main__ - INFO - ✅ 轨迹 elliptical 测试通过
2025-06-29 00:12:02,846 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,846 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: horizontal_scroll', 位置模式: dynamic
2025-06-29 00:12:02,856 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,856 - __main__ - INFO - ✅ 轨迹 horizontal_scroll 测试通过
2025-06-29 00:12:02,856 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,856 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: vertical_scroll', 位置模式: dynamic
2025-06-29 00:12:02,865 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,866 - __main__ - INFO - ✅ 轨迹 vertical_scroll 测试通过
2025-06-29 00:12:02,866 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,866 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: diagonal', 位置模式: dynamic
2025-06-29 00:12:02,876 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,876 - __main__ - INFO - ✅ 轨迹 diagonal 测试通过
2025-06-29 00:12:02,876 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 10
2025-06-29 00:12:02,876 - src.effects.text_content_controller - INFO - 文字内容: '轨迹: custom_path', 位置模式: dynamic
2025-06-29 00:12:02,886 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 10 帧
2025-06-29 00:12:02,887 - __main__ - INFO - ✅ 轨迹 custom_path 测试通过
2025-06-29 00:12:02,890 - __main__ - INFO - === 测试插入融合文字内容控制 ===
2025-06-29 00:12:02,900 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:12:02,900 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:12:02,913 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:12:02,914 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:12:02,915 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:12:02,915 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:12:02,916 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:12:02,916 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:12:02,916 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:12:02,916 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:12:02,916 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:12:02,916 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:12:02,916 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:12:02,916 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 00:12:02,916 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:12:02,917 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:12:02,917 - __main__ - INFO - 测试: 静态文字
2025-06-29 00:12:02,917 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 1
2025-06-29 00:12:02,917 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:12:08,573 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1203
2025-06-29 00:12:08,574 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1203
2025-06-29 00:12:08,574 - src.effects.text_content_controller - INFO - 文字内容: '静态测试文字', 位置模式: static
2025-06-29 00:12:10,726 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1203 帧
2025-06-29 00:12:10,954 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1203
2025-06-29 00:12:11,718 - __main__ - INFO - ✅ 静态文字 测试通过，结果帧数: 1203
2025-06-29 00:12:11,718 - __main__ - INFO - 测试: 滚动文字
2025-06-29 00:12:11,718 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 1
2025-06-29 00:12:11,718 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:12:16,035 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1203
2025-06-29 00:12:16,035 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1203
2025-06-29 00:12:16,035 - src.effects.text_content_controller - INFO - 文字内容: '滚动测试文字', 位置模式: dynamic
2025-06-29 00:12:18,194 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1203 帧
2025-06-29 00:12:18,241 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1203
2025-06-29 00:12:19,442 - __main__ - INFO - ✅ 滚动文字 测试通过，结果帧数: 1203
2025-06-29 00:12:19,442 - __main__ - INFO - 测试: 带效果文字
2025-06-29 00:12:19,442 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 1
2025-06-29 00:12:19,442 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:12:23,663 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1203
2025-06-29 00:12:23,663 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1203
2025-06-29 00:12:23,663 - src.effects.text_content_controller - INFO - 文字内容: '效果测试文字', 位置模式: static
2025-06-29 00:12:25,820 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1203 帧
2025-06-29 00:12:25,887 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1203
2025-06-29 00:12:27,025 - __main__ - INFO - ✅ 带效果文字 测试通过，结果帧数: 1203
2025-06-29 00:12:28,230 - __main__ - INFO - 测试完成: 5/5 通过
2025-06-29 00:12:28,231 - __main__ - INFO - 🎉 所有测试通过！文字内容控制功能工作正常
2025-06-29 00:14:20,534 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,534 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,534 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,534 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,534 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:14:20,534 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:14:20,535 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:14:20,535 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:14:20,535 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:14:20,535 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,535 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,535 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,535 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,535 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:14:20,535 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:14:20,535 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:14:20,535 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:14:20,536 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:14:20,536 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,536 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,536 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:14:20,536 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:14:20,536 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:14:20,536 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:14:20,536 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:14:20,536 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:14:20,536 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:14:20,536 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:14:20,536 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:14:20,537 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:14:20,537 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:14:20,537 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:14:20,862 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 00:14:20,862 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 00:14:20,863 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 00:14:20,863 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 00:14:20,877 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 00:14:20,878 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 00:14:20,878 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 00:23:52,684 - __main__ - INFO - 开始五维控制系统综合测试
2025-06-29 00:23:52,684 - __main__ - INFO - === 测试融合参数创建和序列化 ===
2025-06-29 00:23:52,684 - __main__ - ERROR - 融合参数创建和序列化测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:23:52,684 - __main__ - ERROR - 测试 test_fusion_params_creation_and_serialization 失败
2025-06-29 00:23:52,684 - __main__ - INFO - === 测试融合引擎GUI集成 ===
2025-06-29 00:23:52,685 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,685 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,686 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,686 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,686 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,686 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,686 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,686 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,686 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,686 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,687 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,687 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,687 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,687 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,687 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,687 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,687 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,688 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,688 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,688 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,688 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,688 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,688 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,688 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,689 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:23:52,689 - __main__ - ERROR - 融合引擎GUI集成测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:23:52,689 - __main__ - ERROR - 测试 test_fusion_engine_gui_integration 失败
2025-06-29 00:23:52,689 - __main__ - INFO - === 测试综合控制插入融合 ===
2025-06-29 00:23:52,689 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,689 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,689 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,689 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,689 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,689 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,689 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,689 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,689 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,690 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,690 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,690 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,690 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,690 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,691 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,691 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,691 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,691 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,691 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,691 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,691 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,691 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,691 - __main__ - ERROR - 综合控制插入融合测试失败: 'FusionEngine' object has no attribute 'load_videos'
2025-06-29 00:23:52,691 - __main__ - ERROR - 测试 test_comprehensive_insertion_fusion 失败
2025-06-29 00:23:52,691 - __main__ - INFO - === 测试综合控制叠加融合 ===
2025-06-29 00:23:52,691 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,691 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,691 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,691 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,691 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,691 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,692 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,692 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,692 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,692 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,692 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,692 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,692 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,692 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,693 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,693 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,693 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,693 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,693 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,693 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,693 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,693 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,693 - __main__ - ERROR - 综合控制叠加融合测试失败: 'FusionEngine' object has no attribute 'load_videos'
2025-06-29 00:23:52,693 - __main__ - ERROR - 测试 test_comprehensive_overlay_fusion 失败
2025-06-29 00:23:52,693 - __main__ - INFO - === 测试综合控制混合融合 ===
2025-06-29 00:23:52,693 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,693 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,694 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,694 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,694 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,694 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,694 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,694 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,694 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:23:52,694 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,694 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,695 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:23:52,695 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:23:52,695 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:23:52,695 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:23:52,695 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:23:52,695 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:23:52,695 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:23:52,695 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:23:52,695 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:23:52,695 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:23:52,695 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:23:52,695 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:23:52,695 - __main__ - ERROR - 综合控制混合融合测试失败: 'FusionEngine' object has no attribute 'load_videos'
2025-06-29 00:23:52,695 - __main__ - ERROR - 测试 test_comprehensive_blend_fusion 失败
2025-06-29 00:23:52,696 - __main__ - INFO - 综合测试完成: 0/5 通过
2025-06-29 00:23:52,696 - __main__ - ERROR - ❌ 部分综合测试失败
2025-06-29 00:26:02,886 - __main__ - INFO - 开始五维控制系统综合测试
2025-06-29 00:26:02,886 - __main__ - INFO - === 测试融合参数创建和序列化 ===
2025-06-29 00:26:02,886 - __main__ - ERROR - 融合参数创建和序列化测试失败: __init__() got an unexpected keyword argument 'resize_mode'
2025-06-29 00:26:02,886 - __main__ - ERROR - 测试 test_fusion_params_creation_and_serialization 失败
2025-06-29 00:26:02,887 - __main__ - INFO - === 测试融合引擎GUI集成 ===
2025-06-29 00:26:02,887 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,888 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,888 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,888 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,888 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,888 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,888 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,889 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,889 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,889 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,889 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,889 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,889 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,889 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,889 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,889 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,889 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,889 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,890 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,890 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,890 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,890 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,890 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,890 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,890 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,890 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,890 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,890 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,890 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,891 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,891 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,891 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,891 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,891 - __main__ - ERROR - 融合引擎GUI集成测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,891 - __main__ - ERROR - 测试 test_fusion_engine_gui_integration 失败
2025-06-29 00:26:02,891 - __main__ - INFO - === 测试综合控制插入融合 ===
2025-06-29 00:26:02,891 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,891 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,891 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,892 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,892 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,892 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,892 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,892 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,892 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,892 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,893 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,893 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,893 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,893 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,893 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,893 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,893 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,893 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,893 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,893 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,893 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,906 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,906 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:26:02,906 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,924 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,924 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=30, duration=1)
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=60, duration=1)
2025-06-29 00:26:02,924 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,924 - __main__ - ERROR - 综合控制插入融合测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,929 - __main__ - ERROR - 测试 test_comprehensive_insertion_fusion 失败
2025-06-29 00:26:02,929 - __main__ - INFO - === 测试综合控制叠加融合 ===
2025-06-29 00:26:02,929 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,929 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,929 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,930 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,930 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,930 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,930 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,930 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,930 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,930 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,930 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,930 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,930 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,930 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,930 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,931 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,931 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,931 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,931 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,931 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,931 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,931 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,931 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,931 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,931 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,931 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,931 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,931 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,932 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,940 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,940 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:26:02,940 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,954 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,954 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:26:02,954 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,954 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,954 - __main__ - ERROR - 综合控制叠加融合测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,957 - __main__ - ERROR - 测试 test_comprehensive_overlay_fusion 失败
2025-06-29 00:26:02,958 - __main__ - INFO - === 测试综合控制混合融合 ===
2025-06-29 00:26:02,958 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,958 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,958 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,958 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,959 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,959 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,959 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:26:02,959 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,959 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,959 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,959 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,959 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,959 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,959 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,959 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:26:02,960 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,960 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,960 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:26:02,960 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:26:02,960 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:26:02,960 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:26:02,960 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:26:02,960 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:26:02,960 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:26:02,960 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:26:02,960 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:26:02,960 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:26:02,960 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:26:02,960 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:26:02,970 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,970 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:26:02,970 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:26:02,984 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,984 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:26:02,984 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:26:02,984 - src.fusion.fusion_engine - ERROR - 从GUI参数设置融合参数失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,984 - __main__ - ERROR - 综合控制混合融合测试失败: __init__() got an unexpected keyword argument 'insertion_mode'
2025-06-29 00:26:02,987 - __main__ - ERROR - 测试 test_comprehensive_blend_fusion 失败
2025-06-29 00:26:02,988 - __main__ - INFO - 综合测试完成: 0/5 通过
2025-06-29 00:26:02,988 - __main__ - ERROR - ❌ 部分综合测试失败
2025-06-29 00:28:20,890 - __main__ - INFO - 开始五维控制系统综合测试
2025-06-29 00:28:20,890 - __main__ - INFO - === 测试融合参数创建和序列化 ===
2025-06-29 00:28:20,891 - __main__ - INFO - ✅ 融合参数序列化测试通过
2025-06-29 00:28:20,891 - __main__ - INFO - ✅ 融合参数反序列化测试通过
2025-06-29 00:28:20,891 - __main__ - INFO - === 测试融合引擎GUI集成 ===
2025-06-29 00:28:20,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,892 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,892 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,892 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,892 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,893 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,893 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,893 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:20,893 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,893 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,893 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,893 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,894 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,894 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,894 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,894 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:20,894 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,894 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,894 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,894 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,894 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,894 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,894 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,894 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,894 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:20,895 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:20,895 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:20,895 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:20,895 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:20,895 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:20,895 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.7, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 4, 'distribution_mode': 'random', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.2, 'maintain_aspect_ratio': False, 'scale_mode': 'crop'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.3, 'static_position_y': 0.7, 'motion_trajectory': 'vertical_scroll', 'motion_speed': 2.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': True, 'preprocessing_methods': ['edge_detection', 'histogram_equalization'], 'edge_detection_method': 'sobel', 'edge_low_threshold': 30, 'edge_high_threshold': 120, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.5, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': 'GUI测试文字', 'text_encoding': 'utf-8', 'text_position_mode': 'dynamic', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'circular', 'text_motion_speed': 0.8, 'font_family': 'Times New Roman', 'font_size': 32, 'font_color': [0, 255, 0], 'font_alpha': 1.0, 'font_bold': True, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 2, 'continuous_frames': 50, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:20,895 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:20,895 - __main__ - ERROR - 融合引擎GUI集成测试失败: 'SpatialSizeControl' object has no attribute 'resize_mode'
2025-06-29 00:28:20,896 - __main__ - ERROR - 测试 test_fusion_engine_gui_integration 失败
2025-06-29 00:28:20,896 - __main__ - INFO - === 测试综合控制插入融合 ===
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,896 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,896 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,896 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,896 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,896 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,896 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,896 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,896 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,896 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,897 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,897 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,897 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,897 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:20,897 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,897 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,897 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:20,897 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:20,897 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:20,897 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:20,897 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:20,897 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:20,897 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:20,897 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:20,898 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:20,898 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:20,898 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:20,909 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:20,910 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:20,910 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:20,927 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:20,927 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=30, duration=1)
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - 添加插入位置: Position(frame=60, duration=1)
2025-06-29 00:28:20,927 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.6, 'positions': [{'frame': 30, 'duration': 1}, {'frame': 60, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 2, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.2, 'static_position_y': 0.3, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': True, 'preprocessing_methods': ['gamma_correction'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.3, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': '综合测试', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.9, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 0], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 100, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:20,928 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:20,928 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:20,928 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 00:28:20,928 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 28, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 00:28:20,929 - src.utils.thread_pool - INFO - 块大小设置为: 28
2025-06-29 00:28:20,929 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 00:28:20,929 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 00:28:20,929 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 开始综合控制插入，插入位置数量: 2
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 图像处理控制: 启用预处理=True
2025-06-29 00:28:20,930 - src.fusion.insertion_fusion - INFO - 文字内容控制: 启用文字叠加=True
2025-06-29 00:28:27,654 - src.fusion.insertion_fusion - INFO - 空间控制直接插入完成，结果帧数: 1204
2025-06-29 00:28:27,655 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1204
2025-06-29 00:28:27,655 - src.effects.text_content_controller - INFO - 文字内容: '综合测试', 位置模式: static
2025-06-29 00:28:30,071 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1204 帧
2025-06-29 00:28:30,471 - src.fusion.insertion_fusion - INFO - 综合控制插入完成，结果帧数: 1204
2025-06-29 00:28:31,183 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 00:28:31,183 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 10.25秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 00:28:31,183 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 10.253596067428589, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 00:28:32,191 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 00:28:32,191 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 00:28:32,192 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:32,201 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 00:28:32,202 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 00:28:32,203 - __main__ - INFO - ✅ 综合控制插入融合测试通过，结果帧数: 1204
2025-06-29 00:28:33,181 - __main__ - INFO - === 测试综合控制叠加融合 ===
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,182 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,182 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,182 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:33,182 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:33,182 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:33,182 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:33,182 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,182 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,182 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,183 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:33,183 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:33,183 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:33,183 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:33,183 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,183 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,183 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:33,183 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:33,183 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:33,183 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:33,183 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:33,183 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:33,184 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:33,184 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:33,184 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:33,184 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:33,184 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:33,243 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:33,243 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:33,243 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:33,281 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:33,281 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:33,281 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:33,281 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 0.7, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'circular', 'motion_speed': 1.5, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': '叠加测试', 'text_encoding': 'utf-8', 'text_position_mode': 'dynamic', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 2.0, 'font_family': 'Arial', 'font_size': 20, 'font_color': [0, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:33,281 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:33,282 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:33,282 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 00:28:33,282 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 27, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 00:28:33,282 - src.utils.thread_pool - INFO - 块大小设置为: 27
2025-06-29 00:28:33,282 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 00:28:33,283 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 00:28:33,283 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 00:28:33,283 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.5
2025-06-29 00:28:33,283 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=circular, 运动速度=1.5
2025-06-29 00:28:52,651 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 00:28:53,133 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1202
2025-06-29 00:28:53,133 - src.effects.text_content_controller - INFO - 文字内容: '叠加测试', 位置模式: dynamic
2025-06-29 00:28:54,064 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1202 帧
2025-06-29 00:28:54,064 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 00:28:54,064 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 20.78秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 00:28:54,064 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 20.78084897994995, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 00:28:54,398 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 00:28:54,398 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 00:28:54,398 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:54,408 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 00:28:54,409 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 00:28:54,409 - __main__ - INFO - ✅ 综合控制叠加融合测试通过，结果帧数: 1202
2025-06-29 00:28:55,797 - __main__ - INFO - === 测试综合控制混合融合 ===
2025-06-29 00:28:55,797 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,797 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,797 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,797 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,797 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:55,798 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:55,798 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:55,798 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:55,798 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:28:55,798 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,798 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,798 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,798 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,798 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:55,798 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:55,798 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:55,798 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:55,798 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:28:55,798 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,798 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,799 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:28:55,799 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:28:55,799 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:28:55,799 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:28:55,799 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:28:55,799 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:28:55,799 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:28:55,799 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:28:55,799 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:28:55,799 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:28:55,799 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:28:55,799 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:28:55,810 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:55,810 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 00:28:55,811 - src.fusion.fusion_engine - INFO - A视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 00:28:55,848 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:55,849 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 00:28:55,849 - src.fusion.fusion_engine - INFO - B视频加载成功: videos/283533_medium.mp4
2025-06-29 00:28:55,849 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.4, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'diagonal', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': True, 'preprocessing_methods': ['edge_detection', 'gaussian_blur'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': '混合测试', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 26, 'font_color': [255, 0, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': True, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': True, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 00:28:55,849 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 00:28:55,849 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:28:55,850 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 00:28:55,850 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 27, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 00:28:55,850 - src.utils.thread_pool - INFO - 块大小设置为: 27
2025-06-29 00:28:55,850 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 00:28:55,851 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 00:28:55,851 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 00:28:55,851 - src.fusion.blend_fusion - INFO - 开始空间位置控制混合融合，模式: linear, 权重: 0.4
2025-06-29 00:28:55,851 - src.fusion.blend_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=diagonal, 运动速度=1.0
2025-06-29 00:29:24,353 - src.fusion.blend_fusion - INFO - 空间位置控制混合融合完成，结果帧数: 1202
2025-06-29 00:29:24,842 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1202
2025-06-29 00:29:24,842 - src.effects.text_content_controller - INFO - 文字内容: '混合测试', 位置模式: static
2025-06-29 00:29:25,713 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1202 帧
2025-06-29 00:29:25,713 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 00:29:25,713 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 29.86秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 00:29:25,713 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 29.862234115600586, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 00:29:26,013 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 00:29:26,014 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 00:29:26,014 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 00:29:26,024 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 00:29:26,024 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 00:29:26,024 - __main__ - INFO - ✅ 综合控制混合融合测试通过，结果帧数: 1202
2025-06-29 00:29:27,343 - __main__ - INFO - 综合测试完成: 4/5 通过
2025-06-29 00:29:27,343 - __main__ - ERROR - ❌ 部分综合测试失败
2025-06-29 00:32:49,186 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,186 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,187 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,187 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,187 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:32:49,187 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:32:49,187 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:32:49,187 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:32:49,187 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 00:32:49,188 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,188 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,188 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,188 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,188 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:32:49,188 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:32:49,188 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:32:49,188 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:32:49,188 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 00:32:49,188 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,188 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,189 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 00:32:49,189 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 00:32:49,189 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 00:32:49,189 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 00:32:49,189 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 00:32:49,189 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 00:32:49,189 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 00:32:49,189 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 00:32:49,189 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 00:32:49,189 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 00:32:49,190 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 00:32:49,190 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 00:32:49,475 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 00:32:49,476 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 00:32:49,476 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 00:32:49,476 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 00:32:49,492 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 00:32:49,493 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 00:32:49,493 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 07:49:29,051 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 07:49:51,088 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,088 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,089 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,089 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,089 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:49:51,089 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:49:51,089 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:49:51,089 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:49:51,089 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:49:51,090 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,090 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,090 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,090 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,090 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:49:51,090 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:49:51,090 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:49:51,090 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:49:51,090 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:49:51,091 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,091 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,091 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:49:51,091 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:49:51,091 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:49:51,091 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:49:51,091 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:49:51,091 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:49:51,091 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:49:51,091 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:49:51,091 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:49:51,092 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:49:51,092 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:49:51,092 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:49:51,391 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 07:49:51,391 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 07:49:51,392 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 07:49:51,392 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 07:49:51,405 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 07:49:51,405 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 07:49:51,405 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 07:52:17,400 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:52:17,400 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:52:17,401 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:52:17,401 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:52:17,401 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:52:17,401 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:52:19,913 - __main__ - INFO - 🚀 开始最终系统验证测试
2025-06-29 07:52:19,914 - __main__ - INFO - 
--- 执行测试: 应用程序启动 ---
2025-06-29 07:52:19,914 - __main__ - INFO - === 测试应用程序启动 ===
2025-06-29 07:52:20,001 - __main__ - INFO - ✅ 所有主要模块导入成功
2025-06-29 07:52:20,002 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,003 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,003 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,003 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,003 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,003 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,004 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,004 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,004 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:52:20,004 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,004 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,004 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,004 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,004 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,005 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,005 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,005 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,005 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:52:20,005 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,005 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,005 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,005 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,005 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,006 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,006 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,006 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,006 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:52:20,006 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:52:20,006 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:52:20,007 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:52:20,007 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:52:20,007 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:52:20,007 - __main__ - INFO - ✅ 融合引擎初始化成功
2025-06-29 07:52:20,007 - __main__ - INFO - ✅ 应用程序启动 测试通过
2025-06-29 07:52:20,008 - __main__ - INFO - 
--- 执行测试: 五维控制系统 ---
2025-06-29 07:52:20,008 - __main__ - INFO - === 测试五维控制系统 ===
2025-06-29 07:52:20,008 - __main__ - INFO - ✅ 五维控制系统模块导入成功
2025-06-29 07:52:20,008 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,008 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,008 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,008 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,008 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,008 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,008 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,008 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,008 - __main__ - INFO - ✅ 控制器初始化成功
2025-06-29 07:52:20,009 - __main__ - INFO - ✅ 五维控制参数创建成功
2025-06-29 07:52:20,009 - __main__ - INFO - ✅ 五维控制系统 测试通过
2025-06-29 07:52:20,009 - __main__ - INFO - 
--- 执行测试: 融合算法 ---
2025-06-29 07:52:20,009 - __main__ - INFO - === 测试融合算法 ===
2025-06-29 07:52:20,009 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,009 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,009 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,009 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,009 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,009 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,009 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,009 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,009 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:52:20,009 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,010 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,010 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,010 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,010 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:52:20,010 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:52:20,010 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:52:20,010 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:52:20,011 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:52:20,011 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:52:20,011 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:52:20,011 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:52:20,011 - __main__ - INFO - ✅ 所有融合算法初始化成功
2025-06-29 07:52:20,011 - __main__ - INFO - ✅ 五维控制集成验证成功
2025-06-29 07:52:20,011 - __main__ - INFO - ✅ 融合算法 测试通过
2025-06-29 07:52:20,011 - __main__ - INFO - 
--- 执行测试: GUI集成 ---
2025-06-29 07:52:20,011 - __main__ - INFO - === 测试GUI集成 ===
2025-06-29 07:53:15,601 - __main__ - INFO - 🚀 开始无头模式系统验证测试
2025-06-29 07:53:15,601 - __main__ - INFO - 
--- 执行测试: 核心模块 ---
2025-06-29 07:53:15,601 - __main__ - INFO - === 测试核心模块 ===
2025-06-29 07:53:15,918 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,919 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,919 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,919 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,919 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,919 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,920 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,920 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,920 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:53:15,920 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,920 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,920 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,921 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,921 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,921 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,921 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,921 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,921 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:53:15,921 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,921 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,921 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,922 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,922 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,922 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,922 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,922 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,922 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:53:15,922 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:53:15,922 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:53:15,923 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:53:15,923 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:53:15,923 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 融合引擎模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 五维控制参数模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 融合算法模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 控制器模块正常
2025-06-29 07:53:15,923 - __main__ - INFO - ✅ 核心模块 测试通过
2025-06-29 07:53:15,923 - __main__ - INFO - 
--- 执行测试: 视频处理 ---
2025-06-29 07:53:15,923 - __main__ - INFO - === 测试视频处理 ===
2025-06-29 07:53:15,941 - src.video.video_loader - INFO - 视频加载成功: videos/172681-849651720_tiny.mp4
2025-06-29 07:53:15,941 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 07:53:15,961 - src.video.video_loader - INFO - 视频加载成功: videos/283533_medium.mp4
2025-06-29 07:53:15,961 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 07:53:15,961 - __main__ - INFO - ✅ 视频加载功能正常
2025-06-29 07:53:15,963 - __main__ - INFO - ✅ 视频处理 测试通过
2025-06-29 07:53:15,963 - __main__ - INFO - 
--- 执行测试: 性能模块 ---
2025-06-29 07:53:15,963 - __main__ - INFO - === 测试性能模块 ===
2025-06-29 07:53:15,963 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:53:15,963 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:53:15,963 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:53:15,964 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 5
2025-06-29 07:53:15,964 - __main__ - INFO - ✅ 性能模块正常
2025-06-29 07:53:15,964 - __main__ - INFO - ✅ 性能模块 测试通过
2025-06-29 07:53:15,964 - __main__ - INFO - 
--- 执行测试: 五维控制集成 ---
2025-06-29 07:53:15,964 - __main__ - INFO - === 测试五维控制集成 ===
2025-06-29 07:53:15,964 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,964 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,964 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,964 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,965 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,965 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,965 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 07:53:15,965 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,965 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,965 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,965 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,965 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,965 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,965 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,966 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 07:53:15,966 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,966 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,966 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 07:53:15,966 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 07:53:15,966 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 07:53:15,966 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 07:53:15,966 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 07:53:15,966 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 07:53:15,966 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 07:53:15,966 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 07:53:15,966 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 07:53:15,966 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 07:53:15,967 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 07:53:15,967 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 07:53:15,967 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 3, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': True, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': 'static', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:15,967 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:15,967 - __main__ - INFO - ✅ 五维控制集成正常
2025-06-29 07:53:15,967 - __main__ - INFO - ✅ 五维控制集成 测试通过
2025-06-29 07:53:15,967 - __main__ - INFO - 
--- 执行测试: 文档完整性 ---
2025-06-29 07:53:15,967 - __main__ - INFO - === 测试文档完整性 ===
2025-06-29 07:53:15,967 - __main__ - INFO - ✅ 存在的文档: ['README.md', 'USER_GUIDE.md', 'prd.md', 'requirements.txt', 'activate_env.sh', 'run.sh']
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 文档完整性 测试通过
2025-06-29 07:53:15,968 - __main__ - INFO - 
🏁 系统验证测试完成: 5/5 通过
2025-06-29 07:53:15,968 - __main__ - INFO - 🎉 恭喜！系统验证测试全部通过！
2025-06-29 07:53:15,968 - __main__ - INFO - 📋 五维视频融合控制系统核心功能正常
2025-06-29 07:53:15,968 - __main__ - INFO - 🚀 系统已准备就绪
2025-06-29 07:53:15,968 - __main__ - INFO - 
============================================================
2025-06-29 07:53:15,968 - __main__ - INFO - 📊 五维视频融合控制系统 - 最终验证报告
2025-06-29 07:53:15,968 - __main__ - INFO - ============================================================
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 1️⃣ 时间维度控制 - 插入频次/时间控制
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 2️⃣ 空间尺寸控制 - 图像缩放/长宽比控制
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 3️⃣ 空间位置控制 - 静态/动态运动模式
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 4️⃣ 图像处理控制 - 预处理和混合方法控制
2025-06-29 07:53:15,968 - __main__ - INFO - ✅ 5️⃣ 文字内容控制 - 文字叠加位置/样式/时间控制
2025-06-29 07:53:15,968 - __main__ - INFO - ============================================================
2025-06-29 07:53:15,969 - __main__ - INFO - 🎯 项目状态: 完成 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - 🔧 核心功能: 正常 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - 📚 文档体系: 完整 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - 🧪 测试验证: 通过 ✅
2025-06-29 07:53:15,969 - __main__ - INFO - ============================================================
2025-06-29 07:53:35,599 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:35,600 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:35,600 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:35,600 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:35,600 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:35,600 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:37,582 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:37,583 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:37,583 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:37,583 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:37,583 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:37,584 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:39,818 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:39,818 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:39,818 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:41,515 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:41,515 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:41,515 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 07:53:41,516 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 07:53:41,516 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 07:53:41,516 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:01,638 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,638 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,638 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,638 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,639 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:06:01,639 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:06:01,639 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:06:01,639 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:06:01,639 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 08:06:01,640 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,640 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,640 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,640 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,640 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:06:01,640 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:06:01,640 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:06:01,640 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:06:01,640 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 08:06:01,640 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,641 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,641 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:06:01,641 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:06:01,641 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:06:01,641 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:06:01,641 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:06:01,641 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:06:01,641 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 08:06:01,642 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 08:06:01,642 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 08:06:01,642 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 08:06:01,642 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 08:06:01,642 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 08:06:02,024 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 08:06:02,024 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 08:06:02,024 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 08:06:02,025 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 08:06:02,041 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 08:06:02,041 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 08:06:02,042 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 08:06:02,448 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,448 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,448 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:02,449 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,449 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,449 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:02,551 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,551 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,551 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:02,551 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:02,552 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:02,552 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:07,219 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 08:06:17,239 - src.gui.main_window - INFO - 正在加载A视频: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,254 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,254 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:17,268 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,269 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:17,269 - src.fusion.fusion_engine - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:17,269 - src.gui.main_window - INFO - A视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/172681-849651720_tiny.mp4
2025-06-29 08:06:22,025 - src.gui.main_window - INFO - 正在加载B视频: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,055 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,055 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:22,080 - src.video.video_loader - INFO - 视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,081 - src.video.video_loader - INFO - 视频信息: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:22,081 - src.fusion.fusion_engine - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:22,081 - src.gui.main_window - INFO - B视频加载成功: /Users/<USER>/Documents/augment-projects/X/videos/283533_medium.mp4
2025-06-29 08:06:27,821 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:27,821 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 08:06:27,821 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:27,822 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:27,846 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 0
2025-06-29 08:06:27,846 - src.gui.main_window - WARNING - 预览生成失败：无预览帧
2025-06-29 08:06:29,398 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:06:34,923 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:34,923 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:34,923 - src.gui.main_window - INFO - 设置默认插入位置: 3个位置
2025-06-29 08:06:34,924 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:34,924 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'insertion', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:34,924 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:34,924 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:35,975 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:35,975 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 08:06:35,975 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:35,975 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:36,355 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-29 08:06:36,370 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-29 08:06:36,744 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:06:41,021 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:41,021 - src.fusion.insertion_fusion - INFO - 视频设置完成
2025-06-29 08:06:41,022 - src.fusion.insertion_fusion - INFO - 视频A: 视频信息:
  文件: 172681-849651720_tiny.mp4
  分辨率: 640x360
  帧率: 24.00 FPS
  帧数: 276
  时长: 11.50秒
  编码: h264
  大小: 1.09 MB
2025-06-29 08:06:41,022 - src.fusion.insertion_fusion - INFO - 视频B: 视频信息:
  文件: 283533_medium.mp4
  分辨率: 1280x720
  帧率: 30.00 FPS
  帧数: 1202
  时长: 40.07秒
  编码: h264
  大小: 15.20 MB
2025-06-29 08:06:41,377 - src.fusion.insertion_fusion - INFO - 生成插入预览，预览帧数: 3
2025-06-29 08:06:41,387 - src.gui.main_window - INFO - 预览生成完成，显示第300帧
2025-06-29 08:06:41,408 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:06:48,873 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-29 08:06:48,876 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:48,876 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:48,876 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:48,877 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:06:48,877 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:06:48,877 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:06:50,554 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:06:50,555 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:06:50,555 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:06:50,804 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:06:50,808 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:06:50,827 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:09:43,056 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:09:43,056 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:43,056 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:09:43,268 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:09:43,270 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:09:43,891 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:09:43,891 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:43,891 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:09:44,112 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:09:44,114 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:09:45,522 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:09:45,523 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:45,523 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:09:45,732 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:09:45,734 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:09:47,023 - src.gui.main_window - INFO - 开始融合处理...
2025-06-29 08:09:47,025 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 08:09:47,025 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 08:09:47,025 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 22, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 08:09:47,026 - src.utils.thread_pool - INFO - 块大小设置为: 22
2025-06-29 08:09:47,026 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 08:09:47,027 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 08:09:47,027 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:09:47,027 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.5
2025-06-29 08:09:47,027 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=horizontal_scroll, 运动速度=1.0
2025-06-29 08:10:20,442 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 08:10:21,092 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 08:10:21,092 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 34.07秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 08:10:21,092 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 34.06509613990784, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 08:10:21,115 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 08:10:21,115 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 08:10:21,116 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 08:10:21,125 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 08:10:21,126 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 08:10:21,126 - src.gui.main_window - INFO - 融合处理成功完成
2025-06-29 08:11:19,645 - src.gui.main_window - INFO - 正在导出视频到: /Users/<USER>/Documents/augment-projects/X/output/1.mp4
2025-06-29 08:11:20,583 - src.fusion.overlay_fusion - INFO - 已写入 100/1202 帧
2025-06-29 08:11:21,489 - src.fusion.overlay_fusion - INFO - 已写入 200/1202 帧
2025-06-29 08:11:22,485 - src.fusion.overlay_fusion - INFO - 已写入 300/1202 帧
2025-06-29 08:11:23,380 - src.fusion.overlay_fusion - INFO - 已写入 400/1202 帧
2025-06-29 08:11:24,233 - src.fusion.overlay_fusion - INFO - 已写入 500/1202 帧
2025-06-29 08:11:25,097 - src.fusion.overlay_fusion - INFO - 已写入 600/1202 帧
2025-06-29 08:11:25,927 - src.fusion.overlay_fusion - INFO - 已写入 700/1202 帧
2025-06-29 08:11:26,754 - src.fusion.overlay_fusion - INFO - 已写入 800/1202 帧
2025-06-29 08:11:27,610 - src.fusion.overlay_fusion - INFO - 已写入 900/1202 帧
2025-06-29 08:11:28,454 - src.fusion.overlay_fusion - INFO - 已写入 1000/1202 帧
2025-06-29 08:11:29,303 - src.fusion.overlay_fusion - INFO - 已写入 1100/1202 帧
2025-06-29 08:11:30,213 - src.fusion.overlay_fusion - INFO - 已写入 1200/1202 帧
2025-06-29 08:11:30,231 - src.fusion.overlay_fusion - INFO - 叠加融合视频保存完成: /Users/<USER>/Documents/augment-projects/X/output/1.mp4
2025-06-29 08:11:30,231 - src.gui.main_window - INFO - 视频导出成功: /Users/<USER>/Documents/augment-projects/X/output/1.mp4
2025-06-29 08:11:39,126 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:11:39,126 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:11:39,126 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:11:39,353 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:11:39,355 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:11:42,998 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:11:42,998 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:11:42,999 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:11:43,212 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:11:43,214 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:25,531 - src.gui.control_panel - INFO - 融合类型改变: 混合融合 (Blend)
2025-06-29 08:12:25,532 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:25,533 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:25,533 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:25,533 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:25,533 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:25,533 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:26,914 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:26,914 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:26,915 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:27,128 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:27,130 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:27,130 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:29,229 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:29,230 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:29,230 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:29,444 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:29,446 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:29,446 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:31,275 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:31,276 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:31,276 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:31,276 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'blend', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:31,276 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:31,276 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:32,227 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:32,228 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:32,228 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:32,442 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:32,443 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:32,444 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:34,463 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:34,464 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:34,464 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:34,677 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:34,679 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:34,679 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:35,596 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:35,596 - src.fusion.blend_fusion - INFO - 混合融合视频设置完成
2025-06-29 08:12:35,597 - src.fusion.blend_fusion - INFO - 生成混合预览，预览帧数: 3
2025-06-29 08:12:35,809 - src.fusion.blend_fusion - INFO - 混合预览生成完成，成功生成 3 帧
2025-06-29 08:12:35,810 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:35,810 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:38,749 - src.gui.control_panel - INFO - 融合类型改变: 叠加融合 (Overlay)
2025-06-29 08:12:38,749 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:38,749 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:38,750 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:38,750 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:38,750 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:38,750 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:39,700 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:39,701 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:39,701 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:39,929 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:39,930 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:39,931 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:45,515 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:45,516 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:45,516 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:45,516 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:12:45,516 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:12:45,516 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:12:46,566 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:46,566 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:46,566 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:46,799 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:46,800 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:46,800 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:12:54,711 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:54,711 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:54,711 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:54,932 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:54,934 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:12:56,676 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:12:56,676 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:12:56,676 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:12:56,895 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:12:56,897 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:00,350 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:00,350 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:00,350 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:01,301 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:01,301 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:01,302 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:01,519 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:01,521 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:01,521 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:03,499 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:03,500 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:03,500 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:03,500 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': [], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:03,500 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:03,500 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:04,551 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:04,551 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:04,552 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:04,771 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:04,773 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:04,773 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:08,597 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:08,597 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:08,597 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:08,598 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:08,598 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:08,598 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:09,606 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:09,607 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:09,607 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:09,821 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:09,823 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:09,823 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,710 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,710 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,711 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:18,938 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,939 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,939 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:18,939 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:18,939 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:18,939 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ces', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,042 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ces', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,042 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,042 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,593 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,593 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,593 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,593 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'ce', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,594 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,594 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,746 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,747 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,747 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,747 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'c', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,747 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,747 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,909 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,909 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,909 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:19,910 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': '', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:19,910 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:19,910 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:20,960 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:20,961 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:20,961 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:21,171 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:21,173 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:21,173 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:21,937 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 't', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:21,937 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:21,937 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:21,938 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 't', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:21,938 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:21,938 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,025 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'te', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,025 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,025 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,026 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'te', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,026 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,026 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,149 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'tes', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,149 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,149 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,150 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'tes', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,150 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,150 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,244 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,244 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,244 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,967 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,967 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,967 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:22,967 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': False, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:22,968 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:22,968 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:24,019 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:24,019 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:24,020 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:24,239 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:24,240 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:24,242 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:24,643 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:24,643 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:24,643 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:24,643 - src.fusion.fusion_engine - INFO - 融合参数已设置: {'fusion_type': 'overlay', 'insertion_mode': 'direct', 'resize_mode': 'fit', 'alpha': 0.5, 'positions': [{'frame': 300, 'duration': 1}, {'frame': 601, 'duration': 1}, {'frame': 901, 'duration': 1}], 'output_fps': 30.0, 'output_codec': 'mp4v', 'time_dimension': {'insertion_count': 5, 'distribution_mode': 'uniform', 'custom_time_points': [], 'time_bias_strength': 0.7}, 'spatial_size': {'align_to_main_video': True, 'scale_ratio': 1.0, 'maintain_aspect_ratio': True, 'scale_mode': 'proportional'}, 'spatial_position': {'is_static': False, 'static_position_x': 0.5, 'static_position_y': 0.5, 'motion_trajectory': 'horizontal_scroll', 'motion_speed': 1.0, 'motion_range_limit': [0.0, 0.0, 1.0, 1.0], 'custom_path_points': []}, 'image_processing': {'enable_preprocessing': False, 'preprocessing_methods': ['edge_detection'], 'edge_detection_method': 'canny', 'edge_low_threshold': 50, 'edge_high_threshold': 150, 'histogram_equalization': False, 'histogram_matching': False, 'gamma_correction': 1.0, 'fusion_method': 'alpha_blend', 'blend_weight': 0.5, 'feather_radius': 5}, 'text_content': {'enable_text_overlay': True, 'text_content': 'test ', 'text_encoding': 'utf-8', 'text_position_mode': '静态位置', 'static_position_x': 0.1, 'static_position_y': 0.1, 'text_motion_trajectory': 'horizontal_scroll', 'text_motion_speed': 1.0, 'font_family': 'Arial', 'font_size': 24, 'font_color': [255, 255, 255], 'font_alpha': 1.0, 'font_bold': False, 'font_italic': False, 'enable_outline': False, 'outline_color': [0, 0, 0], 'outline_width': 1, 'enable_shadow': False, 'shadow_offset': [2, 2], 'shadow_color': [0, 0, 0], 'appearance_frequency': 1, 'continuous_frames': 30, 'enable_fade_effect': False, 'fade_duration': 10}}
2025-06-29 08:13:24,644 - src.fusion.fusion_engine - INFO - 从GUI参数设置融合参数完成
2025-06-29 08:13:24,644 - src.gui.main_window - INFO - 融合参数已更新
2025-06-29 08:13:25,606 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:25,606 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:25,607 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:25,826 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:25,827 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:25,827 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:32,545 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:32,545 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:32,545 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:32,762 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:32,764 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:32,764 - src.gui.control_panel - INFO - 生成融合预览
2025-06-29 08:13:35,295 - src.gui.main_window - INFO - 正在生成预览...
2025-06-29 08:13:35,295 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:35,295 - src.fusion.overlay_fusion - INFO - 生成叠加预览，预览帧数: 3
2025-06-29 08:13:35,510 - src.fusion.overlay_fusion - INFO - 叠加预览生成完成，成功生成 3 帧
2025-06-29 08:13:35,512 - src.gui.main_window - INFO - 预览生成完成，显示第0帧
2025-06-29 08:13:36,660 - src.gui.main_window - INFO - 开始融合处理...
2025-06-29 08:13:36,661 - src.utils.memory_manager - INFO - 大视频优化设置: {'cache_size_mb': 1000, 'chunk_size': 10, 'enable_streaming': False, 'reduce_quality': False}
2025-06-29 08:13:36,661 - src.utils.memory_manager - INFO - 最优处理参数: {'use_threading': True, 'max_threads': 4, 'chunk_size': 23, 'enable_caching': True, 'streaming_mode': False}
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 线程池管理器初始化完成，最大工作线程数: 4
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 视频处理线程池初始化完成
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 线程池已启动，工作线程数: 4
2025-06-29 08:13:36,661 - src.utils.thread_pool - INFO - 块大小设置为: 23
2025-06-29 08:13:36,661 - src.fusion.fusion_engine - INFO - 开始执行融合
2025-06-29 08:13:36,662 - src.utils.performance_monitor - INFO - 开始性能监控，间隔: 1.0秒
2025-06-29 08:13:36,662 - src.fusion.overlay_fusion - INFO - 叠加融合视频设置完成
2025-06-29 08:13:36,662 - src.fusion.overlay_fusion - INFO - 开始空间位置控制叠加融合，模式: normal, 透明度: 0.5
2025-06-29 08:13:36,662 - src.fusion.overlay_fusion - INFO - 空间位置控制参数: 静态=False, 运动轨迹=horizontal_scroll, 运动速度=1.0
2025-06-29 08:14:09,263 - src.fusion.overlay_fusion - INFO - 空间位置控制叠加融合完成，结果帧数: 1202
2025-06-29 08:14:10,076 - src.effects.text_content_controller - INFO - 开始应用文字叠加控制，帧数: 1202
2025-06-29 08:14:10,076 - src.effects.text_content_controller - INFO - 文字内容: 'test ', 位置模式: 静态位置
2025-06-29 08:14:10,910 - src.effects.text_content_controller - INFO - 文字叠加控制应用完成，处理了 1202 帧
2025-06-29 08:14:10,910 - src.fusion.fusion_engine - INFO - 融合执行完成
2025-06-29 08:14:10,910 - src.utils.performance_monitor - INFO - 处理完成 - 耗时: 34.25秒, 帧数: 0, 平均FPS: 0.00
2025-06-29 08:14:10,910 - src.fusion.fusion_engine - INFO - 性能统计: {'elapsed_time': 34.24817085266113, 'frame_count': 0, 'average_fps': 0.0}
2025-06-29 08:14:11,076 - src.utils.performance_monitor - INFO - 性能监控已停止
2025-06-29 08:14:11,077 - src.utils.thread_pool - INFO - 线程池已关闭
2025-06-29 08:14:11,077 - src.utils.memory_manager - INFO - 缓存已清空
2025-06-29 08:14:11,087 - src.utils.memory_manager - INFO - 强制垃圾回收完成，回收对象: 0
2025-06-29 08:14:11,088 - src.utils.memory_manager - INFO - 内存清理完成，释放: 0.00MB
2025-06-29 08:14:11,090 - src.gui.main_window - INFO - 融合处理成功完成
2025-06-29 08:14:33,569 - src.gui.main_window - INFO - 正在导出视频到: /Users/<USER>/Documents/augment-projects/X/output/2.mp4
2025-06-29 08:14:34,494 - src.fusion.overlay_fusion - INFO - 已写入 100/1202 帧
2025-06-29 08:14:35,419 - src.fusion.overlay_fusion - INFO - 已写入 200/1202 帧
2025-06-29 08:14:36,339 - src.fusion.overlay_fusion - INFO - 已写入 300/1202 帧
2025-06-29 08:14:37,234 - src.fusion.overlay_fusion - INFO - 已写入 400/1202 帧
2025-06-29 08:14:38,107 - src.fusion.overlay_fusion - INFO - 已写入 500/1202 帧
2025-06-29 08:14:38,961 - src.fusion.overlay_fusion - INFO - 已写入 600/1202 帧
2025-06-29 08:14:39,814 - src.fusion.overlay_fusion - INFO - 已写入 700/1202 帧
2025-06-29 08:14:40,708 - src.fusion.overlay_fusion - INFO - 已写入 800/1202 帧
2025-06-29 08:14:41,690 - src.fusion.overlay_fusion - INFO - 已写入 900/1202 帧
2025-06-29 08:14:42,559 - src.fusion.overlay_fusion - INFO - 已写入 1000/1202 帧
2025-06-29 08:14:43,432 - src.fusion.overlay_fusion - INFO - 已写入 1100/1202 帧
2025-06-29 08:14:44,388 - src.fusion.overlay_fusion - INFO - 已写入 1200/1202 帧
2025-06-29 08:14:44,410 - src.fusion.overlay_fusion - INFO - 叠加融合视频保存完成: /Users/<USER>/Documents/augment-projects/X/output/2.mp4
2025-06-29 08:14:44,410 - src.gui.main_window - INFO - 视频导出成功: /Users/<USER>/Documents/augment-projects/X/output/2.mp4
2025-06-29 08:15:37,527 - src.gui.main_window - INFO - 应用程序退出
2025-06-29 08:18:42,719 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,720 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,720 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,720 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,720 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:18:42,720 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:18:42,721 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:18:42,721 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:18:42,721 - src.fusion.insertion_fusion - INFO - 插入融合模块初始化完成
2025-06-29 08:18:42,721 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,721 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,721 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,722 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,722 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:18:42,722 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:18:42,722 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:18:42,722 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:18:42,722 - src.fusion.overlay_fusion - INFO - 叠加融合模块初始化完成
2025-06-29 08:18:42,722 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,722 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,722 - src.effects.edge_detector - INFO - 边缘检测器初始化完成
2025-06-29 08:18:42,722 - src.effects.histogram_matcher - INFO - 直方图匹配器初始化完成
2025-06-29 08:18:42,723 - src.effects.image_processor - INFO - 图像处理器初始化完成
2025-06-29 08:18:42,723 - src.effects.image_processing_controller - INFO - 图像处理控制器初始化完成
2025-06-29 08:18:42,723 - src.effects.text_overlay - INFO - 文字叠加功能初始化完成
2025-06-29 08:18:42,723 - src.effects.text_content_controller - INFO - 文字内容控制器初始化完成
2025-06-29 08:18:42,723 - src.fusion.blend_fusion - INFO - 混合融合模块初始化完成
2025-06-29 08:18:42,723 - src.utils.performance_monitor - INFO - 性能监控器初始化完成
2025-06-29 08:18:42,723 - src.utils.performance_monitor - INFO - 性能优化器初始化完成
2025-06-29 08:18:42,724 - src.utils.memory_manager - INFO - 内存缓存初始化，最大大小: 500MB
2025-06-29 08:18:42,724 - src.utils.memory_manager - INFO - 内存管理器初始化完成
2025-06-29 08:18:42,724 - src.fusion.fusion_engine - INFO - 融合引擎初始化完成
2025-06-29 08:18:43,083 - src.gui.main_window - INFO - 主窗口初始化完成
2025-06-29 08:18:43,083 - __main__ - INFO - 视频融合编辑器启动
2025-06-29 08:18:43,083 - __main__ - INFO - Python版本: 3.9.23 (main, Jun  5 2025, 08:23:30) 
[Clang 14.0.6 ]
2025-06-29 08:18:43,083 - __main__ - INFO - PyQt5版本: 5.15.10
2025-06-29 08:18:43,100 - src.gui.main_window - INFO - 🎉 欢迎使用视频融合编辑器！
2025-06-29 08:18:43,101 - src.gui.main_window - INFO - 💡 提示: 可以拖拽视频文件到窗口中快速加载
2025-06-29 08:18:43,101 - src.gui.main_window - INFO - 📖 使用帮助: 菜单栏 -> 帮助 -> 用户手册
2025-06-29 08:18:51,548 - src.gui.main_window - INFO - 应用程序退出
