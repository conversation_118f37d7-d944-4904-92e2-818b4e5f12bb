#!/usr/bin/env python3
"""
视频播放控制功能单元测试
Video Player Controls Unit Test
"""

import sys
import os
import unittest
from unittest.mock import Mock, patch, MagicMock

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 模拟PyQt5模块
class MockQWidget:
    def __init__(self, *args, **kwargs):
        pass
    def setAlignment(self, *args): pass
    def setMinimumHeight(self, *args): pass
    def setStyleSheet(self, *args): pass
    def setText(self, *args): pass
    def setPixmap(self, *args): pass
    def size(self): return Mock(width=lambda: 640, height=lambda: 480)
    def clear(self): pass

class MockQSlider:
    def __init__(self, *args, **kwargs):
        self.value_val = 0
        self.min_val = 0
        self.max_val = 100
        self.enabled = True
    def setMinimum(self, val): self.min_val = val
    def setMaximum(self, val): self.max_val = val
    def setValue(self, val): self.value_val = val
    def value(self): return self.value_val
    def setEnabled(self, enabled): self.enabled = enabled
    def sliderPressed(self): return Mock()
    def sliderReleased(self): return Mock()
    def valueChanged(self): return Mock()

class MockQPushButton:
    def __init__(self, text=""):
        self.text_val = text
        self.enabled = True
    def setText(self, text): self.text_val = text
    def text(self): return self.text_val
    def setEnabled(self, enabled): self.enabled = enabled
    def clicked(self): return Mock()

class MockQTimer:
    def __init__(self):
        self.timeout = Mock()
        self.running = False
    def start(self, interval): self.running = True
    def stop(self): self.running = False
    def isActive(self): return self.running

# 模拟PyQt5模块
sys.modules['PyQt5'] = Mock()
sys.modules['PyQt5.QtWidgets'] = Mock()
sys.modules['PyQt5.QtCore'] = Mock()
sys.modules['PyQt5.QtGui'] = Mock()

# 设置模拟类
mock_qt = sys.modules['PyQt5.QtWidgets']
mock_qt.QWidget = MockQWidget
mock_qt.QVBoxLayout = Mock
mock_qt.QHBoxLayout = Mock
mock_qt.QGroupBox = Mock
mock_qt.QLabel = MockQWidget
mock_qt.QPushButton = MockQPushButton
mock_qt.QSlider = MockQSlider
mock_qt.QFileDialog = Mock()

mock_core = sys.modules['PyQt5.QtCore']
mock_core.Qt = Mock()
mock_core.Qt.AlignCenter = 0x84
mock_core.Qt.Horizontal = 0x1
mock_core.Qt.KeepAspectRatio = 0x1
mock_core.Qt.SmoothTransformation = 0x1
mock_core.pyqtSignal = Mock(return_value=Mock())
mock_core.QThread = Mock
mock_core.QTimer = MockQTimer
mock_core.pyqtSlot = lambda *args, **kwargs: lambda func: func

mock_gui = sys.modules['PyQt5.QtGui']
mock_gui.QPixmap = Mock()
mock_gui.QImage = Mock()

# 模拟其他依赖
sys.modules['cv2'] = Mock()
sys.modules['numpy'] = Mock()

# 现在导入我们的模块
from src.gui.video_player import VideoPlayer, VideoPlaybackThread
from src.video.video_loader import VideoLoader


class TestVideoPlayer(unittest.TestCase):
    """视频播放器测试类"""
    
    def setUp(self):
        """测试设置"""
        # 模拟VideoLoader
        self.mock_video_loader = Mock(spec=VideoLoader)
        self.mock_video_info = Mock()
        self.mock_video_info.is_valid = True
        self.mock_video_info.frame_count = 100
        self.mock_video_info.fps = 30.0
        self.mock_video_info.width = 640
        self.mock_video_info.height = 480
        self.mock_video_info.duration = 3.33
        
        # 创建视频播放器实例
        with patch('src.gui.video_player.VideoLoader', return_value=self.mock_video_loader):
            self.player = VideoPlayer("测试播放器")
    
    def test_video_player_initialization(self):
        """测试视频播放器初始化"""
        self.assertEqual(self.player.title, "测试播放器")
        self.assertFalse(self.player.is_playing)
        self.assertEqual(self.player.current_frame, 0)
        self.assertEqual(self.player.total_frames, 0)
        self.assertIsNone(self.player.video_path)
    
    def test_load_video_success(self):
        """测试成功加载视频"""
        # 设置模拟返回值
        self.mock_video_loader.load_video.return_value = self.mock_video_info
        self.mock_video_loader.get_frame.return_value = Mock()  # 模拟帧数据
        
        # 测试加载视频
        result = self.player.load_video("/test/video.mp4")
        
        self.assertTrue(result)
        self.assertEqual(self.player.video_path, "/test/video.mp4")
        self.assertEqual(self.player.total_frames, 100)
        self.mock_video_loader.load_video.assert_called_once_with("/test/video.mp4")
    
    def test_load_video_failure(self):
        """测试加载视频失败"""
        # 设置模拟返回值为失败
        self.mock_video_loader.load_video.return_value = None
        
        # 测试加载视频
        result = self.player.load_video("/test/invalid.mp4")
        
        self.assertFalse(result)
        self.assertIsNone(self.player.video_path)
        self.assertEqual(self.player.total_frames, 0)
    
    def test_play_video_without_loading(self):
        """测试在未加载视频时播放"""
        self.player.play_video()
        
        # 应该不会开始播放
        self.assertFalse(self.player.is_playing)
    
    def test_play_video_with_loaded_video(self):
        """测试在已加载视频时播放"""
        # 先加载视频
        self.mock_video_loader.load_video.return_value = self.mock_video_info
        self.mock_video_loader.get_frame.return_value = Mock()
        self.player.load_video("/test/video.mp4")
        
        # 测试播放
        self.player.play_video()
        
        self.assertTrue(self.player.is_playing)
    
    def test_pause_video(self):
        """测试暂停视频"""
        # 先设置为播放状态
        self.player.is_playing = True
        
        # 测试暂停
        self.player.pause_video()
        
        self.assertFalse(self.player.is_playing)
    
    def test_stop_video(self):
        """测试停止视频"""
        # 设置播放状态
        self.player.is_playing = True
        self.player.current_frame = 50
        
        # 测试停止
        self.player.stop_video()
        
        self.assertFalse(self.player.is_playing)
        self.assertEqual(self.player.current_frame, 0)
    
    def test_seek_to_frame(self):
        """测试跳转到指定帧"""
        # 先加载视频
        self.mock_video_loader.load_video.return_value = self.mock_video_info
        self.mock_video_loader.get_frame.return_value = Mock()
        self.player.load_video("/test/video.mp4")
        
        # 测试跳转
        self.player.seek_to_frame(25)
        
        self.assertEqual(self.player.current_frame, 25)
    
    def test_seek_to_frame_boundary(self):
        """测试跳转到边界帧"""
        # 先加载视频
        self.mock_video_loader.load_video.return_value = self.mock_video_info
        self.mock_video_loader.get_frame.return_value = Mock()
        self.player.load_video("/test/video.mp4")
        
        # 测试跳转到负数帧（应该被限制为0）
        self.player.seek_to_frame(-10)
        self.assertEqual(self.player.current_frame, 0)
        
        # 测试跳转到超出范围的帧（应该被限制为最大帧数-1）
        self.player.seek_to_frame(200)
        self.assertEqual(self.player.current_frame, 99)  # total_frames - 1
    
    def test_toggle_play(self):
        """测试切换播放状态"""
        # 先加载视频
        self.mock_video_loader.load_video.return_value = self.mock_video_info
        self.mock_video_loader.get_frame.return_value = Mock()
        self.player.load_video("/test/video.mp4")
        
        # 初始状态应该是暂停
        self.assertFalse(self.player.is_playing)
        
        # 第一次切换应该开始播放
        self.player.toggle_play()
        self.assertTrue(self.player.is_playing)
        
        # 第二次切换应该暂停
        self.player.toggle_play()
        self.assertFalse(self.player.is_playing)


class TestVideoPlaybackThread(unittest.TestCase):
    """视频播放线程测试类"""
    
    def setUp(self):
        """测试设置"""
        self.mock_video_loader = Mock(spec=VideoLoader)
        self.mock_video_info = Mock()
        self.mock_video_info.frame_count = 100
        self.mock_video_info.fps = 30.0
        
        self.mock_video_loader.is_loaded.return_value = True
        self.mock_video_loader.get_current_info.return_value = self.mock_video_info
        
        self.thread = VideoPlaybackThread(self.mock_video_loader)
    
    def test_thread_initialization(self):
        """测试线程初始化"""
        self.assertEqual(self.thread.current_frame, 0)
        self.assertFalse(self.thread.is_playing)
        self.assertFalse(self.thread.should_stop)
        self.assertEqual(self.thread.fps, 30.0)
    
    def test_set_position(self):
        """测试设置播放位置"""
        self.thread.set_position(25)
        self.assertEqual(self.thread.current_frame, 25)
    
    def test_set_playing(self):
        """测试设置播放状态"""
        self.thread.set_playing(True)
        self.assertTrue(self.thread.is_playing)
        
        self.thread.set_playing(False)
        self.assertFalse(self.thread.is_playing)
    
    def test_stop(self):
        """测试停止线程"""
        self.thread.stop()
        self.assertTrue(self.thread.should_stop)
        self.assertFalse(self.thread.is_playing)


def run_tests():
    """运行测试"""
    print("开始视频播放控制功能测试...")
    
    # 创建测试套件
    suite = unittest.TestSuite()
    
    # 添加测试用例
    suite.addTest(unittest.makeSuite(TestVideoPlayer))
    suite.addTest(unittest.makeSuite(TestVideoPlaybackThread))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print("\n✅ 所有视频播放控制功能测试通过！")
        return True
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
        return False


if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
