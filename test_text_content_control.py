#!/usr/bin/env python3
"""
测试文字内容控制功能
Test Text Content Control
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.insertion_fusion import InsertionFusion, InsertionPosition
from src.fusion.fusion_engine import TextContentControl, MotionTrajectory
from src.effects.text_content_controller import TextContentController
from src.video.video_loader import VideoLoader
from src.utils.logger import Logger
import numpy as np
import cv2

def test_text_content_control_creation():
    """测试文字内容控制参数创建"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试文字内容控制参数创建 ===")
    
    try:
        # 测试默认参数
        text_control = TextContentControl()
        assert text_control.enable_text_overlay == False
        assert text_control.text_content == ""
        assert text_control.text_position_mode == "static"
        assert text_control.font_family == "Arial"
        logger.info("✅ 默认参数创建成功")
        
        # 测试自定义参数
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="测试文字",
            text_position_mode="dynamic",
            text_motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL,
            text_motion_speed=2.0,
            font_family="Times New Roman",
            font_size=32,
            font_color=(255, 0, 0),
            font_bold=True,
            enable_outline=True,
            outline_color=(0, 0, 0),
            outline_width=2
        )
        assert text_control.enable_text_overlay == True
        assert text_control.text_content == "测试文字"
        assert text_control.text_motion_trajectory == MotionTrajectory.HORIZONTAL_SCROLL
        assert text_control.font_size == 32
        assert text_control.font_bold == True
        logger.info("✅ 自定义参数创建成功")
        
        # 测试序列化
        data = text_control.to_dict()
        new_text_control = TextContentControl.from_dict(data)
        assert new_text_control.enable_text_overlay == True
        assert new_text_control.text_content == "测试文字"
        assert new_text_control.font_size == 32
        logger.info("✅ 参数序列化测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"文字内容控制参数创建测试失败: {e}")
        return False

def test_text_content_controller():
    """测试文字内容控制器"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试文字内容控制器 ===")
    
    try:
        controller = TextContentController()
        
        # 创建测试帧
        test_frames = []
        for i in range(10):
            frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
            test_frames.append(frame)
        
        # 测试不启用文字叠加
        text_control = TextContentControl(enable_text_overlay=False)
        result = controller.apply_text_overlay_to_frames(test_frames, text_control)
        assert len(result) == len(test_frames)
        logger.info("✅ 不启用文字叠加测试通过")
        
        # 测试静态文字叠加
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="静态文字",
            text_position_mode="static",
            static_position_x=0.5,
            static_position_y=0.5,
            appearance_frequency=1,
            continuous_frames=10
        )
        result = controller.apply_text_overlay_to_frames(test_frames, text_control)
        assert len(result) == len(test_frames)
        logger.info("✅ 静态文字叠加测试通过")
        
        # 测试动态文字叠加
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="动态文字",
            text_position_mode="dynamic",
            text_motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL,
            text_motion_speed=1.0,
            appearance_frequency=1,
            continuous_frames=10
        )
        result = controller.apply_text_overlay_to_frames(test_frames, text_control)
        assert len(result) == len(test_frames)
        logger.info("✅ 动态文字叠加测试通过")
        
        # 测试单帧文字叠加
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="单帧文字",
            font_size=24,
            font_color=(0, 255, 0)
        )
        result_frame = controller.apply_text_overlay_to_single_frame(
            test_frames[0], text_control, 0, 1)
        assert result_frame.shape == test_frames[0].shape
        logger.info("✅ 单帧文字叠加测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"文字内容控制器测试失败: {e}")
        return False

def test_text_styles_and_effects():
    """测试文字样式和效果"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试文字样式和效果 ===")
    
    try:
        controller = TextContentController()
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # 测试不同字体
        fonts = controller.get_available_fonts()
        for font in fonts:
            text_control = TextContentControl(
                enable_text_overlay=True,
                text_content=f"字体测试 {font}",
                font_family=font,
                font_size=20
            )
            result = controller.apply_text_overlay_to_single_frame(test_frame, text_control)
            assert result.shape == test_frame.shape
        logger.info(f"✅ 字体测试通过，测试了 {len(fonts)} 种字体")
        
        # 测试描边效果
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="描边文字",
            enable_outline=True,
            outline_color=(0, 0, 0),
            outline_width=3
        )
        result = controller.apply_text_overlay_to_single_frame(test_frame, text_control)
        assert result.shape == test_frame.shape
        logger.info("✅ 描边效果测试通过")
        
        # 测试阴影效果
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="阴影文字",
            enable_shadow=True,
            shadow_offset=(3, 3),
            shadow_color=(128, 128, 128)
        )
        result = controller.apply_text_overlay_to_single_frame(test_frame, text_control)
        assert result.shape == test_frame.shape
        logger.info("✅ 阴影效果测试通过")
        
        # 测试淡入淡出效果
        test_frames = [test_frame.copy() for _ in range(20)]
        text_control = TextContentControl(
            enable_text_overlay=True,
            text_content="淡入淡出文字",
            enable_fade_effect=True,
            fade_duration=5,
            appearance_frequency=1,
            continuous_frames=20
        )
        result = controller.apply_text_overlay_to_frames(test_frames, text_control)
        assert len(result) == len(test_frames)
        logger.info("✅ 淡入淡出效果测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"文字样式和效果测试失败: {e}")
        return False

def test_motion_trajectories():
    """测试运动轨迹"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试运动轨迹 ===")
    
    try:
        controller = TextContentController()
        test_frames = [np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8) for _ in range(10)]
        
        # 测试所有可用轨迹
        trajectories = controller.get_available_trajectories()
        for trajectory in trajectories:
            if trajectory != "static":  # 跳过静态轨迹
                text_control = TextContentControl(
                    enable_text_overlay=True,
                    text_content=f"轨迹: {trajectory}",
                    text_position_mode="dynamic",
                    text_motion_trajectory=MotionTrajectory(trajectory),
                    text_motion_speed=1.0,
                    appearance_frequency=1,
                    continuous_frames=10
                )
                result = controller.apply_text_overlay_to_frames(test_frames, text_control)
                assert len(result) == len(test_frames)
                logger.info(f"✅ 轨迹 {trajectory} 测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"运动轨迹测试失败: {e}")
        return False

def test_insertion_fusion_with_text_control():
    """测试插入融合的文字内容控制"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试插入融合文字内容控制 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建插入融合器
        insertion_fusion = InsertionFusion()
        insertion_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 测试不同的文字内容控制参数
        test_cases = [
            {
                "name": "静态文字",
                "text_control": TextContentControl(
                    enable_text_overlay=True,
                    text_content="静态测试文字",
                    text_position_mode="static",
                    static_position_x=0.1,
                    static_position_y=0.1,
                    font_size=24,
                    font_color=(255, 255, 0),
                    appearance_frequency=1,
                    continuous_frames=50
                )
            },
            {
                "name": "滚动文字",
                "text_control": TextContentControl(
                    enable_text_overlay=True,
                    text_content="滚动测试文字",
                    text_position_mode="dynamic",
                    text_motion_trajectory=MotionTrajectory.HORIZONTAL_SCROLL,
                    text_motion_speed=1.0,
                    font_size=20,
                    font_color=(0, 255, 255),
                    appearance_frequency=1,
                    continuous_frames=50
                )
            },
            {
                "name": "带效果文字",
                "text_control": TextContentControl(
                    enable_text_overlay=True,
                    text_content="效果测试文字",
                    font_size=28,
                    font_color=(255, 0, 255),
                    font_bold=True,
                    enable_outline=True,
                    outline_color=(0, 0, 0),
                    outline_width=2,
                    enable_shadow=True,
                    shadow_offset=(2, 2),
                    shadow_color=(128, 128, 128),
                    appearance_frequency=1,
                    continuous_frames=50
                )
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"测试: {test_case['name']}")
            
            # 创建插入位置
            positions = [InsertionPosition(30, 1)]
            
            # 执行综合控制插入
            result_frames = insertion_fusion.comprehensive_controlled_insertion(
                positions, None, None, test_case['text_control'], "direct"
            )
            
            # 验证结果
            assert len(result_frames) > 0
            logger.info(f"✅ {test_case['name']} 测试通过，结果帧数: {len(result_frames)}")
        
        return True
        
    except Exception as e:
        logger.error(f"插入融合文字内容控制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始测试文字内容控制功能")
    
    tests = [
        test_text_content_control_creation,
        test_text_content_controller,
        test_text_styles_and_effects,
        test_motion_trajectories,
        test_insertion_fusion_with_text_control
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！文字内容控制功能工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
