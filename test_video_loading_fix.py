#!/usr/bin/env python3
"""
测试视频加载修复
Test Video Loading Fix
"""

import sys
import os
import tempfile
import cv2
import numpy as np

# 添加src目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def create_test_video(file_path: str, duration_seconds: int = 3, fps: int = 30):
    """创建测试视频文件"""
    try:
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(file_path, fourcc, fps, (640, 480))
        
        total_frames = duration_seconds * fps
        
        for i in range(total_frames):
            # 创建彩色帧
            frame = np.zeros((480, 640, 3), dtype=np.uint8)
            
            # 根据帧号改变颜色
            color_intensity = int((i / total_frames) * 255)
            frame[:, :] = [color_intensity, 100, 255 - color_intensity]
            
            # 添加帧号文本
            cv2.putText(frame, f"Frame {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        print(f"✅ 测试视频创建成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 测试视频创建失败: {e}")
        return False

def test_video_load_validator():
    """测试视频加载验证器"""
    try:
        print("测试视频加载验证器...")
        
        from src.utils.video_utils import VideoLoadValidator
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        # 创建测试视频
        if not create_test_video(test_video_path):
            return False
        
        try:
            # 测试有效视频路径
            result = VideoLoadValidator.validate_video_path(test_video_path)
            print(f"有效视频路径验证结果: {result}")
            
            # 测试视频打开
            result = VideoLoadValidator.test_video_open(test_video_path)
            print(f"视频打开测试结果: {result}")
            
            # 测试无效路径
            result = VideoLoadValidator.validate_video_path("/nonexistent/video.mp4")
            print(f"无效路径验证结果: {result}")
            
            print("✅ 视频加载验证器测试通过")
            return True
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ 视频加载验证器测试失败: {e}")
        return False

def test_video_player_loading():
    """测试VideoPlayer加载功能"""
    try:
        print("测试VideoPlayer加载功能...")
        
        # 模拟PyQt5环境
        import sys
        from unittest.mock import Mock, MagicMock
        
        # 模拟PyQt5模块
        mock_qt_widgets = Mock()
        mock_qt_core = Mock()
        mock_qt_gui = Mock()
        
        # 设置模拟类
        mock_qt_widgets.QWidget = Mock
        mock_qt_widgets.QVBoxLayout = Mock
        mock_qt_widgets.QHBoxLayout = Mock
        mock_qt_widgets.QGroupBox = Mock
        mock_qt_widgets.QLabel = Mock
        mock_qt_widgets.QPushButton = Mock
        mock_qt_widgets.QSlider = Mock
        mock_qt_widgets.QFileDialog = Mock()
        
        mock_qt_core.Qt = Mock()
        mock_qt_core.pyqtSignal = Mock(return_value=Mock())
        mock_qt_core.QThread = Mock
        mock_qt_core.QTimer = Mock
        mock_qt_core.pyqtSlot = lambda *args, **kwargs: lambda func: func
        
        mock_qt_gui.QPixmap = Mock()
        mock_qt_gui.QImage = Mock()
        
        sys.modules['PyQt5'] = Mock()
        sys.modules['PyQt5.QtWidgets'] = mock_qt_widgets
        sys.modules['PyQt5.QtCore'] = mock_qt_core
        sys.modules['PyQt5.QtGui'] = mock_qt_gui
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        if not create_test_video(test_video_path):
            return False
        
        try:
            # 导入VideoPlayer
            from src.gui.video_player import VideoPlayer
            
            # 创建VideoPlayer实例
            player = VideoPlayer("测试播放器")
            
            # 测试加载视频
            result = player.load_video(test_video_path)
            print(f"VideoPlayer加载测试视频结果: {result}")
            
            if result:
                print(f"视频路径: {player.get_video_path()}")
                print(f"总帧数: {player.get_total_frames()}")
                print(f"当前帧: {player.get_current_frame()}")
                print(f"是否已加载: {player.is_video_loaded()}")
            
            print("✅ VideoPlayer加载功能测试通过")
            return True
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ VideoPlayer加载功能测试失败: {e}")
        return False

def test_video_loader_compatibility():
    """测试VideoLoader兼容性"""
    try:
        print("测试VideoLoader兼容性...")
        
        from src.video.video_loader import VideoLoader
        
        # 创建临时测试视频
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            test_video_path = tmp_file.name
        
        if not create_test_video(test_video_path):
            return False
        
        try:
            # 创建VideoLoader实例
            loader = VideoLoader()
            
            # 测试加载视频
            video_info = loader.load_video(test_video_path)
            
            if video_info:
                print(f"VideoLoader加载成功:")
                print(f"  - 宽度: {video_info.width}")
                print(f"  - 高度: {video_info.height}")
                print(f"  - 帧数: {video_info.frame_count}")
                print(f"  - 帧率: {video_info.fps}")
                print(f"  - 时长: {video_info.duration:.2f}秒")
                print(f"  - 是否有效: {video_info.is_valid}")
                
                # 测试获取帧
                frame = loader.get_frame(0)
                if frame is not None:
                    print(f"  - 第一帧形状: {frame.shape}")
                else:
                    print("  - 无法获取第一帧")
                
                print("✅ VideoLoader兼容性测试通过")
                return True
            else:
                print("❌ VideoLoader加载失败")
                return False
            
        finally:
            # 清理测试文件
            try:
                os.unlink(test_video_path)
            except:
                pass
        
    except Exception as e:
        print(f"❌ VideoLoader兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 视频加载修复测试 ===\n")
    
    tests = [
        ("视频加载验证器测试", test_video_load_validator),
        ("VideoLoader兼容性测试", test_video_loader_compatibility),
        ("VideoPlayer加载功能测试", test_video_player_loading)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过\n")
            else:
                print(f"❌ {test_name} 失败\n")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}\n")
    
    print("=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 视频加载修复测试全部通过！")
        print("现在应该可以正常加载视频并进行融合了。")
        return True
    else:
        print("⚠️  部分测试失败，可能仍有问题需要解决。")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
