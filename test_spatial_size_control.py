#!/usr/bin/env python3
"""
测试空间尺寸控制算法
Test Spatial Size Control Algorithm
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.fusion.insertion_fusion import InsertionFusion, InsertionPosition
from src.fusion.overlay_fusion import OverlayFusion, OverlayPosition
from src.fusion.blend_fusion import BlendFusion
from src.fusion.fusion_engine import SpatialSizeControl, ScaleMode
from src.video.video_loader import VideoLoader
from src.utils.logger import Logger
import numpy as np
import cv2

def test_spatial_size_control_creation():
    """测试空间尺寸控制参数创建"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试空间尺寸控制参数创建 ===")
    
    try:
        # 测试默认参数
        spatial_control = SpatialSizeControl()
        assert spatial_control.align_to_main_video == True
        assert spatial_control.scale_ratio == 1.0
        assert spatial_control.maintain_aspect_ratio == True
        assert spatial_control.scale_mode == ScaleMode.PROPORTIONAL
        logger.info("✅ 默认参数创建成功")
        
        # 测试自定义参数
        spatial_control = SpatialSizeControl(
            align_to_main_video=True,
            scale_ratio=0.8,
            maintain_aspect_ratio=False,
            scale_mode=ScaleMode.STRETCH
        )
        assert spatial_control.scale_ratio == 0.8
        assert spatial_control.maintain_aspect_ratio == False
        assert spatial_control.scale_mode == ScaleMode.STRETCH
        logger.info("✅ 自定义参数创建成功")
        
        # 测试序列化
        data = spatial_control.to_dict()
        new_spatial_control = SpatialSizeControl.from_dict(data)
        assert new_spatial_control.scale_ratio == 0.8
        assert new_spatial_control.maintain_aspect_ratio == False
        assert new_spatial_control.scale_mode == ScaleMode.STRETCH
        logger.info("✅ 参数序列化测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"空间尺寸控制参数创建测试失败: {e}")
        return False

def test_insertion_fusion_spatial_control():
    """测试插入融合的空间尺寸控制"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试插入融合空间尺寸控制 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过实际视频测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建插入融合器
        insertion_fusion = InsertionFusion()
        insertion_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 测试不同的空间尺寸控制参数
        test_cases = [
            {
                "name": "等比例缩放50%",
                "spatial_control": SpatialSizeControl(
                    scale_ratio=0.5,
                    scale_mode=ScaleMode.PROPORTIONAL,
                    maintain_aspect_ratio=True
                )
            },
            {
                "name": "拉伸缩放80%",
                "spatial_control": SpatialSizeControl(
                    scale_ratio=0.8,
                    scale_mode=ScaleMode.STRETCH,
                    maintain_aspect_ratio=False
                )
            },
            {
                "name": "裁剪缩放120%",
                "spatial_control": SpatialSizeControl(
                    scale_ratio=1.2,
                    scale_mode=ScaleMode.CROP,
                    maintain_aspect_ratio=True
                )
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"测试: {test_case['name']}")
            
            # 创建插入位置
            positions = [InsertionPosition(100, 1), InsertionPosition(200, 1)]
            
            # 执行空间控制插入
            result_frames = insertion_fusion.spatial_controlled_insertion(
                positions, test_case['spatial_control'], "direct"
            )
            
            # 验证结果
            assert len(result_frames) > 0
            logger.info(f"✅ {test_case['name']} 测试通过，结果帧数: {len(result_frames)}")
        
        return True
        
    except Exception as e:
        logger.error(f"插入融合空间尺寸控制测试失败: {e}")
        return False

def test_frame_resize_with_spatial_control():
    """测试帧尺寸调整功能"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试帧尺寸调整功能 ===")
    
    try:
        # 创建测试帧
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        target_size = (1280, 720)  # width, height
        
        insertion_fusion = InsertionFusion()
        
        # 测试不同的空间控制参数
        test_cases = [
            {
                "name": "等比例缩放",
                "spatial_control": SpatialSizeControl(
                    scale_ratio=0.5,
                    scale_mode=ScaleMode.PROPORTIONAL,
                    maintain_aspect_ratio=True
                ),
                "expected_behavior": "保持长宽比，缩放50%"
            },
            {
                "name": "拉伸缩放",
                "spatial_control": SpatialSizeControl(
                    scale_ratio=0.8,
                    scale_mode=ScaleMode.STRETCH,
                    maintain_aspect_ratio=False
                ),
                "expected_behavior": "直接拉伸到80%尺寸"
            },
            {
                "name": "不对齐主视频",
                "spatial_control": SpatialSizeControl(
                    align_to_main_video=False,
                    scale_ratio=0.5
                ),
                "expected_behavior": "返回原始帧"
            }
        ]
        
        for test_case in test_cases:
            logger.info(f"测试: {test_case['name']}")
            
            resized_frame = insertion_fusion._resize_frame_with_spatial_control(
                test_frame, target_size, test_case['spatial_control']
            )
            
            # 验证结果
            assert resized_frame is not None
            assert len(resized_frame.shape) == 3
            
            if test_case['spatial_control'].align_to_main_video:
                # 如果对齐主视频，结果应该是目标尺寸
                assert resized_frame.shape[:2] == (target_size[1], target_size[0])  # height, width
            else:
                # 如果不对齐，应该返回原始帧
                assert resized_frame.shape == test_frame.shape
            
            logger.info(f"✅ {test_case['name']} 测试通过")
            logger.info(f"   原始尺寸: {test_frame.shape}")
            logger.info(f"   结果尺寸: {resized_frame.shape}")
            logger.info(f"   预期行为: {test_case['expected_behavior']}")
        
        return True
        
    except Exception as e:
        logger.error(f"帧尺寸调整功能测试失败: {e}")
        return False

def test_overlay_fusion_spatial_control():
    """测试叠加融合的空间尺寸控制"""
    logger = Logger.get_logger(__name__)
    logger.info("=== 测试叠加融合空间尺寸控制 ===")
    
    try:
        # 检查测试视频文件是否存在
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.warning("测试视频文件不存在，跳过叠加融合测试")
            return True
        
        # 加载视频
        video_a_loader = VideoLoader()
        video_b_loader = VideoLoader()
        
        if not video_a_loader.load_video(video_a_path):
            logger.error("加载A视频失败")
            return False
        
        if not video_b_loader.load_video(video_b_path):
            logger.error("加载B视频失败")
            return False
        
        # 创建叠加融合器
        overlay_fusion = OverlayFusion()
        overlay_fusion.set_videos(video_a_loader, video_b_loader)
        
        # 创建叠加位置
        position = OverlayPosition(x=100, y=100, width=300, height=200)
        
        # 测试空间控制叠加融合
        spatial_control = SpatialSizeControl(
            scale_ratio=0.6,
            scale_mode=ScaleMode.PROPORTIONAL,
            maintain_aspect_ratio=True
        )
        
        result_frames = overlay_fusion.spatial_controlled_overlay_fusion(
            position, spatial_control, alpha=0.7, frame_range=(0, 10)
        )
        
        # 验证结果
        assert len(result_frames) == 10
        logger.info("✅ 叠加融合空间尺寸控制测试通过")
        
        return True
        
    except Exception as e:
        logger.error(f"叠加融合空间尺寸控制测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger = Logger.get_logger(__name__)
    logger.info("开始测试空间尺寸控制算法")
    
    tests = [
        test_spatial_size_control_creation,
        test_frame_resize_with_spatial_control,
        test_insertion_fusion_spatial_control,
        test_overlay_fusion_spatial_control
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        else:
            logger.error(f"测试 {test.__name__} 失败")
    
    logger.info(f"测试完成: {passed}/{total} 通过")
    
    if passed == total:
        logger.info("🎉 所有测试通过！空间尺寸控制算法工作正常")
        return True
    else:
        logger.error("❌ 部分测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
