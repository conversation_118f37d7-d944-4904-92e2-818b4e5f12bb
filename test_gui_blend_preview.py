#!/usr/bin/env python3
"""
测试GUI中混合融合预览功能
模拟用户在界面中使用混合融合预览的完整流程
"""

import sys
import os
import time
import logging
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_gui_blend_preview():
    """测试GUI中的混合融合预览功能"""
    logger.info("=== 测试GUI中的混合融合预览功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        from src.fusion.fusion_engine import FusionType
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 测试视频路径
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        # 检查视频文件是否存在
        if not os.path.exists(video_a_path) or not os.path.exists(video_b_path):
            logger.error("测试视频文件不存在")
            return False
        
        # 1. 加载视频
        logger.info("1. 加载测试视频...")
        if not main_window.fusion_engine.load_video_a(video_a_path):
            logger.error("A视频加载失败")
            return False
        
        if not main_window.fusion_engine.load_video_b(video_b_path):
            logger.error("B视频加载失败")
            return False
        
        logger.info("✅ 视频加载成功")
        
        # 2. 设置混合融合类型
        logger.info("2. 设置混合融合类型...")
        main_window.control_panel.fusion_type_combo.setCurrentText("混合融合 (Blend)")

        # 等待信号处理完成
        app.processEvents()
        
        # 验证融合类型设置
        current_params = main_window.fusion_engine.fusion_params
        if current_params.fusion_type == FusionType.BLEND:
            logger.info("✅ 混合融合类型设置成功")
        else:
            logger.error(f"❌ 融合类型设置失败，当前类型: {current_params.fusion_type}")
            return False
        
        # 3. 生成预览
        logger.info("3. 生成混合融合预览...")
        main_window.generate_preview()
        
        # 检查预览是否生成成功
        preview_frames = main_window.fusion_engine.get_fusion_preview(max_frames=3)
        
        if preview_frames:
            logger.info(f"✅ 混合融合预览生成成功，预览帧数: {len(preview_frames)}")
            
            # 保存预览图像
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)
            
            import cv2
            for i, (frame_index, frame) in enumerate(preview_frames):
                preview_path = output_dir / f"gui_blend_test_preview_{i}_frame_{frame_index}.jpg"
                cv2.imwrite(str(preview_path), frame)
                logger.info(f"GUI混合预览图像已保存: {preview_path}")
            
            # 4. 测试预览窗口显示
            logger.info("4. 测试预览窗口显示...")
            if main_window.preview_window:
                # 显示第一个预览帧
                first_frame = preview_frames[0][1]
                main_window.preview_window.display_preview(first_frame)
                main_window.preview_window.add_info(f"混合融合预览 - 帧数: {len(preview_frames)}")
                logger.info("✅ 预览窗口显示成功")
            else:
                logger.warning("预览窗口未创建")
            
            return True
        else:
            logger.error("❌ 混合融合预览生成失败")
            return False
            
    except Exception as e:
        logger.error(f"GUI混合融合预览测试失败: {e}")
        return False

def test_all_fusion_types_gui():
    """测试GUI中所有融合类型的预览功能"""
    logger.info("=== 测试GUI中所有融合类型预览功能 ===")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from src.gui.main_window import MainWindow
        from src.fusion.fusion_engine import FusionType
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        
        # 测试视频路径
        video_a_path = "videos/172681-849651720_tiny.mp4"
        video_b_path = "videos/283533_medium.mp4"
        
        # 加载视频
        main_window.fusion_engine.load_video_a(video_a_path)
        main_window.fusion_engine.load_video_b(video_b_path)
        
        # 测试所有融合类型
        fusion_types = [
            ("插入融合 (Insertion)", FusionType.INSERTION),
            ("混合融合 (Blend)", FusionType.BLEND),
            ("叠加融合 (Overlay)", FusionType.OVERLAY)
        ]
        
        success_count = 0
        
        for type_name, fusion_type in fusion_types:
            try:
                logger.info(f"测试 {type_name}...")
                
                # 设置融合类型
                main_window.control_panel.fusion_type_combo.setCurrentText(type_name)
                app.processEvents()  # 等待信号处理完成
                
                # 为插入融合设置位置
                if fusion_type == FusionType.INSERTION:
                    # 设置一些插入位置
                    main_window.control_panel.position_list.clear()
                    main_window.control_panel.add_position(100, 1)
                    main_window.control_panel.add_position(200, 1)
                
                # 生成预览
                main_window.generate_preview()
                
                # 检查预览结果
                preview_frames = main_window.fusion_engine.get_fusion_preview(max_frames=2)
                
                if preview_frames:
                    logger.info(f"✅ {type_name} 预览成功，帧数: {len(preview_frames)}")
                    success_count += 1
                else:
                    logger.error(f"❌ {type_name} 预览失败")
                    
            except Exception as e:
                logger.error(f"❌ {type_name} 预览异常: {e}")
        
        logger.info(f"GUI预览测试完成: {success_count}/{len(fusion_types)} 成功")
        return success_count == len(fusion_types)
        
    except Exception as e:
        logger.error(f"GUI所有融合类型预览测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始GUI混合融合预览测试")
    
    # 检查当前工作目录
    current_dir = os.getcwd()
    logger.info(f"当前工作目录: {current_dir}")
    
    # 测试列表
    tests = [
        ("GUI混合融合预览", test_gui_blend_preview),
        ("GUI所有融合类型预览", test_all_fusion_types_gui),
    ]
    
    # 执行测试
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"执行测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            if test_func():
                logger.info(f"✅ {test_name} - 通过")
                passed += 1
            else:
                logger.error(f"❌ {test_name} - 失败")
        except Exception as e:
            logger.error(f"❌ {test_name} - 异常: {e}")
    
    # 输出测试结果
    logger.info(f"\n{'='*50}")
    logger.info(f"测试完成: {passed}/{total} 通过")
    logger.info(f"{'='*50}")
    
    if passed == total:
        logger.info("🎉 所有测试通过！GUI中混合融合和叠加融合预览功能完全正常")
        logger.info("📋 功能验证:")
        logger.info("  ✅ 混合融合预览：GUI中可以正常生成和显示")
        logger.info("  ✅ 叠加融合预览：GUI中可以正常生成和显示")
        logger.info("  ✅ 插入融合预览：继续正常工作")
        logger.info("  ✅ 预览窗口集成：所有融合类型都能在预览窗口中显示")
        logger.info("  ✅ 用户体验：不再有'暂不支持'的提示")
        return True
    else:
        logger.error(f"⚠️  有 {total - passed} 个测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
